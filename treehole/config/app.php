<?php
// +----------------------------------------------------------------------
// | 应用设置（自动环境检测）
// +----------------------------------------------------------------------

use app\util\SecretUtil;

// 根据环境设置配置
$isLocal = SecretUtil::isLocal();

return [
    // 应用地址
    'app_host'         => env('APP_HOST', ''),

    // 应用的命名空间
    'app_namespace'    => '',

    // 是否启用路由
    'with_route'       => true,

    // 默认应用
    'default_app'      => 'index',

    // 默认时区
    'default_timezone' => 'Asia/Shanghai',

    // 应用映射（自动多应用模式有效）
    'app_map'          => [],

    // 域名绑定（自动多应用模式有效）
    'domain_bind'      => [],

    // 禁止URL访问的应用列表
    'deny_app_list'    => $isLocal ? [] : ['common', 'runtime', 'vendor', 'config'],

    // 是否开启调试模式
    'app_debug'        => $isLocal ? true : false,

    // 异常页面的模板文件路径
    'exception_tmpl'   => app()->getThinkPath() . 'tpl/think_exception.tpl',

    // 错误信息显示
    'error_message'    => $isLocal ? '开发环境错误信息' : '页面错误！请稍后再试～',

    // 是否显示详细错误信息
    'show_error_msg'   => $isLocal ? true : false,

    // 默认过滤方法（防止 XSS 等）
    'default_filter'   => $isLocal ? '' : 'htmlspecialchars',

    // 默认输出类型
    'default_return_type' => 'json',

    // 开发环境特殊配置
    'app_trace'        => $isLocal ? true : false,  // 页面Trace
    'log_level'        => $isLocal ? ['error', 'warning', 'info', 'debug'] : ['error'], // 日志级别

    // 其他自定义配置
    'crypto_key'       => $isLocal ? 'DEV-BUAA-SSO-KEY-2024' : 'PROD-BUAA-SSO-KEY-2024',
];
