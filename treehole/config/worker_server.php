<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// +----------------------------------------------------------------------
// | Workerman设置 仅对 php think worker:server 指令有效
// +----------------------------------------------------------------------
return [
    // 扩展自身需要的配置
    'protocol'       => 'websocket', // 协议 支持 tcp udp unix http websocket text
    'host'           => '0.0.0.0', // 监听地址
    'port'           => 2345, // 监听端口
    'socket'         => '', // 完整监听地址
    'context'        => [], // socket 上下文选项
    'worker_class'   => \app\websocket\WebSocketServer::class, // 使用我们自定义的WebSocket服务器类

    // 支持workerman的所有配置参数
    'name'           => 'TreeholeWebSocket',
    'count'          => 1, // 进程数
    'daemonize'      => false,
    'pidFile'        => runtime_path() . 'worker.pid',

    // 支持事件回调
    'onWorkerStart'  => function($worker) {
        // 初始化数据库连接
        think\facade\Db::connect();
    },
    'onWorkerReload' => function($worker) {
        // 重新加载时重置数据库连接
        think\facade\Db::reconnect();
    },
    'onConnect'      => null,
    'onMessage'      => null,
    'onClose'        => null,
    'onError'        => null,
];
