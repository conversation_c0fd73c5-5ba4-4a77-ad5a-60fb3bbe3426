<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// +----------------------------------------------------------------------
// | Workerman设置 仅对 php think worker 指令有效
// +----------------------------------------------------------------------
return [
    'server' => [
        'handler' => \app\websocket\WebSocketServer::class,
        'listen'  => 'websocket://0.0.0.0:2345',
        'count'   => 4,
        'reusePort' => true,
        'heartbeat_interval' => 10,
        'heartbeat_timeout' => 30,
    ],
    'name'      => 'thinkphp',
    'daemonize' => false,
    'pidFile'   => '',
];