<?php
// +----------------------------------------------------------------------
// | 微信公众号配置
// +----------------------------------------------------------------------

use app\util\SecretUtil;

$wechatConfig = SecretUtil::getWechatOfficial('main');

return [
    // 公众号配置
    'official_account' => [
        'app_id'  => $wechatConfig['appid'] ?? '', // 公众号AppID
        'secret'  => $wechatConfig['secret'] ?? '', // 公众号AppSecret
        'token'   => '', // 公众号Token
        'aes_key' => '', // 公众号EncodingAESKey
    ],
    
    // 草稿箱配置
    'draft' => [
        'cover_image' => 'public/uploads/cover.jpg', // 封面图片路径，相对于应用根目录
        'message_limit' => 10, // 每次获取的消息数量
        'author' => '树洞', // 文章作者
        'title_template' => '树洞最新消息汇总 - {date}', // 标题模板，{date}会被替换为当前日期
        'digest' => '今日树洞最新消息汇总', // 文章摘要
    ],
]; 