<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
return [
    // 指令定义
    'commands' => [
    	'draft:generate' => 'app\command\DraftGenerate',
    	'send' => 'app\command\Send',
    	'tas' => '\app\command\Tas',
    	'draft-task' => 'app\command\DraftTask',
    	'log:cleanup' => 'app\command\LogCleanup',
    	'security:scan' => 'app\command\SecurityScan',
    	'security:clean' => 'app\command\SecurityClean',
    	'security:monitor' => 'app\command\SecurityMonitor',
    	'cache:monitor' => 'app\command\CacheMonitor',
    	'report:weekly' => 'app\command\WeeklyReport',
    ],
];

