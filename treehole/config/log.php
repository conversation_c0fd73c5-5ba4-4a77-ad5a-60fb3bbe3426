<?php
// +----------------------------------------------------------------------
// | 日志设置（分类管理优化）
// +----------------------------------------------------------------------

use app\util\SecretUtil;

// 根据环境设置日志级别
$isLocal = SecretUtil::isLocal();

return [
    // 默认日志记录通道
    'default'      => 'file',

    // 记录哪些级别的日志（开发环境记录所有，生产环境只记录重要信息）
    'level'        => $isLocal ? ['error', 'warning', 'info', 'debug'] : ['error', 'warning', 'info'],

    // 日志类型记录的通道 - 按功能分类
    'type_channel' => [
        'error'   => 'error',
        'warning' => 'warning',
        'info'    => 'info',
        'debug'   => $isLocal ? 'debug' : 'file',
    ],

    // 关闭全局日志写入（false 表示开启）
    'close'        => false,

    // 全局日志处理 支持闭包
    'processor'    => null,

    // 日志通道列表
    'channels'     => [
        // 默认文件日志
        'file' => [
            'type'           => 'File',
            'path'           => '',
            'single'         => false,  // 改为按日期分文件
            'apart_level'    => [],
            'max_files'      => 30,
            'json'           => false,
            'processor'      => null,
            'close'          => false,
            'format'         => '[%s][%s] %s',
            'realtime_write' => true,  // 实时写入
        ],

        // 错误日志 - 单独文件（永久保存）
        'error' => [
            'type'           => 'File',
            'path'           => '',
            'single'         => true,
            'file_name'      => 'error',
            'apart_level'    => ['error'],
            'max_files'      => 0,  // 0表示不限制文件数量，永久保存
            'json'           => false,
            'format'         => '[%s][%s] %s',
            'realtime_write' => true,
        ],

        // 警告日志 - 单独文件（永久保存）
        'warning' => [
            'type'           => 'File',
            'path'           => '',
            'single'         => true,
            'file_name'      => 'warning',
            'apart_level'    => ['warning'],
            'max_files'      => 0,  // 永久保存
            'json'           => false,
            'format'         => '[%s][%s] %s',
            'realtime_write' => true,
        ],

        // 信息日志 - 按模块分类（永久保存）
        'info' => [
            'type'           => 'File',
            'path'           => '',
            'single'         => true,
            'file_name'      => 'info',
            'apart_level'    => ['info'],
            'max_files'      => 0,  // 永久保存
            'json'           => false,
            'format'         => '[%s][%s] %s',
            'realtime_write' => true,
        ],

        // 调试日志 - 仅开发环境（永久保存）
        'debug' => [
            'type'           => 'File',
            'path'           => '',
            'single'         => true,
            'file_name'      => 'debug',
            'apart_level'    => ['debug'],
            'max_files'      => 0,  // 永久保存
            'json'           => false,
            'format'         => '[%s][%s] %s',
            'realtime_write' => true,
        ],

        // API日志 - 专门记录API调用（永久保存）
        'api' => [
            'type'           => 'File',
            'path'           => '',
            'single'         => true,
            'file_name'      => 'api',
            'max_files'      => 0,  // 永久保存
            'json'           => true,  // API日志使用JSON格式便于分析
            'format'         => '[%s][%s] %s',
            'realtime_write' => true,
        ],

        // 业务日志 - 记录重要业务操作（永久保存）
        'business' => [
            'type'           => 'File',
            'path'           => '',
            'single'         => true,
            'file_name'      => 'business',
            'max_files'      => 0,  // 永久保存
            'json'           => false,
            'format'         => '[%s][%s] %s',
            'realtime_write' => true,
        ],
    ],
];
