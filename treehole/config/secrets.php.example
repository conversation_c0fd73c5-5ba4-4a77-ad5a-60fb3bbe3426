<?php
// +----------------------------------------------------------------------
// | 密钥统一管理配置文件模板
// | 复制此文件为 secrets.php 并填入真实的密钥信息
// +----------------------------------------------------------------------

use app\util\SecretUtil;

// 根据环境设置配置
$isLocal = SecretUtil::isLocal();

return [
    // 微信小程序配置
    'wechat_miniprogram' => [
        'main' => [
            'appid' => $isLocal ? 'your_dev_miniprogram_appid' : 'your_prod_miniprogram_appid',
            'secret' => $isLocal ? 'your_dev_miniprogram_secret' : 'your_prod_miniprogram_secret',
        ],
        'banxuexing' => [
            'appid' => $isLocal ? 'your_dev_banxuexing_appid' : 'your_prod_banxuexing_appid',
            'secret' => $isLocal ? 'your_dev_banxuexing_secret' : 'your_prod_banxuexing_secret',
        ]
    ],

    // 微信公众号配置
    'wechat_official' => [
        'main' => [
            'appid' => $isLocal ? 'your_dev_official_appid' : 'your_prod_official_appid',
            'secret' => $isLocal ? 'your_dev_official_secret' : 'your_prod_official_secret',
        ],
        'template' => [
            'appid' => $isLocal ? 'your_dev_template_appid' : 'your_prod_template_appid',
            'secret' => $isLocal ? 'your_dev_template_secret' : 'your_prod_template_secret',
        ]
    ],

    // 腾讯云COS配置
    'tencent_cos' => [
        'region' => 'ap-beijing',
        'secret_id' => $isLocal ? 'your_dev_cos_secret_id' : 'your_prod_cos_secret_id',
        'secret_key' => $isLocal ? 'your_dev_cos_secret_key' : 'your_prod_cos_secret_key',
    ],

    // 加密密钥配置
    'encryption' => [
        'jwt_key' => $isLocal ? 'your_dev_jwt_key' : 'your_prod_jwt_key',
        'sso_key' => $isLocal ? 'your_dev_sso_key' : 'your_prod_sso_key',
        'crypto_key' => $isLocal ? 'your_dev_crypto_key' : 'your_prod_crypto_key',
    ],

    // 数据库配置
    'database' => [
        'hostname' => $isLocal ? 'localhost' : 'your_tencent_cloud_db_host',
        'database' => $isLocal ? 'treehole' : 'your_tencent_cloud_db_name',
        'username' => $isLocal ? 'root' : 'your_tencent_cloud_db_user',
        'password' => $isLocal ? 'your_local_db_password' : 'your_tencent_cloud_db_password',
        'hostport' => $isLocal ? '8889' : '3306',
    ],

    // Redis配置
    'redis' => [
        'host' => $isLocal ? '127.0.0.1' : 'your_tencent_cloud_redis_host',
        'port' => $isLocal ? 6379 : 6379, // 腾讯云Redis端口，通常也是6379
        'password' => $isLocal ? '' : 'your_tencent_cloud_redis_password',
        'database' => $isLocal ? 1 : 0, // 本地用数据库1，云端用数据库0
    ],

    // API域名配置
    'api' => [
        'base_url' => $isLocal ? 'http://localhost:80/index.php' : 'https://your-domain.com',
        'wangz' => $isLocal ? 'http://localhost:80/index.php' : 'https://your-domain.com',
    ],
];
