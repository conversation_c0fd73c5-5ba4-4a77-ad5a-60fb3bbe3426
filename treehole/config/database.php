<?php

// 检查是否为本地环境
$host = $_SERVER['HTTP_HOST'] ?? '';
$isLocal = strpos($host, 'localhost') !== false ||
           strpos($host, '127.0.0.1') !== false ||
           strpos($host, '192.168.') !== false;

// 直接读取secrets.php配置文件
$secretsPath = __DIR__ . '/secrets.php';
if (!file_exists($secretsPath)) {
    throw new \Exception('配置文件 config/secrets.php 不存在，请复制 config/secrets.php.example 并重命名为 config/secrets.php，然后填入正确的配置信息');
}

$secrets = include $secretsPath;
if (!isset($secrets['database'])) {
    throw new \Exception('config/secrets.php 中缺少 database 配置项');
}

$dbConfig = $secrets['database'];

// 验证必需的配置项
$required = ['hostname', 'database', 'username', 'password', 'hostport'];
foreach ($required as $key) {
    if (!isset($dbConfig[$key])) {
        throw new \Exception("数据库配置缺少必需项: {$key}，请检查 config/secrets.php");
    }
}

return [
    // 默认使用的数据库连接配置
    'default'         => env('database.driver', 'mysql'),

    // 自定义时间查询规则
    'time_query_rule' => [],

    // 自动写入时间戳字段
    // true为自动识别类型 false关闭
    // 字符串则明确指定时间字段类型 支持 int timestamp datetime date
    'auto_timestamp'  => true,

    // 时间字段取出后的默认时间格式
    'datetime_format' => 'Y-m-d H:i:s',

    // 时间字段配置 配置格式：create_time,update_time
    'datetime_field'  => '',

    // 数据库连接配置
    'connections'     => [
        'mysql' => [
            'type'            => 'mysql',
            'hostname'        => $dbConfig['hostname'],
            'database'        => $dbConfig['database'],
            'username'        => $dbConfig['username'],
            'password'        => $dbConfig['password'],
            'hostport'        => $dbConfig['hostport'],
            'params'          => [],
            'charset'         => 'utf8mb4',
            'prefix'          => '',
            'deploy'          => 0,
            'rw_separate'     => false,
            'master_num'      => 1,
            'slave_no'        => '',
            'fields_strict'   => false,
            'break_reconnect' => true,
            'trigger_sql'     => $isLocal, // 开发环境开启SQL监听
            'fields_cache'    => !$isLocal, // 生产环境开启字段缓存
            // 连接池配置
            'pool' => [
                'min' => $isLocal ? 2 : 5,
                'max' => $isLocal ? 20 : 100,
                'timeout' => $isLocal ? 60 : 300,
                'heartbeat' => $isLocal ? 30 : 60,
            ],
            'options'   => [
                PDO::ATTR_PERSISTENT => !$isLocal, // 生产环境使用持久连接
                PDO::ATTR_TIMEOUT => $isLocal ? 10 : 30,
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::MYSQL_ATTR_INIT_COMMAND => $isLocal ?
                    'SET NAMES utf8mb4' :
                    'SET NAMES utf8mb4 COLLATE utf8mb4_0900_ai_ci',
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                PDO::MYSQL_ATTR_FOUND_ROWS => true,
            ],
        ],

        // 更多的数据库配置信息
    ],
];
