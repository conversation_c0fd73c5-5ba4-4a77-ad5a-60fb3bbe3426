<?php

// +----------------------------------------------------------------------
// | 缓存设置（自动环境检测）
// +----------------------------------------------------------------------

// 检查是否为本地环境
$host = $_SERVER['HTTP_HOST'] ?? '';
$isLocal = strpos($host, 'localhost') !== false ||
           strpos($host, '127.0.0.1') !== false ||
           strpos($host, '192.168.') !== false;

// 读取Redis配置
$secretsPath = __DIR__ . '/secrets.php';
$secrets = file_exists($secretsPath) ? include $secretsPath : [];
$redisConfig = $secrets['redis'] ?? [];

return [
    // 默认缓存驱动 - 生产环境也使用Redis
    'default' => 'redis',

    // 缓存连接方式配置
    'stores'  => [
        'file' => [
            // 驱动方式
            'type'       => 'File',
            // 缓存保存目录
            'path'       => '',
            // 缓存前缀
            'prefix'     => $isLocal ? 'dev_' : 'prod_',
            // 缓存有效期
            'expire'     => $isLocal ? 3600 : 86400, // 开发环境1小时，生产环境24小时
            // 缓存标签前缀
            'tag_prefix' => 'tag:',
            // 序列化机制
            'serialize'  => [],
        ],

        // Redis缓存配置（使用腾讯云Redis）
        'redis' => [
            'type'       => 'Redis',
            'host'       => $redisConfig['host'] ?? ($isLocal ? '127.0.0.1' : 'localhost'),
            'port'       => $redisConfig['port'] ?? 6379,
            'password'   => $redisConfig['password'] ?? '',
            'select'     => $redisConfig['database'] ?? ($isLocal ? 1 : 0),
            'timeout'    => $redisConfig['timeout'] ?? ($isLocal ? 5 : 10),
            'expire'     => $isLocal ? 3600 : 86400,
            'persistent' => $redisConfig['persistent'] ?? ($isLocal ? false : true),
            'prefix'     => $isLocal ? 'dev_cache:' : 'prod_cache:',
            'serialize'  => ['serialize', 'unserialize'],
            // 如果没有Redis扩展，ThinkPHP会自动使用Predis
        ],
    ],
];
