#!/bin/bash

# 日志查看脚本
LOG_DIR="runtime/log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 树洞项目日志查看器${NC}"
echo "=================================="

# 检查日志目录是否存在
if [ ! -d "$LOG_DIR" ]; then
    echo -e "${RED}❌ 日志目录不存在: $LOG_DIR${NC}"
    exit 1
fi

# 显示菜单
show_menu() {
    echo ""
    echo -e "${CYAN}请选择操作:${NC}"
    echo "1. 📋 查看日志文件列表"
    echo "2. 📄 查看信息日志 (info.log)"
    echo "3. ❌ 查看错误日志 (error.log)"
    echo "4. ⚠️  查看警告日志 (warning.log)"
    echo "5. 🔧 查看调试日志 (debug.log)"
    echo "6. 🌐 查看API日志 (api.log)"
    echo "7. 💼 查看业务日志 (business.log)"
    echo "8. 🔍 搜索日志内容"
    echo "9. 📊 查看日志统计"
    echo "10. 🔄 实时监控日志"
    echo "0. 🚪 退出"
    echo ""
    echo -n "请输入选项 (0-10): "
}

# 查看文件列表
list_files() {
    echo -e "\n${GREEN}📋 日志文件列表:${NC}"
    echo "=================================="
    for file in $LOG_DIR/*.log; do
        if [ -f "$file" ]; then
            filename=$(basename "$file")
            size=$(ls -lh "$file" | awk '{print $5}')
            modified=$(ls -l "$file" | awk '{print $6, $7, $8}')
            echo -e "${YELLOW}📄 $filename${NC} - 大小: $size - 修改时间: $modified"
        fi
    done
}

# 查看指定日志文件
view_log() {
    local logfile="$1"
    local lines="${2:-50}"
    
    if [ ! -f "$LOG_DIR/$logfile" ]; then
        echo -e "${RED}❌ 文件不存在: $logfile${NC}"
        return
    fi
    
    echo -e "\n${GREEN}📖 查看 $logfile (最新 $lines 行):${NC}"
    echo "=================================="
    
    # 使用颜色高亮不同级别的日志
    tail -n "$lines" "$LOG_DIR/$logfile" | while IFS= read -r line; do
        if [[ $line == *"[error]"* ]]; then
            echo -e "${RED}$line${NC}"
        elif [[ $line == *"[warning]"* ]]; then
            echo -e "${YELLOW}$line${NC}"
        elif [[ $line == *"[info]"* ]]; then
            echo -e "${GREEN}$line${NC}"
        elif [[ $line == *"[debug]"* ]]; then
            echo -e "${PURPLE}$line${NC}"
        else
            echo "$line"
        fi
    done
}

# 搜索日志
search_logs() {
    echo -n "请输入搜索关键词: "
    read keyword
    
    if [ -z "$keyword" ]; then
        echo -e "${RED}❌ 请输入搜索关键词${NC}"
        return
    fi
    
    echo -e "\n${GREEN}🔍 搜索结果 (关键词: $keyword):${NC}"
    echo "=================================="
    
    for file in $LOG_DIR/*.log; do
        if [ -f "$file" ]; then
            filename=$(basename "$file")
            results=$(grep -i "$keyword" "$file" 2>/dev/null)
            if [ ! -z "$results" ]; then
                echo -e "\n${CYAN}📄 $filename:${NC}"
                echo "$results" | tail -10 | while IFS= read -r line; do
                    # 高亮搜索关键词
                    highlighted=$(echo "$line" | sed "s/$keyword/${YELLOW}&${NC}/gi")
                    echo -e "$highlighted"
                done
            fi
        fi
    done
}

# 查看统计信息
show_stats() {
    echo -e "\n${GREEN}📊 日志统计信息:${NC}"
    echo "=================================="
    
    total_files=0
    total_size=0
    
    for file in $LOG_DIR/*.log; do
        if [ -f "$file" ]; then
            filename=$(basename "$file")
            size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
            lines=$(wc -l < "$file")
            
            # 格式化文件大小
            if [ $size -gt 1048576 ]; then
                size_formatted=$(echo "scale=2; $size/1048576" | bc)MB
            elif [ $size -gt 1024 ]; then
                size_formatted=$(echo "scale=2; $size/1024" | bc)KB
            else
                size_formatted="${size}B"
            fi
            
            echo -e "${YELLOW}📄 $filename:${NC} $lines 行, $size_formatted"
            
            total_files=$((total_files + 1))
            total_size=$((total_size + size))
        fi
    done
    
    # 总计
    if [ $total_size -gt 1048576 ]; then
        total_size_formatted=$(echo "scale=2; $total_size/1048576" | bc)MB
    elif [ $total_size -gt 1024 ]; then
        total_size_formatted=$(echo "scale=2; $total_size/1024" | bc)KB
    else
        total_size_formatted="${total_size}B"
    fi
    
    echo "=================================="
    echo -e "${CYAN}📊 总计: $total_files 个文件, $total_size_formatted${NC}"
}

# 实时监控
monitor_logs() {
    echo -e "\n${GREEN}🔄 实时监控日志 (按 Ctrl+C 退出):${NC}"
    echo "=================================="
    
    # 监控所有日志文件
    tail -f $LOG_DIR/*.log 2>/dev/null | while IFS= read -r line; do
        if [[ $line == *"[error]"* ]]; then
            echo -e "${RED}$line${NC}"
        elif [[ $line == *"[warning]"* ]]; then
            echo -e "${YELLOW}$line${NC}"
        elif [[ $line == *"[info]"* ]]; then
            echo -e "${GREEN}$line${NC}"
        elif [[ $line == *"[debug]"* ]]; then
            echo -e "${PURPLE}$line${NC}"
        else
            echo "$line"
        fi
    done
}

# 主循环
while true; do
    show_menu
    read choice
    
    case $choice in
        1) list_files ;;
        2) view_log "info.log" ;;
        3) view_log "error.log" ;;
        4) view_log "warning.log" ;;
        5) view_log "debug.log" ;;
        6) view_log "api.log" ;;
        7) view_log "business.log" ;;
        8) search_logs ;;
        9) show_stats ;;
        10) monitor_logs ;;
        0) echo -e "${GREEN}👋 再见!${NC}"; exit 0 ;;
        *) echo -e "${RED}❌ 无效选项，请重新选择${NC}" ;;
    esac
    
    echo ""
    echo -n "按回车键继续..."
    read
done
