<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳转中...</title>
    <style>
        body {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            text-align: center;
            background-color: #f8f8f8;
        }
        .loading {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .message {
            font-size: 16px;
            color: #333;
            max-width: 80%;
        }
        .error {
            color: #e74c3c;
            margin-top: 10px;
        }
        .btn {
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="loading"></div>
    <div class="message">正在跳转到文章，请稍候...</div>
    <div id="error-msg" class="error" style="display: none;"></div>
    <a href="#" id="manual-link" class="btn" style="display: none;">手动跳转</a>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const targetUrl = urlParams.get('url');
            const manualLink = document.getElementById('manual-link');
            const errorMsg = document.getElementById('error-msg');
            
            // 如果存在目标URL参数
            if (targetUrl) {
                try {
                    // 解码URL
                    const decodedUrl = decodeURIComponent(targetUrl);
                    
                    // 设置手动跳转链接
                    manualLink.href = decodedUrl;
                    
                    // 自动跳转
                    setTimeout(function() {
                        window.location.href = decodedUrl;
                    }, 1500);
                    
                    // 5秒后如果还未跳转，显示手动跳转按钮
                    setTimeout(function() {
                        manualLink.style.display = 'inline-block';
                        errorMsg.textContent = '自动跳转未完成，请点击下方按钮手动跳转';
                        errorMsg.style.display = 'block';
                    }, 5000);
                    
                } catch (error) {
                    // 处理URL解码错误
                    errorMsg.textContent = '链接格式错误，无法跳转';
                    errorMsg.style.display = 'block';
                    document.querySelector('.loading').style.display = 'none';
                }
            } else {
                // 没有提供目标URL
                errorMsg.textContent = '未提供跳转链接';
                errorMsg.style.display = 'block';
                document.querySelector('.loading').style.display = 'none';
            }
        });
    </script>
</body>
</html> 