{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.0.0", "topthink/framework": "^8.0", "topthink/think-orm": "^3.0", "topthink/think-filesystem": "^2.0", "easy-task/easy-task": "^2.4", "firebase/php-jwt": "^6.11", "topthink/think-view": "^2.0", "workerman/workerman": "5.0", "topthink/think-worker": "5.0", "guzzlehttp/guzzle": "^7.9", "symfony/dom-crawler": "^7.2", "qcloud/cos-sdk-v5": "^2.6", "predis/predis": "^3.2"}, "require-dev": {"symfony/var-dumper": ">=4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}, "minimum-stability": "dev", "prefer-stable": true}