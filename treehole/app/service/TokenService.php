<?php
namespace app\service;

use app\util\JwtUtil;
use think\facade\Request;
use think\facade\Log;

class TokenService
{
    /**
     * 获取当前用户ID
     * 从token中解析并返回用户ID
     * 
     * @return int|null 用户ID，失败则返回null
     */
    public static function getCurrentUid()
    {
        // 从请求头中获取token
        $token = Request::header('Authorization', '');
        if (empty($token)) {
            $token = Request::header('token', '');
        }
        
        // 如果没有在header中找到token，则尝试从GET或POST参数中获取
        if (empty($token)) {
            $token = Request::param('token', '');
            $token = Request::param('access_token', $token);
        }
        
        // 如果没有提供token
        if (empty($token)) {
            return null;
        }
        
        // 处理带有Bearer前缀的token
        if (is_string($token) && strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }
        
        // 验证token并获取用户ID
        try {
            $userData = JwtUtil::validateToken($token);
            
            if (!$userData || !isset($userData['user_id'])) {
                return null;
            }
            
            // 确保用户ID是整数
            $userId = intval($userData['user_id']);
            
            // 记录日志
            Log::info('TokenService - 获取到用户ID: ' . $userId);
            
            return $userId;
        } catch (\Exception $e) {
            Log::error('TokenService - 验证token异常: ' . $e->getMessage());
            return null;
        }
    }
} 