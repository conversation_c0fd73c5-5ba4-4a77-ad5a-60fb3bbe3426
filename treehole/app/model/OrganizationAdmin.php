<?php
namespace app\model;

use think\Model;

class OrganizationAdmin extends Model
{
    // 设置表名
    protected $name = 'organization_admins';
    
    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'organization_id' => 'int',
        'user_id'         => 'int',
        'create_time'     => 'datetime',
        'update_time'     => 'datetime',
    ];
    
    // 关联组织
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }
    
    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    // 获取用户管理的所有组织
    public static function getUserOrganizations($userId)
    {
        return self::where('user_id', $userId)
            ->with('organization')
            ->select()
            ->column('organization');
    }
} 