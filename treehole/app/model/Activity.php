<?php
namespace app\model;

use think\Model;

class Activity extends Model
{
    // 设置表名
    protected $name = 'activities';
    
    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'title'           => 'string',
        'description'     => 'string',
        'cover_image'     => 'string',
        'organization_id' => 'int',
        'user_id'         => 'int',
        'has_reward'      => 'tinyint',
        'is_online'       => 'tinyint',
        'article_url'     => 'string',
        'start_time'      => 'datetime',
        'end_time'        => 'datetime',
        'status'          => 'int',
        'create_time'     => 'datetime',
        'update_time'     => 'datetime',
    ];
    
    // 状态常量
    const STATUS_DRAFT = 0;    // 草稿
    const STATUS_PUBLISH = 1;  // 已发布
    const STATUS_CANCEL = 2;   // 已取消
    const STATUS_DELETE = 3;   // 已删除
    
    // 关联组织
    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }
    
    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    /**
     * 获取活动列表
     * @param array $params 过滤参数
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return \think\Paginator
     */
    public static function getList($params = [], $page = 1, $pageSize = 10)
    {
        $query = self::where('status', self::STATUS_PUBLISH);
        
        // 按组织过滤
        if (!empty($params['organization_id'])) {
            $query->where('organization_id', $params['organization_id']);
        }
        
        // 按开始时间过滤
        if (!empty($params['start_time'])) {
            $query->where('start_time', '>=', $params['start_time']);
        }
        
        // 按结束时间过滤
        if (!empty($params['end_time'])) {
            $query->where('end_time', '<=', $params['end_time']);
        }
        
        // 关键词搜索
        if (!empty($params['keyword'])) {
            $query->where('title|description', 'like', '%' . $params['keyword'] . '%');
        }
        
        return $query->with(['organization'])
            ->order('create_time', 'desc')
            ->paginate([
                'list_rows' => $pageSize,
                'page' => $page,
            ]);
    }
    
    /**
     * 获取活动列表（分为进行中和已结束）
     * @return array 包含active和past两个数组的关联数组
     */
    public static function getActivityList()
    {
        // 当前时间
        $now = date('Y-m-d H:i:s');
        
        // 获取所有活动
        $allActivities = self::where('status', self::STATUS_PUBLISH)
                        ->with(['organization', 'user'])
                        ->order('create_time', 'desc')
                        ->select();
        
        // 分类活动
        $activeList = [];
        $pastList = [];
        
        foreach ($allActivities as $activity) {
            // 如果没有结束时间或者结束时间未到，则是进行中活动
            if (empty($activity->end_time) || $activity->end_time > $now) {
                $activeList[] = $activity;
            } else {
                $pastList[] = $activity;
            }
        }
        
        return [
            'active' => $activeList,
            'past' => $pastList
        ];
    }
    
    /**
     * 获取活动详情
     * @param int $id 活动ID
     * @return Activity|null
     */
    public static function getDetail($id)
    {
        return self::where('id', $id)
            ->where('status', self::STATUS_PUBLISH)
            ->with(['organization'])
            ->find();
    }
    
    /**
     * 新增浏览量
     * @param int $id 活动ID
     * @return bool
     */
    public static function increaseViews($id)
    {
        return self::where('id', $id)->inc('views')->update();
    }
    
    /**
     * 发布活动
     * @param array $data 活动数据
     * @param int $userId 用户ID
     * @return Activity
     */
    public static function publish($data, $userId)
    {
        $activity = new self();
        $activity->title = $data['title'];
        $activity->description = $data['description'];
        $activity->cover_image = $data['cover_image'];
        $activity->organization_id = $data['organization_id'];
        $activity->user_id = $userId;
        $activity->status = self::STATUS_PUBLISH;
        $activity->views = 0;
        $activity->start_time = $data['start_time'] ?? null;
        $activity->end_time = $data['end_time'] ?? null;
        $activity->save();
        
        return $activity;
    }
    
    /**
     * 获取封面图完整URL
     * @return string
     */
    public function getCoverImageUrlAttr()
    {
        if (empty($this->cover_image)) {
            return '';
        }
        
        if (strpos($this->cover_image, 'http') === 0) {
            return $this->cover_image;
        }
        
        return 'https://www.bjgaoxiaoshequ.store/tupian/活动/' . $this->cover_image;
    }
} 