<?php
namespace app\model;

use think\Model;

class OrganizationCategory extends Model
{
    // 设置表名
    protected $name = 'organization_categories';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'name'        => 'string',
        'sort'        => 'int',
        'status'      => 'int',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
    ];
    
    // 状态常量
    const STATUS_DISABLE = 0; // 禁用
    const STATUS_ENABLE = 1;  // 启用
    
    // 关联组织
    public function organizations()
    {
        return $this->hasMany(Organization::class, 'category_id');
    }
    
    /**
     * 获取启用的分类列表
     * @return \think\Collection
     */
    public static function getEnabledList()
    {
        return self::where('status', self::STATUS_ENABLE)
            ->order('sort', 'asc')
            ->select();
    }
    
    /**
     * 获取分类及其组织
     * @return \think\Collection
     */
    public static function getCategoriesWithOrganizations()
    {
        return self::where('status', self::STATUS_ENABLE)
            ->with(['organizations' => function($query) {
                $query->where('status', Organization::STATUS_ENABLE);
            }])
            ->order('sort', 'asc')
            ->select();
    }
    
    /**
     * 获取所有分类（不区分状态）
     * @return \think\Collection
     */
    public static function getAllCategories()
    {
        return self::order('sort', 'asc')
            ->select();
    }
} 