<?php
namespace app\model;

use think\Model;

class Organization extends Model
{
    // 设置表名
    protected $name = 'organizations';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'name'        => 'string',
        'description' => 'string',
        'logo'        => 'string',
        'qrcode'      => 'string',
        'category_id' => 'int',
        'status'      => 'int',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
    ];
    
    // 状态常量
    const STATUS_DISABLE = 0; // 禁用
    const STATUS_ENABLE = 1;  // 启用
    
    // 关联分类
    public function category()
    {
        return $this->belongsTo(OrganizationCategory::class, 'category_id');
    }
    
    // 关联活动
    public function activities()
    {
        return $this->hasMany(Activity::class, 'organization_id');
    }
    
    // 关联管理员
    public function admins()
    {
        return $this->hasMany(OrganizationAdmin::class, 'organization_id');
    }
    
    /**
     * 获取启用的组织列表
     * @param int|null $categoryId 分类ID
     * @return \think\Collection
     */
    public static function getEnabledList($categoryId = null)
    {
        $query = self::where('status', self::STATUS_ENABLE);
        
        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }
        
        return $query->order('id', 'desc')->select();
    }
    
    /**
     * 获取组织详情
     * @param int $id 组织ID
     * @return Organization|null
     */
    public static function getDetail($id)
    {
        return self::where('id', $id)
            ->where('status', self::STATUS_ENABLE)
            ->find();
    }
    
    /**
     * 获取二维码完整URL
     * @return string
     */
    public function getQrcodeUrlAttr()
    {
        if (empty($this->qrcode)) {
            return '';
        }
        
        return 'https://www.bjgaoxiaoshequ.store/tupian/公众号/' . $this->qrcode;
    }
    
    // 获取带分类的组织列表
    public static function getOrganizationsByCategory()
    {
        $categories = OrganizationCategory::order('sort')->select();
        $result = [];
        
        foreach ($categories as $category) {
            $orgs = self::where('category_id', $category->id)
                ->where('status', self::STATUS_ENABLE)
                ->order('id', 'desc')
                ->select();
                
            $result[$category->id] = [
                'category' => $category,
                'organizations' => $orgs
            ];
        }
        
        return $result;
    }
    
    // 检查用户是否是该组织的管理员
    public static function isUserOrgAdmin($userId, $organizationId)
    {
        return OrganizationAdmin::where('user_id', $userId)
            ->where('organization_id', $organizationId)
            ->find() ? true : false;
    }
} 