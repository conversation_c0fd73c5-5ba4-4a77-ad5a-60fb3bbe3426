<?php
namespace app\model;

use think\Model;

class User extends Model
{
    // 设置表名
    protected $name = 'user';
    
    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'username'        => 'string',
        'nickname'        => 'string',
        'status'          => 'string',
        'activity_subscribed' => 'int',
        'last_view_activity_time' => 'datetime',
        'avatar'          => 'string',
        'create_time'     => 'datetime',
        'update_time'     => 'datetime',
    ];
    
    /**
     * 检查用户是否为管理员
     * @return bool
     */
    public function isAdmin()
    {
        // 检查用户状态是否为'管理员'
        return $this->status === '管理员';
    }
    
    /**
     * 关联活动
     */
    public function activities()
    {
        return $this->hasMany(Activity::class, 'user_id');
    }
    
    /**
     * 关联组织管理员
     */
    public function organizationAdmins()
    {
        return $this->hasMany(OrganizationAdmin::class, 'user_id');
    }
} 