<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\Db;

class Course extends BaseController
{
    /**
     * 获取课程列表
     */
    public function list()
    {
        $params = input();
        
        // 验证必填参数
        if (empty($params['page'])) {
            return json(['code' => 1001, 'msg' => '页码不能为空']);
        }
        if (empty($params['pageSize'])) {
            return json(['code' => 1001, 'msg' => '每页数量不能为空']);
        }
        if (empty($params['sort']) || !in_array($params['sort'], ['rating', 'time'])) {
            return json(['code' => 1001, 'msg' => '排序方式错误']);
        }

        // 验证筛选参数（department和type不能同时存在）
        if (!empty($params['department']) && !empty($params['type'])) {
            return json(['code' => 1001, 'msg' => '系别筛选和类型筛选不能同时使用']);
        }

        try {
            $page = max(1, intval($params['page']));
            $pageSize = max(1, min(50, intval($params['pageSize'])));

            $query = Db::name('courses');
            
            if (!empty($params['department'])) {
                $department = intval($params['department']);
                if ($department > 0) {
                    if ($department > 54 && $department != 99) {
                        return json(['code' => 1001, 'msg' => '系别代码不存在']);
                    }
                    if ($department == 99) {
                        // 特殊处理：当department_id为99时，查找id在89-100之间的课程
                        $query->whereBetween('department_id', [90, 99]);
                    } else {
                        $query->where('department_id', $department);
                    }
                }
            }

            // 类型筛选
            if (!empty($params['type'])) {
                $query->where('category', $params['type']);
            }

            // 排序
            if ($params['sort'] === 'rating') {
                $query->order('overall_score', 'desc');
            } else {
                $query->order('created_at', 'desc');
            }

            $total = $query->count();
            $list = $query->field([
                'id',
                'name',
                'department_id as department',
                'overall_score',
                'grade_score',
                'ability_score',
                'campus',
                'category',
                'nature',
                'code'
            ])
            ->page($page, $pageSize)
            ->select()
            ->toArray();

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'list' => $list,
                    'hasMore' => ($page * $pageSize) < $total
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 5000, 'msg' => '服务器错误：' . $e->getMessage()]);
        }
    }

    /**
     * 获取课程详情
     */
    public function detail()
    {
        $courseId = input('course_id');
        if (empty($courseId)) {
            return json(['code' => 1, 'msg' => '课程ID不能为空']);
        }

        try {
            $course = Db::name('courses')
                ->where('id', $courseId)
                ->field([
                    'id',
                    'name',
                    'department_id as department',
                    'category',
                    'nature',
                    'campus',
                    'code',
                    'overall_score',
                    'grade_score',
                    'ability_score'
                ])
                ->find();

            if (!$course) {
                return json(['code' => 1, 'msg' => '课程不存在']);
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => $course
            ]);

        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取课程详情失败：' . $e->getMessage()]);
        }
    }

    /**
     * 提交课程评分
     */
    public function rate()
    {
        $params = input();
        
        // 调试信息
        trace('接收到的参数：' . json_encode($params), 'debug');
        
        if (empty($params['course_id'])) {
            return json(['code' => 1, 'msg' => '课程ID不能为空']);
        }

        // 兼容两种参数名
        $userId = $params['user_id'] ?? $params['userId'] ?? '';
        $openId = $params['open_id'] ?? $params['openid'] ?? '';
        
        if (empty($userId) || empty($openId)) {
            return json([
                'code' => 1, 
                'msg' => '用户信息不完整',
                'debug' => [
                    'received_params' => $params,
                    'user_id' => $userId,
                    'open_id' => $openId
                ]
            ]);
        }

        // 验证评分参数
        $scores = ['grade_score', 'ability_score', 'overall_score'];
        foreach ($scores as $score) {
            if (!isset($params[$score]) || !$this->validateScore($params[$score])) {
                return json(['code' => 1, 'msg' => '评分必须在0.5-5分之间，且步长为0.5']);
            }
        }

        try {
            // 验证用户信息
            $user = Db::name('user')
                ->where([
                    'id' => $userId,
                    'openid' => $openId
                ])->find();
            if (!$user) {
                return json(['code' => 1, 'msg' => '用户信息验证失败']);
            }

            // 验证课程是否存在
            $course = Db::name('courses')->where('id', $params['course_id'])->find();
            if (!$course) {
                return json(['code' => 1, 'msg' => '课程不存在']);
            }

            // 检查是否已经评分过
            $exists = Db::name('course_ratings')
                ->where([
                    'user_id' => $userId,
                    'course_id' => $params['course_id']
                ])->find();

            if ($exists) {
                return json(['code' => 1, 'msg' => '已经评分过该课程']);
            }

            // 开启事务
            Db::startTrans();
            try {
                // 添加评分记录
                $data = [
                    'user_id' => $userId,
                    'course_id' => $params['course_id'],
                    'grade_score' => $params['grade_score'],
                    'ability_score' => $params['ability_score'],
                    'overall_score' => $params['overall_score'],
                    'created_at' => date('Y-m-d H:i:s')
                ];
                Db::name('course_ratings')->insert($data);

                // 更新课程平均分
                $this->updateCourseAverageScore($params['course_id']);

                // 提交事务
                Db::commit();

                return json([
                    'code' => 0,
                    'msg' => '评分成功',
                    'data' => [
                        'grade_score' => $params['grade_score'],
                        'ability_score' => $params['ability_score'],
                        'overall_score' => $params['overall_score']
                    ]
                ]);

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return json(['code' => 2, 'msg' => '评分失败：' . $e->getMessage()]);
            }

        } catch (\Exception $e) {
            return json(['code' => 5000, 'msg' => '服务器错误：' . $e->getMessage()]);
        }
    }

    /**
     * 验证评分是否合法
     */
    private function validateScore($score)
    {
        return $score >= 0.5 && $score <= 5 && ($score * 10) % 5 == 0;
    }

    /**
     * 更新课程平均分
     */
    private function updateCourseAverageScore($courseId)
    {
        try {
            $scores = Db::name('course_ratings')
                ->where('course_id', $courseId)
                ->field([
                    'AVG(grade_score) as avg_grade_score',
                    'AVG(ability_score) as avg_ability_score',
                    'AVG(overall_score) as avg_overall_score'
                ])
                ->find();

            // 确保所有评分都有值，并转换为浮点数
            $avgGradeScore = floatval($scores['avg_grade_score'] ?? 0);
            $avgAbilityScore = floatval($scores['avg_ability_score'] ?? 0);
            $avgOverallScore = floatval($scores['avg_overall_score'] ?? 0);

            Db::name('courses')
                ->where('id', $courseId)
                ->update([
                    'grade_score' => round($avgGradeScore, 1),
                    'ability_score' => round($avgAbilityScore, 1),
                    'overall_score' => round($avgOverallScore, 1),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            return true;
        } catch (\Exception $e) {
            // 记录错误日志但不影响评分结果
            trace('更新课程平均分失败：' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 搜索课程
     */
    public function search()
    {
        $keyword = input('keyword');
        if (empty($keyword)) {
            return json(['code' => 1001, 'msg' => '搜索关键词不能为空']);
        }

        try {
            $list = Db::name('courses')
                ->where('name', 'like', "%{$keyword}%")
                ->field([
                    'id',
                    'name',
                    'department_id as department',
                    'overall_score',
                    'grade_score',
                    'ability_score',
                    'campus',
                    'category',
                    'nature',
                    'code'
                ])
                ->select()
                ->toArray();

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => $list
            ]);

        } catch (\Exception $e) {
            return json(['code' => 5000, 'msg' => '服务器错误：' . $e->getMessage()]);
        }
    }
} 