<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Filesystem;
use think\Request;
use think\response\Json;
use app\util\ImageSecurityUtil;

class OfficialAccount extends BaseController
{
    /**
     * 获取公众号分类列表
     */
    public function getOfficialCategories(): Json
    {
        // 定义分类列表（使用id作为subcategory_id，所有分类都属于官方组织）
        $categories = [
            ['id' => 1, 'name' => '校级公众号', 'category_id' => 1],
            ['id' => 2, 'name' => '院级公众号', 'category_id' => 1],
            ['id' => 3, 'name' => '校园生活公众号', 'category_id' => 1],
            ['id' => 4, 'name' => '灵行校园公众号', 'category_id' => 1]
        ];
        
        return json(['code' => 200, 'msg' => '获取成功', 'data' => $categories]);
    }
    
    /**
     * 添加公众号
     */
    public function addOfficialAccount(Request $request): Json
    {
        // 验证参数
        $params = $request->param();
        
        if (empty($params['name']) || empty($params['category_id'])) {
            return json(['code' => 400, 'msg' => '参数不完整']);
        }
        
        // 检查logo图片
        $logo = !empty($params['logo']) ? $params['logo'] : null;
        
        // 处理图片（从临时文件移动到永久存储）
        if ($logo && strpos($logo, 'temp') !== false) {
            // 提取文件名
            $filename = basename($logo);
            
            // 移动到正式目录
            $destPath = 'public/uploads/official_accounts/' . $filename;
            $publicPath = '/uploads/official_accounts/' . $filename;
            
            // 确保目录存在
            $directory = dirname(root_path() . $destPath);
            if (!is_dir($directory)) {
                mkdir($directory, 0777, true);
            }
            
            // 移动文件
            rename(root_path() . $logo, root_path() . $destPath);
            
            // 更新路径
            $logo = $publicPath;
        }
        
        // 处理二维码地址，确保有完整域名前缀
        $qrcode = !empty($params['qrcode']) ? $params['qrcode'] : null;
        if ($qrcode && strpos($qrcode, 'http') !== 0) {
            // 如果不是以http开头，添加域名前缀
            if (strpos($qrcode, '/') === 0) {
                // 如果以/开头，直接添加域名
                $qrcode = 'https://www.bjgaoxiaoshequ.store' . $qrcode;
            } else {
                // 否则添加域名和/
                $qrcode = 'https://www.bjgaoxiaoshequ.store/' . $qrcode;
            }
        }
        
        // 保存到数据库
        try {
            // 使用category_id=1（官方组织），将传入的category_id用作subcategory_id
            $insertData = [
                'name' => $params['name'],
                'category_id' => 1, // 固定为官方组织
                'subcategory_id' => intval($params['category_id']), // 使用传入的category_id作为subcategory_id
                'logo' => $logo,
                'description' => !empty($params['description']) ? $params['description'] : '',
                'qrcode' => $qrcode,
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            $id = Db::table('organizations')->insertGetId($insertData);
            
            return json(['code' => 200, 'msg' => '添加成功', 'data' => ['id' => $id]]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '添加失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 上传公众号二维码或Logo
     */
    public function uploadQrcode(Request $request): Json
    {
        // 获取上传的文件
        $file = $request->file('image');
        
        // 验证文件
        if (!$file) {
            return json(['code' => 400, 'msg' => '请上传图片']);
        }
        
        // 严格的文件安全验证
        $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
        if (!$securityCheck['success']) {
            return json(['code' => 400, 'msg' => $securityCheck['message']]);
        }
        
        // 上传到临时目录
        try {
            $savename = Filesystem::disk('public')->putFile('temp', $file);
            $url = '/uploads/' . $savename;
            
            return json([
                'code' => 200, 
                'msg' => '上传成功', 
                'data' => [
                    'url' => $url,
                    'name' => $file->getOriginalName()
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '上传失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 根据分类获取公众号列表
     */
    public function getOfficialAccountsByCategory(Request $request): Json
    {
        // 获取参数
        $categoryId = $request->param('category_id', '1', 'trim'); // 默认为官方组织(1)
        $subcategoryId = $request->param('subcategory_id', '0', 'trim'); // 0表示不限制子分类
        
        try {
            // 记录请求参数
            error_log("接收到请求 category_id = {$categoryId}, subcategory_id = {$subcategoryId}");
            
            // 构建基础查询
            $query = Db::table('organizations')->where('status', 1);
            
            // 添加category_id筛选
            if (!empty($categoryId) && $categoryId !== '0') {
                $intCategoryId = intval($categoryId);
                $query->where('category_id', $intCategoryId);
            }
            
            // 添加subcategory_id筛选
            if (!empty($subcategoryId) && $subcategoryId !== '0') {
                $intSubcategoryId = intval($subcategoryId);
                $query->where('subcategory_id', $intSubcategoryId);
            }
            
            // 添加排序
            $query->order('id', 'asc');
            
            // 执行查询并获取结果
            $list = $query->select();
            
            // 记录查询结果
            error_log("查询结果数量: " . count($list));
            
            // 处理图片URL
            foreach ($list as &$item) {
                // 处理logo
                if (!empty($item['logo']) && strpos($item['logo'], 'http') !== 0) {
                    $path = ltrim($item['logo'], '/');
                    $item['logo'] = 'https://www.bjgaoxiaoshequ.store/' . $path;
                }
                
                // 处理qrcode
                if (!empty($item['qrcode']) && strpos($item['qrcode'], 'http') !== 0) {
                    $path = ltrim($item['qrcode'], '/');
                    $item['qrcode'] = 'https://www.bjgaoxiaoshequ.store/' . $path;
                }
                
                // 格式化时间为易读格式
                if (isset($item['create_time'])) {
                    $item['create_time_formatted'] = date('Y-m-d H:i:s', strtotime($item['create_time']));
                }
                if (isset($item['update_time'])) {
                    $item['update_time_formatted'] = date('Y-m-d H:i:s', strtotime($item['update_time']));
                }
                
                // 获取子分类名称
                if (isset($item['subcategory_id'])) {
                    switch ($item['subcategory_id']) {
                        case 1:
                            $item['subcategory_name'] = '校级公众号';
                            break;
                        case 2:
                            $item['subcategory_name'] = '院级公众号';
                            break;
                        case 3:
                            $item['subcategory_name'] = '校园生活公众号';
                            break;
                        case 4:
                            $item['subcategory_name'] = '灵行校园公众号';
                            break;
                        default:
                            $item['subcategory_name'] = '其他公众号';
                    }
                }
            }
            
            // 返回成功结果
            return json([
                'code' => 200, 
                'msg' => '获取成功', 
                'data' => $list,
                'total' => count($list)
            ]);
        } catch (\Exception $e) {
            // 记录异常
            error_log("获取公众号列表出错: " . $e->getMessage());
            // 返回错误信息
            return json(['code' => 500, 'msg' => '获取公众号列表失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 添加公众号（通过文件上传方式）
     */
    public function addOfficialAccountWithFile(Request $request): Json
    {
        // 验证参数
        $params = $request->param();
        
        // 记录请求信息到日志
        error_log('添加公众号请求参数: ' . json_encode($params));
        
        // 检查必填字段
        if (empty($params['name']) || empty($params['category_id'])) {
            error_log('参数不完整: name=' . ($params['name'] ?? '') . ', category_id=' . ($params['category_id'] ?? ''));
            return json(['code' => 400, 'msg' => '参数不完整']);
        }
        
        // 获取上传的文件
        $file = $request->file('logo');
        
        if (!$file) {
            error_log('未接收到上传文件');
            return json(['code' => 400, 'msg' => '请上传图片']);
        }
        
        // 记录文件信息
        error_log('接收到文件: ' . $file->getOriginalName() . ', 大小: ' . $file->getSize());
        
        // 严格的文件安全验证
        $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
        if (!$securityCheck['success']) {
            error_log('文件验证失败: ' . $securityCheck['message']);
            return json(['code' => 400, 'msg' => $securityCheck['message']]);
        }
        
        try {
            // 创建目标目录
            $uploadDir = 'tupian/公众号';
            $rootPath = root_path() . 'public/';
            $fullDir = $rootPath . $uploadDir;
            
            // 确保目录存在
            if (!is_dir($fullDir)) {
                if (!mkdir($fullDir, 0777, true)) {
                    error_log('创建目录失败: ' . $fullDir);
                    return json(['code' => 500, 'msg' => '创建目录失败']);
                }
                error_log('创建目录: ' . $fullDir);
            }
            
            // 生成文件名：公众号名称 + 时间戳 + 随机数
            $safeName = preg_replace('/[\\\\\/\:\*\?\"\<\>\|]/', '', $params['name']); // 移除非法字符
            $timestamp = date('YmdHis');
            $random = rand(1000, 9999);
            $filename = $safeName . '_' . $timestamp . '_' . $random . '.' . $file->getOriginalExtension();
            $filepath = $uploadDir . '/' . $filename;
            $fullpath = $rootPath . $filepath;
            
            // 移动上传的文件
            if (!$file->move($fullDir, $filename)) {
                error_log('文件移动失败: ' . $file->getError());
                return json(['code' => 500, 'msg' => '文件上传失败']);
            }
            error_log('文件已保存到: ' . $fullpath);
            
            // 处理文件路径，添加域名前缀
            $qrcodeUrl = 'https://www.bjgaoxiaoshequ.store/' . $filepath;
            
            // 保存到数据库
            $insertData = [
                'name' => $params['name'],
                'category_id' => 1, // 固定为官方组织
                'subcategory_id' => intval($params['category_id']), // 使用传入的category_id作为subcategory_id
                'logo' => null, // logo字段设为null
                'qrcode' => $qrcodeUrl, // 将完整的图片URL路径保存到qrcode字段
                'description' => !empty($params['description']) ? $params['description'] : '',
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            // 记录即将插入的数据
            error_log('尝试插入数据: ' . json_encode($insertData));
            
            // 执行数据库插入
            $id = Db::table('organizations')->insertGetId($insertData);
            if (!$id) {
                error_log('数据库插入失败: ' . Db::getError());
                return json(['code' => 500, 'msg' => '数据库插入失败']);
            }
            error_log('数据已插入，ID: ' . $id);
            
            return json(['code' => 200, 'msg' => '添加成功', 'data' => ['id' => $id]]);
        } catch (\Exception $e) {
            error_log('添加公众号失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 500, 'msg' => '添加失败: ' . $e->getMessage()]);
        }
    }
} 