<?php
namespace Home\Controller;

use Think\Controller;
require_once THINK_PATH . 'Extend/Vendor/PHPMailer/src/PHPMailer.php';
require_once THINK_PATH . 'Extend/Vendor/PHPMailer/src/SMTP.php';
require_once THINK_PATH . 'Extend/Vendor/PHPMailer/src/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
class OtherController extends BaseController
{
    public function sendVerificationCode() {
        if (IS_POST) {
            $email = I('post.email');
            $user_id = I('post.user_id');

            if (empty($user_id)) {
                $this->ajaxReturn(['error' => '请先登录再认证'], 'JSON');
            }

            if (empty($email)) {
                $this->ajaxReturn(['error' => '请输入邮箱地址'], 'JSON');
            }
            
            // 验证邮箱格式是否正确
            if (!preg_match('/^(?:[a-zA-Z]{2}\d{7}|\d{8})@buaa\.edu\.cn$/', $email)) {
                $this->ajaxReturn(['error' => '邮箱格式有误，请使用学号邮箱认证'], 'JSON');
            }

            // 生成验证码
            $verification_code = $this->generateVerificationCode();
            $current_time = date('Y-m-d H:i:s'); // 获取当前时间

            // 更新用户表中的 code 和 code_generated_at 字段
            $userModel = M('User');
            $userModel->where(['id' => $user_id])->save([
                'code' => $verification_code,
                'code_generated_at' => $current_time
            ]);

            // 发送邮件
            try {
                $this->sendEmail($email, $verification_code);
                $this->ajaxReturn(['message' => '验证码已成功发送到您的邮箱'], 'JSON');
            } catch (Exception $e) {
                $this->ajaxReturn(['error' => '邮件发送失败: ' . $e->getMessage()], 'JSON');
            }
        } else {
            $this->ajaxReturn(['error' => '无效的请求方式'], 'JSON');
        }
    }

    // 生成6位随机验证码
    private function generateVerificationCode() {
        return rand(100000, 999999);
    }

    // 发送邮件方法
    private function sendEmail($email, $verification_code) {
        // 邮件主题和内容
        $subject = "【灵行BUAA】学生认证,您的验证码";
        $body = "您的验证码是: " . $verification_code . "\n十分钟之内有效";

        $mail = new PHPMailer(true);  // 传入 `true` 表示启用异常处理
        try {
            // 配置SMTP服务器
            $mail->isSMTP();
            $mail->Host = 'smtp.qq.com';  // 替换为您的SMTP服务器
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';  // SMTP用户名
            $mail->Password = 'auwcvrhmwqubdbhi';  // SMTP密码
            $mail->SMTPSecure = 'ssl';  // 启用SSL加密
            $mail->Port = 465;  // 使用的SMTP端口

            // 发件人和收件人设置
            $mail->setFrom('<EMAIL>', '月光下的温柔');
            $mail->addAddress($email);  // 收件人
            $mail->CharSet='UTF-8';
            // 邮件内容设置
            $mail->isHTML(false);
            $mail->Subject = $subject;
            $mail->Body = $body;

            // 发送邮件
            if (!$mail->send()) {
                throw new Exception('Mailer Error: ' . $mail->ErrorInfo);
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public function verifyCode() {
        if (IS_POST) {
            $email = I('post.email');
            $user_id = I('post.user_id');
            $code = I('post.code');

            if (empty($user_id)) {
                $this->ajaxReturn(['error' => '请先登录再认证'], 'JSON');
            }

            if (empty($email)) {
                $this->ajaxReturn(['error' => '请输入邮箱地址'], 'JSON');
            }

            if (empty($code)) {
                $this->ajaxReturn(['error' => '请输入验证码'], 'JSON');
            }

            // 查询用户信息
            $userModel = M('User');
            $user = $userModel->where(['id' => $user_id])->find();

            // 检查验证码是否正确
            if ($user && $user['code'] === $code) {
                // 检查验证码是否在10分钟内有效
                $code_generated_at = strtotime($user['code_generated_at']);
                $current_time = time();
                $time_diff = $current_time - $code_generated_at;

                if ($time_diff <= 600) { // 600秒即10分钟
                    // 验证成功，更新 status 为 verified 并将邮箱存入 xuehao 字段
                    $updateData = [
                        'status' => 'verified',
                        'xuehao' => $email // 将邮箱地址存到 xuehao 字段
                    ];
                    $userModel->where(['id' => $user_id])->save($updateData);

                    $this->ajaxReturn(['message' => '验证成功，状态已更新为verified，邮箱已存入xuehao字段'], 'JSON');
                } else {
                    $this->ajaxReturn(['error' => '验证码已过期'], 'JSON');
                }
            } else {
                $this->ajaxReturn(['error' => '验证码错误'], 'JSON');
            }
        } else {
            $this->ajaxReturn(['error' => '无效的请求方式'], 'JSON');
        }
    }









}