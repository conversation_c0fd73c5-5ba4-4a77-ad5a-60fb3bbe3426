<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\Request;

class ButtonSetting extends BaseController
{
    /**
     * 保存用户按钮设置
     * @param Request $request
     * @return \think\Response
     */
    public function saveButtonSetting(Request $request)
    {
        $user_id = $request->post('user_id', '', 'trim');
        $button_type = $request->post('button_type', '', 'trim'); // official_account, activity_subscribe等
        $button_status = $request->post('button_status', '1', 'trim'); // 1显示，0不显示
        
        // 验证参数
        if (empty($user_id) || empty($button_type)) {
            return json(['code' => 400, 'message' => '参数错误']);
        }
        
        // 检查用户是否存在
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return json(['code' => 404, 'message' => '用户不存在']);
        }
        
        // 查询用户是否有设置记录
        $setting = Db::name('user_button_setting')->where('user_id', $user_id)->find();
        
        if ($setting) {
            // 如果有记录，更新对应的按钮状态
            $settingData = json_decode($setting['setting_data'], true) ?: [];
            $settingData[$button_type] = $button_status;
            
            Db::name('user_button_setting')->where('user_id', $user_id)
                ->update(['setting_data' => json_encode($settingData)]);
        } else {
            // 如果没有记录，创建新记录
            $settingData = [$button_type => $button_status];
            
            Db::name('user_button_setting')->insert([
                'user_id' => $user_id,
                'setting_data' => json_encode($settingData)
            ]);
        }
        
        return json([
            'code' => 200, 
            'message' => '保存成功', 
            'data' => ['button_type' => $button_type, 'button_status' => $button_status]
        ]);
    }
    
    /**
     * 获取用户按钮设置
     * @param Request $request
     * @return \think\Response
     */
    public function getButtonSetting(Request $request)
    {
        $user_id = $request->post('user_id', '', 'trim');
        $button_type = $request->post('button_type', '', 'trim'); // 可选参数，如果提供则返回特定按钮状态
        
        if (empty($user_id)) {
            return json(['code' => 400, 'message' => '参数错误']);
        }
        
        // 获取设置
        $setting = Db::name('user_button_setting')->where('user_id', $user_id)->find();
        
        if (!$setting) {
            // 如果没有找到设置，返回默认值
            if (!empty($button_type)) {
                return json([
                    'code' => 200, 
                    'message' => '获取成功', 
                    'data' => ['button_status' => '1'] // 默认显示
                ]);
            } else {
                return json([
                    'code' => 200, 
                    'message' => '获取成功', 
                    'data' => ['setting_data' => []] // 默认空设置
                ]);
            }
        }
        
        // 解析设置数据
        $settingData = json_decode($setting['setting_data'], true) ?: [];
        
        // 如果请求特定按钮状态
        if (!empty($button_type)) {
            $button_status = isset($settingData[$button_type]) ? $settingData[$button_type] : '1'; // 默认显示
            return json([
                'code' => 200, 
                'message' => '获取成功', 
                'data' => ['button_status' => $button_status]
            ]);
        }
        
        // 返回所有设置
        return json([
            'code' => 200, 
            'message' => '获取成功', 
            'data' => ['setting_data' => $settingData]
        ]);
    }
} 