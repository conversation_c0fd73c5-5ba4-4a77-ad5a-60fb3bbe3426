<?php

namespace app\controller;

use app\BaseController;
use think\facade\Db;  // 导入 Db 类
use think\facade\View;

class Index extends BaseController
{
    /**
     * 测试函数
     * @return [type] [description]
     */
    public function test2(){
        var_dump('嘿嘿,大傻瓜大娃啦啦');
    }

    public function test3(){
        var_dump('爱你薇薇酱❤️');
    }
    public function index()
    {
        return '<style>*{ padding: 0; margin: 0; }</style><iframe src="https://www.thinkphp.cn/welcome?version=' . \think\facade\App::version() . '" width="100%" height="100%" frameborder="0" scrolling="auto"></iframe>';
    }

    public function hello($name = 'ThinkPHP8')
    {
        return 'hello,' . $name;
    }
    public function test()
    {
        return 'hello,';
    }
    public function getUserById($id = 561)
    {
        // 使用数据库查询 user 表中 id 为 561 的数据
        $userData = Db::name('user')->where('id', $id)->find();

        // 检查查询结果并返回
        if ($userData) {
            return json($userData);  // 返回 JSON 格式的数据
        } else {
            return json(['message' => '未找到 id 为 ' . $id . ' 的用户记录']);
        }
    }
}
