<?php
namespace app\controller;

use app\BaseController;
use app\util\CosUtil;
use app\util\LogUtil;
use think\facade\Db;
use think\facade\Config;
use think\Request;
use think\response\Json;
use think\App;

/**
 * 微信公众号草稿生成控制器精简版
 * 功能：生成草稿HTML和图片处理核心功能
 */
class SimpleDraftGenerator extends BaseController
{
    // 微信公众号配置
    protected $appId = '';
    protected $appSecret = '';
    
    // 服务器地址配置
    protected $serverDomain = 'https://www.bjgaoxiaoshequ.store';
    
    // 消息配置
    protected $messageLimit = 10;
    protected $author = '月光下的温柔';
    protected $titleTemplate = '树洞最新消息 - {date}';
    protected $digest = '';
    
    // 样式配置
    protected $mainBgColor = '#e3f2fd';  // 浅色蓝色背景
    protected $titleBgGradient = ['#7D5A4F', '#A67F6D'];
    protected $buttonBgGradient = ['#1976d2', '#42a5f5'];  // 蓝色渐变按钮
    protected $textColor = '#5a4a42';
    
    // 封面图片配置
    protected $coverImagePath = '/uploads/cover3.jpg';
    
    /**
     * 构造函数，加载配置
     */
    public function __construct(App $app)
    {
        parent::__construct($app);

        // 加载公众号配置，从wechat.php配置文件获取
        $this->appId = Config::get('wechat.official_account.app_id', '');
        $this->appSecret = Config::get('wechat.official_account.secret', '');

        // 加载封面图片配置
        $this->coverImagePath = '/uploads/cover3.jpg';
    }

    /**
     * 统一的 URL 补全工具
     * 将相对路径转换为完整的 URL
     * 优先使用COS域名，如果不是COS路径则使用服务器域名
     *
     * @param string $path 相对路径或完整URL
     * @param string $bucketType COS存储桶类型，默认为'public'
     * @return string 完整的URL
     */
    protected function completeUrl(string $path, string $bucketType = 'public'): string
    {
        // 如果已经是完整的URL（包含http或https），直接返回
        if (preg_match('/^https?:\/\//', $path)) {
            return $path;
        }

        // 如果是空字符串，返回空
        if (empty($path)) {
            return '';
        }

        // 检查是否是COS路径（包含环境前缀的路径，如 dev/comment/2024/01/01/xxx.jpg 或 prod/comment/2024/01/01/xxx.jpg）
        if ($this->isCosPath($path)) {
            // 使用COS工具类生成URL
            return \app\util\CosUtil::generateUrl($path, $bucketType);
        }

        // 不是COS路径，使用服务器域名
        // 确保路径以 / 开头
        if (!str_starts_with($path, '/')) {
            $path = '/' . $path;
        }

        // 拼接完整URL
        return rtrim($this->serverDomain, '/') . $path;
    }

    /**
     * 判断是否是COS路径
     * COS路径格式：{env}/{fileType}/{date}/{filename}
     * 例如：dev/comment/2024/01/01/xxx.jpg 或 prod/comment/2024/01/01/xxx.jpg
     *
     * @param string $path 路径
     * @return bool
     */
    protected function isCosPath(string $path): bool
    {
        // 移除开头的斜杠
        $path = ltrim($path, '/');

        // COS路径通常以环境前缀开头：dev/ 或 prod/
        if (preg_match('/^(dev|prod)\/[a-zA-Z]+\/\d{4}\/\d{2}\/\d{2}\//', $path)) {
            return true;
        }

        // 也可能是没有环境前缀的旧格式COS路径
        if (preg_match('/^[a-zA-Z]+\/\d{4}\/\d{2}\/\d{2}\//', $path)) {
            return true;
        }

        return false;
    }

    /**
     * 批量处理图片URL补全
     *
     * @param array $images 图片数组
     * @param string $bucketType COS存储桶类型，默认为'public'
     * @param string $urlField URL字段名，默认为'url'
     * @return array 处理后的图片数组
     */
    protected function completeImageUrls(array $images, string $bucketType = 'public', string $urlField = 'url'): array
    {
        foreach ($images as &$image) {
            if (isset($image[$urlField])) {
                $image[$urlField] = $this->completeUrl($image[$urlField], $bucketType);
            }
        }
        return $images;
    }

    /**
     * 处理消息中的图片URL补全
     *
     * @param array &$message 消息数组（引用传递）
     */
    protected function processMessageImages(array &$message): void
    {
        // 根据消息类型判断存储桶类型，消息图片通常存储在公有存储桶
        $bucketType = $this->getBucketTypeForMessage($message);

        // 处理 square_content_images
        if (isset($message['square_content_images']) && is_array($message['square_content_images'])) {
            $message['square_content_images'] = $this->completeImageUrls($message['square_content_images'], $bucketType);
        }

        // 处理 content_images
        if (isset($message['content_images']) && is_array($message['content_images'])) {
            $message['content_images'] = $this->completeImageUrls($message['content_images'], $bucketType);
        }

        // 处理 square_images
        if (isset($message['square_images']) && is_array($message['square_images'])) {
            $message['square_images'] = $this->completeImageUrls($message['square_images'], $bucketType);
        }

        // 处理 processed_images
        if (isset($message['processed_images']) && is_array($message['processed_images'])) {
            $message['processed_images'] = $this->completeImageUrls($message['processed_images'], $bucketType);
        }

        // 处理 images 字段（如果是JSON字符串，先解析再处理）
        if (isset($message['images']) && is_string($message['images'])) {
            $images = json_decode($message['images'], true);
            if (is_array($images)) {
                $images = $this->completeImageUrls($images, $bucketType);
                $message['images'] = json_encode($images);
            }
        } elseif (isset($message['images']) && is_array($message['images'])) {
            $message['images'] = $this->completeImageUrls($message['images'], $bucketType);
        }
    }

    /**
     * 根据消息类型判断存储桶类型
     *
     * @param array $message 消息数组
     * @return string 存储桶类型
     */
    protected function getBucketTypeForMessage(array $message): string
    {
        // 消息图片通常存储在公有存储桶
        // 如果有特殊需求，可以根据消息的其他字段判断
        return 'public';
    }

    /**
     * 生成具有特定样式的文章内容
     *
     * @param array $messages 消息数组
     * @param string $qrcodeUrl 小程序码的URL
     * @param string $titleImageUrl 标题图片的URL
     * @param string $likeIconUrl 点赞图标的URL
     * @param string $commentIconUrl 评论图标的URL
     * @return string HTML格式的文章内容
     */
    public function generateStyledContent(array $messages, string $qrcodeUrl = '', string $titleImageUrl = '', string $likeIconUrl = '', string $commentIconUrl = ''): string
    {
        // 使用微信公众号支持的简单HTML结构
        $html = '';
        
        // 设置整体背景色 - 使用浅色蓝色背景，减少上下内边距让图片更贴近边缘
        $html .= '<section style="background-color: #e3f2fd; padding: 5px 0 10px;">';

        // 添加标题图片 - 使用上传到微信服务器的图片，贴近边缘显示
        if (!empty($titleImageUrl)) {
            $html .= '<section style="margin: 0 5px 10px; text-align: center;">';
            $html .= '<img src="' . $titleImageUrl . '" style="width: 100%; max-width: 100%; height: auto; border-radius: 5px;" />';
            $html .= '</section>';
        }

        // 消息列表
        $counter = 1; // 用于记录显示的消息编号
        foreach ($messages as $message) {
            $html .= $this->formatMessageItem($message, $counter, $likeIconUrl, $commentIconUrl);
            $counter++;
        }
        
        // 底部引导区域 - 使用上传到微信的小程序码
        $html .= '<section style="margin: 20px 15px; text-align: center; background: #fff; padding: 20px; border-radius: 10px; box-shadow: 0 1px 4px rgba(0,0,0,0.05);">';
        $html .= '<p style="font-size: 15px; color: #5D4037; margin-bottom: 12px; font-weight: bold;">扫码进入树洞消息推送群</p>';

        // 使用微信服务器上的小程序码图片
        if (!empty($qrcodeUrl)) {
            $html .= '<p style="text-align:center">';
            $html .= '<img src="' . $qrcodeUrl . '" style="width: 140px; height: 140px;" />';
            $html .= '</p>';
        } else {
            // 备用方案，使用原始URL
            $html .= '<p style="text-align:center"><img src="https://www.bjgaoxiaoshequ.store/uploads/小程序码.jpg" style="width:140px;" /></p>';
        }
        
        $html .= '<p style="font-size: 13px; color: #8D6E63; margin-top: 12px;">浏览更多内容，参与校园互动</p>';
        $html .= '</section>';

        // 结束整体背景区域
        $html .= '</section>';
        
        return $html;
    }
    
    /**
     * 格式化单个消息项，使用方形图片展示，点击可查看原图
     * @param array $message 消息数据
     * @param int $index 序号
     * @param string $likeIconUrl 点赞图标的URL
     * @param string $commentIconUrl 评论图标的URL
     * @return string 格式化后的HTML
     */
    public function formatMessageItem(array $message, int $index, string $likeIconUrl = '', string $commentIconUrl = ''): string
    {
        // 格式化发布时间 - 月日时分
        $time = isset($message['formatted_time']) ? $message['formatted_time'] : '';
        
        // 处理月份开头的0，例如将05月改为5月
        if (!empty($time)) {
            $time = preg_replace('/^0(\d+)月/', '$1月', $time);
        }
        
        // 处理内容，防止XSS
        $content = htmlspecialchars($message['content']);
        
        // 限制文本内容长度，超过60个字符显示省略号
        if (mb_strlen($content, 'UTF-8') > 60) {
            $content = mb_substr($content, 0, 60, 'UTF-8') . '...';
        }
        
        // 小程序AppID和页面路径
        $appId = 'wx13d6e0dee303467f';
        $path = 'packageEmoji/pages/messageDetail/messageDetail?id=' . $message['id'];
        
        // 用户名和标题，如果有头衔则显示
        $titleName = isset($message['titlename']) && !empty($message['titlename']) ? $message['titlename'] : '';
        if (empty($titleName) && isset($message['user_title']) && !empty($message['user_title'])) {
            $titleName = $message['user_title'];
        }
        
        // 评论数
        $commentCount = isset($message['total_pinglun']) ? $message['total_pinglun'] : 0;
        if ($commentCount == 0 && isset($message['comment_count'])) {
            $commentCount = $message['comment_count'];
        }
        
        // 点赞数
        $likeCount = isset($message['total_likes']) ? $message['total_likes'] : 0;
        
        // 使用微信公众号支持的简单格式，两侧露出背景
        $html = '<section style="margin: 0 15px 16px; background: #fff; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 6px rgba(0,0,0,0.08);">';

        // 内容区域 - 直接开始内容，去掉用户信息区域
        $html .= '<section style="padding: 15px; word-break: break-all;">';
        $html .= '<p style="margin: 0; font-size: 17px; line-height: 1.6; color: #333;">' . $content . '</p>';
        
        // 处理图片 - 确保图片容器是完全的正方形，并且任何比例的图片都显示为方形
        $hasImages = false;
        $imageCount = 0;
        $squareImageUrls = [];
        $originalImageUrls = [];
        $squareMediaIds = [];
        $originalMediaIds = [];
        
        // 获取图片信息 - 优先使用方形裁剪图片显示，点击时链接到原图
        if (!empty($message['square_content_images']) && !empty($message['content_images']) && 
            is_array($message['square_content_images']) && is_array($message['content_images'])) {
            $hasImages = true;
            
            // 收集方形图片和原始图片URL，并补全为完整URL
            $bucketType = $this->getBucketTypeForMessage($message);
            foreach ($message['square_content_images'] as $index => $square) {
                if (!empty($square['url']) && isset($message['content_images'][$index]) && !empty($message['content_images'][$index]['url'])) {
                    $squareImageUrls[] = $this->completeUrl($square['url'], $bucketType);
                    $originalImageUrls[] = $this->completeUrl($message['content_images'][$index]['url'], $bucketType);
                }
            }
            $imageCount = count($squareImageUrls);
        }
        elseif (!empty($message['square_images']) && !empty($message['processed_images']) && 
                is_array($message['square_images']) && is_array($message['processed_images'])) {
            $hasImages = true;
            
            // 收集方形图片和原始图片media_id
            foreach ($message['square_images'] as $index => $square) {
                if (!empty($square['media_id']) && isset($message['processed_images'][$index]) && !empty($message['processed_images'][$index]['media_id'])) {
                    $squareMediaIds[] = $square['media_id'];
                    $originalMediaIds[] = $message['processed_images'][$index]['media_id'];
                }
            }
            $imageCount = count($squareMediaIds);
        }
        elseif (!empty($message['processed_images']) && is_array($message['processed_images'])) {
            // 兼容旧版本，仅使用原图
            $hasImages = true;
            foreach ($message['processed_images'] as $img) {
                if (!empty($img['media_id'])) {
                    $originalMediaIds[] = $img['media_id'];
                }
            }
            $imageCount = count($originalMediaIds);
            $squareMediaIds = $originalMediaIds; // 没有方形图，使用原图
        }
        elseif (!empty($message['content_images']) && is_array($message['content_images'])) {
            // 兼容旧版本，仅使用原图URL
            $hasImages = true;
            foreach ($message['content_images'] as $img) {
                if (!empty($img['url'])) {
                    $originalImageUrls[] = $img['url'];
                }
            }
            $imageCount = count($originalImageUrls);
            $squareImageUrls = $originalImageUrls; // 没有方形图，使用原图
        }
        elseif (!empty($message['images'])) {
            // 尝试解析JSON格式的图片字段 (兼容最原始的方式)
            $imagesArray = json_decode($message['images'], true);
            if (is_array($imagesArray) && !empty($imagesArray)) {
                $hasImages = true;
                $originalImageUrls = $imagesArray;
                $squareImageUrls = $imagesArray; // 没有方形图，使用原图
                $imageCount = count($originalImageUrls);
            }
        }
        
        // 如果有图片，显示图片网格
        if ($hasImages && $imageCount > 0) {
            $html .= '<div style="margin-top: 10px;">';
            
            // 限制显示最多3张图片，即使实际有更多
            $displayCount = min($imageCount, 3);
            
            // 根据图片数量决定布局
            if ($displayCount == 1) {
                // 单图布局 - 使用方形图片显示，点击查看原图
                if (!empty($squareImageUrls) && !empty($originalImageUrls)) {
                    // 使用图片URL方式 (用于图文消息内容)
                    $html .= '<div style="width: 70%; height: 0; padding-bottom: 70%; margin: 0 auto; position: relative; overflow: hidden; border-radius: 8px; background-color: #f2f2f2;">';
                    $html .= '<a href="' . $originalImageUrls[0] . '" target="_blank">';
                    $html .= '<img src="' . $squareImageUrls[0] . '" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;" />';
                    $html .= '</a>';
                    $html .= '</div>';
                } elseif (!empty($squareMediaIds) && !empty($originalMediaIds)) {
                    // 使用media_id方式 (用于引用永久素材)
                    $html .= '<div style="width: 70%; height: 0; padding-bottom: 70%; margin: 0 auto; position: relative; overflow: hidden; border-radius: 8px; background-color: #f2f2f2;">';
                    // 显示方形图片，但在微信文章中无法直接链接查看原图，所以仅使用方形图片展示
                    $html .= '<img src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==" data-media-id="' . $squareMediaIds[0] . '" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; visibility: visible !important;" />';
                    $html .= '</div>';
                }
            } else {
                // 多图网格布局 - 使用方形图片，每个点击后查看对应原图
                $html .= '<table style="width: 100%; border-collapse: separate; border-spacing: 3px; margin: 0; padding: 0;">';
                $html .= '<tr>';
                
                // 计算每个图片容器的宽度百分比
                $cellWidth = (100 / 3) - 1.5; // 留一点间距
                
                for ($i = 0; $i < $displayCount; $i++) {
                    $html .= '<td style="width: ' . $cellWidth . '%; padding: 0; vertical-align: top;">';
                    
                    // 创建正方形容器 - 强制使用padding-bottom确保正方形
                    $html .= '<div style="width: 100%; height: 0; padding-bottom: 100%; position: relative; overflow: hidden; border-radius: 6px; background-color: #f2f2f2;">';
                    
                    // 根据是否有URL或media_id决定使用哪种方式显示图片
                    if (!empty($squareImageUrls) && isset($squareImageUrls[$i]) && !empty($originalImageUrls) && isset($originalImageUrls[$i])) {
                        // 使用URL方式，点击查看原图
                        $html .= '<a href="' . $originalImageUrls[$i] . '" target="_blank">';
                        $html .= '<img src="' . $squareImageUrls[$i] . '" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;" />';
                        $html .= '</a>';
                    } elseif (!empty($squareMediaIds) && isset($squareMediaIds[$i])) {
                        // 使用media_id方式，仅显示方形图片
                        $html .= '<img src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==" data-media-id="' . $squareMediaIds[$i] . '" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; visibility: visible !important;" />';
                    }
                    
                    $html .= '</div>';
                    $html .= '</td>';
                }
                
                // 如果显示的图片少于3张，填充空白单元格保持布局一致
                for ($i = $displayCount; $i < 3; $i++) {
                    $html .= '<td style="width: ' . $cellWidth . '%;"></td>';
                }
                
                $html .= '</tr>';
                $html .= '</table>';
                
                // 如果图片总数超过3张，显示总数信息
                if ($imageCount > 3) {
                    $html .= '<p style="text-align: right; color: #999; font-size: 12px; margin: 3px 0 0;">共' . $imageCount . '张</p>';
                }
            }
            
            $html .= '</div>';
        }
        
        $html .= '</section>';
        
        // 底部信息栏 - 更紧凑的布局，减小内边距，使用浅蓝色背景
        $html .= '<section style="padding: 8px 15px; background: #f0f8ff; border-top: 1px solid #e3f2fd; font-size: 12px; color: #888; display: flex; justify-content: space-between; align-items: center;">';
        
        // 左侧基本信息
        $html .= '<div style="flex: 1;">';

        // 评论图标和数量
        if (!empty($commentIconUrl)) {
            $html .= '<span style="margin-right: 10px;"><img src="' . $commentIconUrl . '" style="width: 16px; height: 16px; vertical-align: middle; margin-right: 2px;" /> ' . $commentCount . '</span>';
        } else {
            $html .= '<span style="margin-right: 10px;"><i style="font-size: 16px;">💬</i> ' . $commentCount . '</span>';
        }

        // 点赞图标和数量
        if (!empty($likeIconUrl)) {
            $html .= '<span style="margin-right: 10px;"><img src="' . $likeIconUrl . '" style="width: 16px; height: 16px; vertical-align: middle; margin-right: 2px;" /> ' . $likeCount . '</span>';
        } else {
            $html .= '<span style="margin-right: 10px;"><i style="font-size: 16px;">👍</i> ' . $likeCount . '</span>';
        }

        $html .= '</div>';
        
        // 右侧按钮 - 调整为适中大小
        $html .= '<div>';
        $html .= '<a class="weapp_text_link" data-miniprogram-appid="' . $appId . '" ';
        $html .= 'data-miniprogram-path="' . $path . '" ';
        $html .= 'style="display: inline-block; color: #fff; background: linear-gradient(to right, #1976d2, #42a5f5); padding: 4px 10px; border-radius: 10px; font-size: 11px; text-decoration: none; line-height: 1.3; box-shadow: 0 2px 4px rgba(25,118,210,0.3);">';
        $html .= '查看详情</a>';
        $html .= '</div>';
        
        $html .= '</section>';
        $html .= '</section>';
        
        return $html;
    }

    /**
     * 通过URL上传图片并存储为永久素材
     *
     * @param string $imageUrl 图片URL
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回media_id，失败返回空字符串
     */
    public function uploadImageAsPermanentMaterial(string $imageUrl, string $accessToken): string
    {
        // 从URL获取文件扩展名
        $pathInfo = pathinfo($imageUrl);
        $extension = isset($pathInfo['extension']) ? strtolower($pathInfo['extension']) : 'jpg';

        // 确保扩展名是微信支持的
        if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            $extension = 'jpg';  // 默认使用jpg
        }

        // 下载图片到临时文件 - 确保包含正确的扩展名
        $tempFilePath = tempnam(sys_get_temp_dir(), 'wx_img');
        $tempFilePathWithExt = $tempFilePath . '.' . $extension;
        rename($tempFilePath, $tempFilePathWithExt); // 重命名临时文件以包含扩展名

        // 下载图片内容
        $imageData = @file_get_contents($imageUrl);
        if (!$imageData) {
            return '';
        }

        // 保存图片到临时文件
        file_put_contents($tempFilePathWithExt, $imageData);

        // 获取图片MIME类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $tempFilePathWithExt);
        finfo_close($finfo);

        // 确保MIME类型满足微信要求
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($mimeType, $allowedMimeTypes)) {
            @unlink($tempFilePathWithExt);
            return '';
        }

        // 获取图片文件大小
        $fileSize = filesize($tempFilePathWithExt);

        // 检查文件大小是否超过微信限制 (2MB)
        if ($fileSize > 2 * 1024 * 1024) {
            @unlink($tempFilePathWithExt);
            return '';
        }

        // 使用永久素材接口上传图片
        $mediaId = $this->uploadLocalImageAsPermanentMaterial($tempFilePathWithExt, $accessToken);

        // 删除临时文件
        @unlink($tempFilePathWithExt);

        return $mediaId;
    }

    /**
     * 将本地图片文件上传为微信永久素材
     *
     * @param string $localPath 本地图片路径
     * @param string $accessToken 微信AccessToken
     * @return string|null 成功返回media_id，失败返回null
     */
    public function uploadLocalImageAsPermanentMaterial(string $localPath, string $accessToken): ?string
    {
        // 检查文件是否存在
        if (!file_exists($localPath)) {
            return null;
        }

        // 获取图片MIME类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $localPath);
        finfo_close($finfo);

        // 确保MIME类型满足微信要求
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($mimeType, $allowedMimeTypes)) {
            return null;
        }

        // 检查文件大小
        $fileSize = filesize($localPath);

        // 检查文件大小是否超过微信限制 (2MB)
        if ($fileSize > 2 * 1024 * 1024) {
            return null;
        }

        // 使用永久素材接口上传图片
        $url = "https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={$accessToken}&type=image";

        // 获取文件后缀名
        $fileExt = strtolower(pathinfo($localPath, PATHINFO_EXTENSION));
        if (empty($fileExt) || !in_array($fileExt, ['jpg', 'jpeg', 'png', 'gif'])) {
            // 根据MIME类型设置正确的扩展名
            if ($mimeType == 'image/jpeg') {
                $fileExt = 'jpg';
            } elseif ($mimeType == 'image/png') {
                $fileExt = 'png';
            } elseif ($mimeType == 'image/gif') {
                $fileExt = 'gif';
            } else {
                $fileExt = 'jpg';
            }

            // 创建一个新的临时文件，确保包含正确的扩展名
            $newPath = $localPath . '.' . $fileExt;
            copy($localPath, $newPath);
            $localPath = $newPath;
            $isTemporaryFile = true;
        }

        $curl = curl_init();

        $data = [
            'media' => new \CURLFile($localPath, $mimeType, 'image.' . $fileExt)
        ];

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        if ($httpCode !== 200 || curl_errno($curl)) {
            curl_close($curl);

            // 删除临时创建的文件
            if (isset($isTemporaryFile) && $isTemporaryFile && file_exists($localPath)) {
                @unlink($localPath);
            }

            return null;
        }

        curl_close($curl);

        // 删除临时创建的文件
        if (isset($isTemporaryFile) && $isTemporaryFile && file_exists($localPath)) {
            @unlink($localPath);
        }

        // 解析响应内容
        $result = json_decode($response, true);
        if (isset($result['media_id'])) {
            return $result['media_id'];
        } else {
            return null;
        }
    }

    /**
     * 上传图片到微信服务器(用于图文消息正文)
     *
     * @param string $imagePath 图片路径
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回图片URL，失败返回空字符串
     */
    public function uploadImageForNewsContent(string $imagePath, string $accessToken): string
    {
        try {
            // 检查文件是否存在
            if (!file_exists($imagePath)) {
                return '';
            }

            // 获取图片MIME类型
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $imagePath);
            finfo_close($finfo);

            // 检查MIME类型
            $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!in_array($mimeType, $allowedMimeTypes)) {
                return '';
            }

            // 检查文件大小
            $fileSize = filesize($imagePath);

            // 对大图片进行预处理 - 超过1MB的图片先压缩
            $processedPath = $imagePath;
            $needCleanup = false;

            if ($fileSize > 1024 * 1024) { // 1MB
                // 尝试使用Imagick压缩(如果可用)
                if (extension_loaded('imagick')) {
                    try {
                        $imagick = new \Imagick($imagePath);

                        // 如果图片尺寸过大，先缩小
                        $width = $imagick->getImageWidth();
                        $height = $imagick->getImageHeight();
                        if ($width > 1200 || $height > 1200) {
                            $ratio = $width / $height;
                            if ($ratio > 1) {
                                $newWidth = min(1200, $width);
                                $newHeight = $newWidth / $ratio;
                            } else {
                                $newHeight = min(1200, $height);
                                $newWidth = $newHeight * $ratio;
                            }
                            $imagick->resizeImage($newWidth, $newHeight, \Imagick::FILTER_LANCZOS, 1);
                        }

                        // 设置压缩质量
                        $imagick->setImageCompressionQuality(75);

                        // 保存结果
                        $processedPath = tempnam(sys_get_temp_dir(), 'news_') . '.' . pathinfo($imagePath, PATHINFO_EXTENSION);
                        $imagick->writeImage($processedPath);
                        $imagick->clear();
                        $imagick->destroy();

                        $needCleanup = true;
                    } catch (\Exception $e) {
                        // Imagick失败，使用原图
                    }
                }

                // 如果Imagick失败或不可用，尝试GD库压缩
                if ($processedPath === $imagePath || !file_exists($processedPath)) {
                    $processedPath = $this->compressImageFile($imagePath, 1024 * 1024);
                    if (!empty($processedPath)) {
                        $needCleanup = true;
                    } else {
                        // 压缩失败，使用原图，但可能会导致上传失败或缓慢
                        $processedPath = $imagePath;
                    }
                }
            }

            // 构建上传URL - 使用正确的图文消息图片上传接口
            $url = "https://api.weixin.qq.com/cgi-bin/media/uploadimg?access_token={$accessToken}";

            // 准备要上传的文件
            $data = [];
            $data['media'] = new \CURLFile($processedPath, $mimeType, basename($processedPath));

            // 发送POST请求，添加超时设置
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 30秒超时
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 10秒连接超时

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            // 清理临时文件
            if ($needCleanup && $processedPath !== $imagePath && file_exists($processedPath)) {
                @unlink($processedPath);
            }

            if ($error) {
                return '';
            }

            // 解析响应
            $result = json_decode($response, true);
            if (!$result) {
                return '';
            }

            if (isset($result['errcode']) && $result['errcode'] != 0) {
                return '';
            }

            // 返回图片URL
            $imageUrl = $result['url'] ?? '';
            if (!empty($imageUrl)) {
                return $imageUrl;
            }

            return '';

        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * 裁剪图片为正方形
     *
     * @param string $imagePath 图片路径
     * @return string|null 处理后的图片路径，失败则返回null
     */
    public function cropImageToSquare(string $imagePath): ?string
    {
        try {
            // 检查文件存在
            if (!file_exists($imagePath)) {
                return null;
            }

            // 获取文件大小
            $fileSize = filesize($imagePath);

            // 根据图片大小选择不同的处理方式
            if ($fileSize > 2 * 1024 * 1024) { // 大于2MB的图片
                // 优先使用Imagick处理大图
                if (extension_loaded('imagick')) {
                    try {
                        $imagick = new \Imagick($imagePath);

                        // 获取原始尺寸
                        $origWidth = $imagick->getImageWidth();
                        $origHeight = $imagick->getImageHeight();

                        // 计算裁剪区域
                        $size = min($origWidth, $origHeight);
                        $x = (int)(($origWidth - $size) / 2);
                        $y = (int)(($origHeight - $size) / 2);

                        // 裁剪成正方形
                        $imagick->cropImage($size, $size, $x, $y);

                        // 调整大小到目标尺寸
                        $targetSize = 600;
                        $imagick->resizeImage($targetSize, $targetSize, \Imagick::FILTER_LANCZOS, 1);

                        // 设置压缩质量
                        $imagick->setImageCompressionQuality(75);

                        // 保存结果
                        $output = tempnam(sys_get_temp_dir(), 'squ') . '.' . pathinfo($imagePath, PATHINFO_EXTENSION);
                        $imagick->writeImage($output);
                        $imagick->clear();
                        $imagick->destroy();

                        if (file_exists($output) && filesize($output) > 0) {
                            return $output;
                        }
                    } catch (\Exception $e) {
                        // Imagick失败，使用其他方法
                    }
                }

                // 如果Imagick失败，使用快速处理模式
                return $this->fastResizeImage($imagePath);
            } else if ($fileSize > 500 * 1024) { // 500KB-2MB的图片
                // 中等大小图片使用简单处理方法
                return $this->simpleSquareImage($imagePath);
            } else {
                // 小图片直接使用简单处理
                return $this->simpleSquareImage($imagePath);
            }

        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 简单的图片处理方法 - 将任何图片处理为方形
     *
     * @param string $imagePath 图片路径
     * @return string|null 处理后的图片路径，失败则返回null
     */
    public function simpleSquareImage(string $imagePath): ?string
    {
        try {
            // 如果装载了 Imagick，则优先使用
            if (extension_loaded('imagick')) {
                try {
                    $imagick = new \Imagick($imagePath);

                    // 获取原始尺寸
                    $origWidth = $imagick->getImageWidth();
                    $origHeight = $imagick->getImageHeight();

                    // 计算裁剪区域
                    $size = min($origWidth, $origHeight);
                    $x = (int)(($origWidth - $size) / 2);
                    $y = (int)(($origHeight - $size) / 2);

                    // 裁剪成正方形
                    $imagick->cropImage($size, $size, $x, $y);

                    // 调整大小到目标尺寸
                    $targetSize = 600;
                    $imagick->resizeImage($targetSize, $targetSize, \Imagick::FILTER_LANCZOS, 1);

                    // 设置压缩质量
                    $imagick->setImageCompressionQuality(80);

                    // 保存结果
                    $output = tempnam(sys_get_temp_dir(), 'squ') . '.' . pathinfo($imagePath, PATHINFO_EXTENSION);
                    $imagick->writeImage($output);
                    $imagick->clear();
                    $imagick->destroy();

                    if (file_exists($output) && filesize($output) > 0) {
                        return $output;
                    } else {
                        // Imagick失败，尝试GD库
                    }
                } catch (\Exception $e) {
                    // Imagick失败，尝试使用GD库
                }
            }

            // 获取基本信息
            $info = @getimagesize($imagePath);
            if (!$info) {
                return null;
            }
            list($width, $height, $type) = $info;

            // 设置目标尺寸
            $targetSize = 600;

            // 计算裁剪区域
            $size = min($width, $height);
            $x = (int)(($width - $size) / 2);
            $y = (int)(($height - $size) / 2);

            // 加载源图像
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $source = @imagecreatefromjpeg($imagePath);
                    break;
                case IMAGETYPE_PNG:
                    $source = @imagecreatefrompng($imagePath);
                    break;
                case IMAGETYPE_GIF:
                    $source = @imagecreatefromgif($imagePath);
                    break;
                default:
                    return null;
            }
            if (!$source) {
                return null;
            }

            // 使用 imagecrop 进行裁剪
            $rect = ['x' => $x, 'y' => $y, 'width' => $size, 'height' => $size];
            $cropped = imagecrop($source, $rect);
            imagedestroy($source);
            if ($cropped === false) {
                return null;
            }

            // 使用 imagescale 进行缩放
            $scaled = imagescale($cropped, $targetSize, $targetSize, IMG_BILINEAR_FIXED);
            imagedestroy($cropped);
            if ($scaled === false) {
                return null;
            }

            // 保存结果
            $extensions = [IMAGETYPE_JPEG => 'jpg', IMAGETYPE_PNG => 'png', IMAGETYPE_GIF => 'gif'];
            $ext = $extensions[$type] ?? 'jpg';
            $output = tempnam(sys_get_temp_dir(), 'square_') . '.' . $ext;

            switch ($type) {
                case IMAGETYPE_JPEG:
                    $success = imagejpeg($scaled, $output, 80);
                    break;
                case IMAGETYPE_PNG:
                    $success = imagepng($scaled, $output, 5);
                    break;
                case IMAGETYPE_GIF:
                    $success = imagegif($scaled, $output);
                    break;
            }
            imagedestroy($scaled);
            if (empty($success)) {
                @unlink($output);
                return null;
            }
            return $output;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 快速处理大尺寸图片为方形
     * 使用GD函数库的快速模式，避免处理过程中内存溢出
     *
     * @param string $imagePath 图片路径
     * @return string|null 处理后的图片路径
     */
    public function fastResizeImage(string $imagePath): ?string
    {
        try {
            // 检查文件存在
            if (!file_exists($imagePath)) {
                return null;
            }

            // 获取图片类型
            $imageInfo = @getimagesize($imagePath);
            if (!$imageInfo) {
                return null;
            }

            $width = $imageInfo[0];
            $height = $imageInfo[1];
            $type = $imageInfo[2];

            // 确定输出类型和目标尺寸
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $extension = 'jpg';
                    break;
                case IMAGETYPE_PNG:
                    $extension = 'png';
                    break;
                case IMAGETYPE_GIF:
                    $extension = 'gif';
                    break;
                default:
                    return null;
            }

            // 最大处理尺寸 - 直接设置较小的尺寸
            $targetSize = 600; // 600x600像素的方形

            // 如果装载了 Imagick，优先使用 Imagick（处理大图效率更高）
            if (extension_loaded('imagick')) {
                try {
                    $imagick = new \Imagick($imagePath);

                    // 计算裁剪区域
                    $size = min($width, $height);
                    $x = (int)(($width - $size) / 2);
                    $y = (int)(($height - $size) / 2);

                    // 裁剪成正方形
                    $imagick->cropImage($size, $size, $x, $y);

                    // 调整大小到目标尺寸
                    $imagick->resizeImage($targetSize, $targetSize, \Imagick::FILTER_LANCZOS, 1);

                    // 设置压缩质量
                    $imagick->setImageCompressionQuality(75);

                    // 保存结果
                    $output = tempnam(sys_get_temp_dir(), 'fast_') . '.' . $extension;
                    $imagick->writeImage($output);
                    $imagick->clear();
                    $imagick->destroy();

                    if (file_exists($output) && filesize($output) > 0) {
                        return $output;
                    }
                } catch (\Exception $e) {
                    // Imagick失败，继续使用GD
                }
            }

            // 使用GD库快速处理大图片

            // 创建一个小的临时图片，先直接加载为小尺寸可以减少内存占用
            $tempImage = imagecreatetruecolor($targetSize, $targetSize);

            // 根据图片类型创建源图像，使用低质量模式
            $source = null;
            switch ($type) {
                case IMAGETYPE_JPEG:
                    // 使用低质量加载原图，减少内存占用
                    $source = @imagecreatefromjpeg($imagePath);
                    break;
                case IMAGETYPE_PNG:
                    // 对PNG处理透明度
                    $source = @imagecreatefrompng($imagePath);
                    if ($source) {
                        imagealphablending($tempImage, false);
                        imagesavealpha($tempImage, true);
                        $transparent = imagecolorallocatealpha($tempImage, 255, 255, 255, 127);
                        imagefilledrectangle($tempImage, 0, 0, $targetSize, $targetSize, $transparent);
                    }
                    break;
                case IMAGETYPE_GIF:
                    $source = @imagecreatefromgif($imagePath);
                    break;
            }

            if (!$source) {
                imagedestroy($tempImage);
                return null;
            }

            // 确定裁剪参数（从中心裁剪）
            $size = min($width, $height);
            $x = ($width - $size) / 2;
            $y = ($height - $size) / 2;

            // 一步裁剪并缩放
            if (!imagecopyresampled($tempImage, $source, 0, 0, $x, $y, $targetSize, $targetSize, $size, $size)) {
                imagedestroy($source);
                imagedestroy($tempImage);
                return null;
            }

            // 释放原图资源
            imagedestroy($source);

            // 保存处理后的图片
            $outputPath = tempnam(sys_get_temp_dir(), 'fast_') . '.' . $extension;
            $success = false;

            switch ($type) {
                case IMAGETYPE_JPEG:
                    // 使用较低的质量
                    $success = imagejpeg($tempImage, $outputPath, 60);
                    break;
                case IMAGETYPE_PNG:
                    // 使用较高的压缩比
                    $success = imagepng($tempImage, $outputPath, 6);
                    break;
                case IMAGETYPE_GIF:
                    $success = imagegif($tempImage, $outputPath);
                    break;
            }

            // 释放资源
            imagedestroy($tempImage);

            if (!$success) {
                @unlink($outputPath);
                return null;
            }

            return $outputPath;

        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 压缩图片文件到指定大小以下
     *
     * @param string $imagePath 图片路径
     * @param int $maxSize 最大文件大小（字节）
     * @return string|null 压缩后的图片路径，失败则返回null
     */
    public function compressImageFile(string $imagePath, int $maxSize): ?string
    {
        try {
            // 检查文件是否存在
            if (!file_exists($imagePath)) {
                return null;
            }

            // 获取图片信息
            $imageInfo = @getimagesize($imagePath);
            if (!$imageInfo) {
                return null;
            }

            $width = $imageInfo[0];
            $height = $imageInfo[1];
            $type = $imageInfo[2];

            // 创建原始图片资源
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $source = imagecreatefromjpeg($imagePath);
                    $extension = 'jpg';
                    break;
                case IMAGETYPE_PNG:
                    $source = imagecreatefrompng($imagePath);
                    $extension = 'png';
                    break;
                case IMAGETYPE_GIF:
                    $source = imagecreatefromgif($imagePath);
                    $extension = 'gif';
                    break;
                default:
                    return null;
            }

            if (!$source) {
                return null;
            }

            // 创建输出文件路径
            $outputPath = tempnam(sys_get_temp_dir(), 'comp_') . '.' . $extension;

            // 使用JPEG格式时可以调整质量
            if ($type == IMAGETYPE_JPEG) {
                // 尝试不同的质量级别，从75开始
                $quality = 75;

                // 先尝试不缩放，只降低质量
                $scaled = imagecreatetruecolor($width, $height);
                imagecopyresampled($scaled, $source, 0, 0, 0, 0, $width, $height, $width, $height);
                imagejpeg($scaled, $outputPath, $quality);
                imagedestroy($scaled);

                // 如果文件仍然太大，尝试进一步降低质量
                if (filesize($outputPath) > $maxSize && $quality > 40) {
                    // 再次尝试更低的质量
                    $quality = 40;
                    $scaled = imagecreatetruecolor($width, $height);
                    imagecopyresampled($scaled, $source, 0, 0, 0, 0, $width, $height, $width, $height);
                    imagejpeg($scaled, $outputPath, $quality);
                    imagedestroy($scaled);
                }

                // 如果还是太大，尝试缩小尺寸
                if (filesize($outputPath) > $maxSize) {
                    // 缩小50%并使用较低质量
                    $newWidth = (int)($width * 0.5);
                    $newHeight = (int)($height * 0.5);
                    $scaled = imagecreatetruecolor($newWidth, $newHeight);
                    imagecopyresampled($scaled, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
                    imagejpeg($scaled, $outputPath, 50);
                    imagedestroy($scaled);
                }
            }
            // PNG格式，尝试降低压缩级别
            else if ($type == IMAGETYPE_PNG) {
                // 先尝试压缩级别7（中等压缩）
                $compressionLevel = 7;
                $scaled = imagecreatetruecolor($width, $height);

                // 保持透明
                imagealphablending($scaled, false);
                imagesavealpha($scaled, true);

                imagecopyresampled($scaled, $source, 0, 0, 0, 0, $width, $height, $width, $height);
                imagepng($scaled, $outputPath, $compressionLevel);
                imagedestroy($scaled);

                // 如果文件仍然太大，尝试缩小尺寸
                if (filesize($outputPath) > $maxSize) {
                    $newWidth = (int)($width * 0.7);
                    $newHeight = (int)($height * 0.7);
                    $scaled = imagecreatetruecolor($newWidth, $newHeight);

                    // 保持透明
                    imagealphablending($scaled, false);
                    imagesavealpha($scaled, true);

                    imagecopyresampled($scaled, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
                    imagepng($scaled, $outputPath, $compressionLevel);
                    imagedestroy($scaled);
                }
            }
            // GIF格式，只能缩小尺寸
            else if ($type == IMAGETYPE_GIF) {
                $newWidth = (int)($width * 0.7);
                $newHeight = (int)($height * 0.7);
                $scaled = imagecreatetruecolor($newWidth, $newHeight);
                imagecopyresampled($scaled, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
                imagegif($scaled, $outputPath);
                imagedestroy($scaled);
            }

            // 释放原图资源
            imagedestroy($source);

            $finalSize = filesize($outputPath);

            if ($finalSize <= 0) {
                @unlink($outputPath);
                return null;
            }

            return $outputPath;

        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 获取微信公众号的AccessToken
     *
     * @return string 成功返回AccessToken，失败返回空字符串
     */
    public function getAccessToken(): string
    {
        try {
            // 构建获取AccessToken的URL
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appId}&secret={$this->appSecret}";

            // 发送GET请求
            $response = file_get_contents($url);
            if (!$response) {
                return '';
            }

            // 解析响应
            $result = json_decode($response, true);
            if (!$result || isset($result['errcode'])) {
                return '';
            }

            // 返回AccessToken
            return $result['access_token'] ?? '';

        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * 获取每天热帖（过去24小时的热度排序消息）
     *
     * @param int $limit 获取消息的数量限制
     * @return array 消息数组
     */
    public function getLatestMessages(int $limit = 15): array
    {
        $startTime = microtime(true);

        try {
            // 计算7天前的时间戳（扩大范围以确保能获取到消息）
            $oneWeek = time() - 7 * 24 * 3600; // 7天

            // 记录查询开始日志
            LogUtil::business('SimpleDraftGenerator', '开始查询消息数据', [
                'limit' => $limit,
                'time_range' => [
                    'start' => date('Y-m-d H:i:s', $oneWeek),
                    'end' => date('Y-m-d H:i:s'),
                    'start_timestamp' => $oneWeek,
                    'end_timestamp' => time()
                ]
            ]);

            // 构建查询SQL - 查询7天内的消息，通过choose字段过滤已删除的消息
            $sql = "SELECT * FROM `message` WHERE `choose` < 100 AND `send_timestamp` > {$oneWeek}";

            // 执行查询
            $queryStart = microtime(true);
            $result = Db::query($sql);
            $queryDuration = microtime(true) - $queryStart;

            LogUtil::database('SELECT', 'message', [
                'conditions' => 'choose < 100 AND send_timestamp > ' . $oneWeek,
                'result_count' => count($result)
            ], $queryDuration, $sql);

            // 如果没有数据，尝试查询更大范围
            if (empty($result)) {
                LogUtil::warning('SimpleDraftGenerator: 7天内无数据，尝试查询30天内数据');

                $oneMonth = time() - 30 * 24 * 3600; // 30天
                $sql = "SELECT * FROM `message` WHERE `choose` < 100 AND `send_timestamp` > {$oneMonth}";
                $result = Db::query($sql);

                LogUtil::database('SELECT', 'message', [
                    'fallback_query' => '30天内数据',
                    'result_count' => count($result)
                ], 0, $sql);

                // 如果还是没有数据，查询所有未删除的消息
                if (empty($result)) {
                    LogUtil::warning('SimpleDraftGenerator: 30天内无数据，查询所有未删除消息');

                    $sql = "SELECT * FROM `message` WHERE `choose` < 100 ORDER BY `send_timestamp` DESC LIMIT " . ($limit * 2);
                    $result = Db::query($sql);

                    LogUtil::database('SELECT', 'message', [
                        'fallback_query' => '所有未删除消息',
                        'result_count' => count($result)
                    ], 0, $sql);
                }

                if (empty($result)) {
                    LogUtil::error('SimpleDraftGenerator: 所有查询都返回空结果', [
                        'attempted_queries' => ['7天内', '30天内', '所有未删除']
                    ]);
                    return [];
                }
            }

            // 处理每条消息，并计算热度分数
            foreach ($result as &$message) {
                // 格式化时间
                $timestamp = $message['send_timestamp'] ?? time();
                $message['formatted_time'] = date('m-d H:i', $timestamp);

                // 获取评论和回复数量
                $commentCount = Db::name('comment')
                    ->where('message_id', $message['id'])
                    ->where('is_deleted', 0)
                    ->count();
                $postCount = Db::name('post')
                    ->where('message_id', $message['id'])
                    ->where('is_deleted', 0)
                    ->count();
                $message['comment_count'] = $commentCount;
                $message['total_pinglun'] = $commentCount + $postCount;

                // 获取实际点赞数
                $message['total_likes'] = Db::name('unified_likes')
                    ->where('target_type', 'message')
                    ->where('target_id', $message['id'])
                    ->count();

                // 处理用户名
                $message['username'] = $message['username'] ?? '匿名用户';

                // 确保图片字段存在且为正确的格式
                if (!isset($message['images']) || empty($message['images'])) {
                    $message['images'] = '[]';
                }

                // 处理图片URL补全
                $this->processMessageImages($message);

                // 计算热度分数（针对24小时内的帖子优化）
                // 公式：浏览量 + 评论数*50 + 回复数*30，再根据时间衰减
                $viewCount = intval($message['view'] ?? 0);
                $timeElapsed = time() - $timestamp; // 已经过去的时间（秒）
                $hoursPassed = $timeElapsed / 3600; // 转换为小时

                // 简化的时间权重系数 - 最近1小时权重为1.5，其余为1
                if ($hoursPassed <= 1) {
                    // 1小时内：轻微加权
                    $timeDecay = 1.5;
                } else {
                    // 1小时后：标准权重
                    $timeDecay = 1.0;
                }

                // 计算最终热度分数
                $heatScore = ($viewCount + $commentCount * 20 + $postCount * 10) * $timeDecay;
                $message['heat_score'] = $heatScore;
            }

            // 按热度分数降序排序消息
            usort($result, function($a, $b) {
                return $b['heat_score'] <=> $a['heat_score'];
            });

            // 取前limit条消息
            $result = array_slice($result, 0, $limit);

            // 获取热度最高的消息标题，如果超过35个字符则截断添加省略号
            $hotMessageTitle = '';
            if (!empty($result) && isset($result[0]['content'])) {
                $topMessageContent = $result[0]['content'];
                $hotMessageTitle = mb_strlen($topMessageContent, 'UTF-8') > 35 ?
                    mb_substr($topMessageContent, 0, 35, 'UTF-8') . '...' :
                    $topMessageContent;

                // 将热门消息标题保存到全局变量，供生成草稿标题使用
                $GLOBALS['hotMessageTitle'] = $hotMessageTitle;
            }

            // 记录性能和结果
            LogUtil::performance('getLatestMessages', $startTime, [
                'final_count' => count($result),
                'hot_message_title' => $hotMessageTitle,
                'processing_steps' => ['查询数据库', '计算热度分数', '排序', '截取限制数量']
            ]);

            return $result;

        } catch (\Exception $e) {
            LogUtil::error('SimpleDraftGenerator getLatestMessages 方法异常', [
                'limit' => $limit,
                'execution_time' => microtime(true) - $startTime
            ], $e);
            return [];
        }
    }

    /**
     * 创建微信公众号草稿
     *
     * @param string $title 草稿标题
     * @param string $content 草稿内容（HTML格式）
     * @param string $thumbMediaId 封面图片media_id
     * @param string $author 作者名称
     * @param string $digest 摘要
     * @param string $accessToken 微信AccessToken
     * @param array $messages 可选的消息数组，用于降级处理
     * @param bool $isRetry 是否为重试调用，防止无限递归
     * @return array 创建结果
     */
    public function createWechatDraft(string $title, string $content, string $thumbMediaId, string $author, string $digest, string $accessToken, array $messages = [], bool $isRetry = false): array
    {
        try {
            // 检查封面图片媒体ID是否有效
            if (empty($thumbMediaId)) {
                return ['error' => '封面图片media_id为空，无法创建草稿'];
            }

            // 防重复机制已在generateDraft方法中实现，此处不再需要

            // 确保内容符合微信的要求
            // 1. 移除内容中可能引起问题的特殊字符
            $content = preg_replace('/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F]/', '', $content);

            // 2. 确保内容是有效的UTF-8编码
            if (!mb_check_encoding($content, 'UTF-8')) {
                $content = mb_convert_encoding($content, 'UTF-8', 'auto');
            }

            // 3. 确保标题长度适合
            if (mb_strlen($title, 'UTF-8') > 64) {
                $title = mb_substr($title, 0, 60, 'UTF-8') . '...';
            }

            // 强制设置摘要为空，不使用自动摘要功能
            $digest = '';

            // 移除可能存在的base64图片引用
            $content = preg_replace('/src="data:image\/[^;]+;base64,[^"]+"/i', 'src=""', $content);

            // 构建API请求URL
            $url = "https://api.weixin.qq.com/cgi-bin/draft/add?access_token={$accessToken}";

            // 准备要发送的数据
            $data = [
                'articles' => [
                    [
                        'title' => $title,
                        'author' => $author,
                        'digest' => '',
                        'content' => $content,
                        'content_source_url' => '',
                        'thumb_media_id' => $thumbMediaId,
                        'need_open_comment' => 1,  // 开启留言功能
                        'only_fans_can_comment' => 0,  // 允许所有人留言
                        'show_cover_pic' => 1  // 显示封面图片
                    ]
                ]
            ];

            // 发送POST请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 设置30秒超时时间

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return ['error' => '创建草稿请求失败: ' . $error];
            }

            if ($httpCode != 200) {
                return ['error' => '创建草稿HTTP错误：状态码 ' . $httpCode];
            }

            // 解析响应
            $result = json_decode($response, true);
            if (!$result) {
                return ['error' => '创建草稿响应解析失败'];
            }

            if (isset($result['errcode']) && $result['errcode'] != 0) {
                $errMsg = $result['errmsg'] ?? '未知错误';
                $errCode = $result['errcode'] ?? 0;

                // 处理access token过期的情况，但只允许重试一次，防止重复创建草稿
                if (!$isRetry && ($errCode == 40001 || $errCode == 42001 || strpos($errMsg, 'access_token') !== false)) {
                    $newAccessToken = $this->getAccessToken();
                    if (!empty($newAccessToken)) {
                        // 标记为重试调用，防止无限递归
                        return $this->createWechatDraft($title, $content, $thumbMediaId, $author, $digest, $newAccessToken, $messages, true);
                    }
                }

                return ['error' => '创建草稿失败: ' . $errMsg . ' (错误码: ' . $errCode . ')'];
            }

            // 返回创建结果
            return $result;

        } catch (\Exception $e) {
            return ['error' => '创建草稿异常：' . $e->getMessage()];
        }
    }

    /**
     * 下载图片到临时文件
     *
     * @param string $url 图片URL
     * @return string 临时文件路径或空字符串
     */
    public function downloadImage(string $url): string
    {
        try {
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'img_');
            if (!$tempFile) {
                return '';
            }

            // 添加扩展名
            $extension = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION);
            if (empty($extension)) {
                $extension = 'jpg';
            }
            $tempFileWithExt = $tempFile . '.' . $extension;
            rename($tempFile, $tempFileWithExt);

            // 准备下载选项
            $options = [
                'http' => [
                    'method' => 'GET',
                    'header' => 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'timeout' => 30,  // 30秒超时
                ]
            ];
            $context = stream_context_create($options);

            // 尝试使用file_get_contents下载图片
            $imageData = @file_get_contents($url, false, $context);
            if ($imageData === false) {
                // 尝试使用curl下载
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                $imageData = curl_exec($ch);
                $curlError = curl_error($ch);
                curl_close($ch);

                if (empty($imageData)) {
                    return '';
                }
            }

            // 保存图片到临时文件
            if (file_put_contents($tempFileWithExt, $imageData) === false) {
                return '';
            }

            return $tempFileWithExt;

        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * 根据URL获取可能的本地文件路径
     *
     * @param string $url 图片URL
     * @return string|null 可能的本地路径，如果无法确定返回null
     */
    public function getLocalImagePath(string $url): ?string
    {
        // 检查URL是否包含文件路径部分
        if (strpos($url, '/file/') !== false) {
            // 提取file后面的路径
            $pattern = '/\/file\/(.*?)($|\?)/';
            if (preg_match($pattern, $url, $matches)) {
                $filePath = 'public/file/' . $matches[1];
                return dirname(__DIR__, 2) . '/' . $filePath;
            }
        }

        // 如果是uploads目录下的文件
        if (strpos($url, '/uploads/') !== false) {
            $pattern = '/\/uploads\/(.*?)($|\?)/';
            if (preg_match($pattern, $url, $matches)) {
                $filePath = 'public/uploads/' . $matches[1];
                return dirname(__DIR__, 2) . '/' . $filePath;
            }
        }

        return null;
    }

    /**
     * 上传标题图片
     *
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回标题图片的URL，失败返回空字符串
     */
    public function uploadTitleImage(string $accessToken): string
    {
        // 标题图片路径
        $titleImagePath = root_path() . 'public/uploads/biaoti3.png';

        // 使用uploadImageForNewsContent上传标题图片，获取URL
        $imageUrl = $this->uploadImageForNewsContent($titleImagePath, $accessToken);
        if (empty($imageUrl)) {
            return '';
        }

        return $imageUrl;
    }

    /**
     * 上传小程序码图片
     *
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回小程序码的URL，失败返回空字符串
     */
    public function uploadQRCodeImage(string $accessToken): string
    {
        // 更正小程序码图片路径
        $qrcodePath = root_path() . 'public/uploads/小程序码.jpg';


        // 使用uploadImageForNewsContent上传小程序码图片，获取URL
        $imageUrl = $this->uploadImageForNewsContent($qrcodePath, $accessToken);
        if (empty($imageUrl)) {
            return '';
        }

        return $imageUrl;
    }

    /**
     * 生成公众号草稿并返回结果
     *
     * @param Request $request
     * @return Json
     */
    public function generateDraft(Request $request): Json
    {
        try {
            // 添加时间戳用于调试
            $startTime = date('Y-m-d H:i:s');

            // 防重复机制：基于时间的简单防重复（10分钟内只能生成一次）
            $globalCacheKey = 'draft_generating_global';
            if (cache($globalCacheKey)) {
                return json([
                    'code' => 429,
                    'msg' => '草稿生成过于频繁，请10分钟后再试',
                    'debug_time' => $startTime
                ]);
            }

            // 设置全局生成标记，10分钟过期
            cache($globalCacheKey, time(), 600);

            // 1. 获取AccessToken
            $accessToken = $this->getAccessToken();
            if (empty($accessToken)) {
                // 清除缓存标记
                cache($globalCacheKey, null);
                return json(['code' => 500, 'msg' => '获取AccessToken失败', 'debug_time' => $startTime]);
            }

            // 2. 上传封面图片 - 必须是永久素材，media_id才能用于封面
            $coverImageUrl = $request->param('cover_image', '');
            if (empty($coverImageUrl)) {
                // 使用默认封面图片，添加时间戳避免缓存
                $coverImageUrl = $this->completeUrl($this->coverImagePath) . '?t=' . time();
            }

            // 上传封面图片作为永久素材
            $coverMediaId = $this->uploadImageAsPermanentMaterial($coverImageUrl, $accessToken);
            if (empty($coverMediaId)) {
                // 清除缓存标记
                cache($globalCacheKey, null);
                return json([
                    'code' => 500,
                    'msg' => '上传封面图片失败',
                    'debug_time' => $startTime,
                    'cover_url' => $coverImageUrl
                ]);
            }

            // 3. 获取数据库消息
            $limit = $request->param('limit', $this->messageLimit, 'intval');
            $messages = $this->getLatestMessages($limit);

            if (empty($messages)) {
                // 清除缓存标记
                cache($globalCacheKey, null);
                LogUtil::error('SimpleDraftGenerator: 获取消息数据失败', [
                    'limit' => $limit,
                    'cache_key' => $globalCacheKey
                ]);
                return json(['code' => 500, 'msg' => '获取消息数据失败，未找到符合条件的消息']);
            }

            LogUtil::business('SimpleDraftGenerator', '获取消息数据成功', [
                'message_count' => count($messages),
                'limit' => $limit
            ]);

            // 4. 处理消息图片
            $messages = $this->uploadMessagesImages($messages, $accessToken);
            $messages = $this->uploadMessagesImagesForContent($messages, $accessToken);

            // 5. 上传标题图片
            $titleImageUrl = $this->uploadTitleImage($accessToken);

            // 6. 上传小程序码
            $qrcodeUrl = $this->uploadQRCodeImage($accessToken);

            // 7. 上传点赞和评论图标
            $likeIconUrl = $this->uploadLikeIconImage($accessToken);
            $commentIconUrl = $this->uploadCommentIconImage($accessToken);

            // 8. 生成文章内容
            $content = $this->generateStyledContent($messages, $qrcodeUrl, $titleImageUrl, $likeIconUrl, $commentIconUrl);

            // 9. 创建草稿
            // 使用热度最高的消息内容作为标题，如果没有则使用默认模板
            if (!empty($GLOBALS['hotMessageTitle'])) {
                $title = $GLOBALS['hotMessageTitle'];  // 去掉"热帖："前缀
            } else {
                $title = str_replace('{date}', date('Y-m-d H:i'), $this->titleTemplate);
            }

            // 尝试创建草稿（摘要设置为空字符串，不使用自动摘要）
            $result = $this->createWechatDraft($title, $content, $coverMediaId, $this->author, '', $accessToken, $messages);

            // 判断是否要自动发布
            if (isset($result['media_id'])) {
                // 草稿创建成功，清除缓存标记
                cache($globalCacheKey, null);

                $autoPublish = $request->param('auto_publish', 0, 'intval');
                if ($autoPublish) {
                    $publishResult = $this->publishWechatDraft($result['media_id'], $accessToken);

                    if (isset($publishResult['error'])) {
                        return json([
                            'code' => 500,
                            'msg' => '草稿生成成功，但发布失败: ' . $publishResult['error'],
                            'debug_time' => $startTime,
                            'process_time' => date('Y-m-d H:i:s')
                        ]);
                    }

                    return json([
                        'code' => 200,
                        'msg' => '草稿生成并发布成功',
                        'data' => $publishResult,
                        'debug_time' => $startTime,
                        'process_time' => date('Y-m-d H:i:s')
                    ]);
                }

                return json([
                    'code' => 200,
                    'msg' => '草稿生成成功',
                    'data' => $result,
                    'debug_time' => $startTime,
                    'process_time' => date('Y-m-d H:i:s'),
                    'message_count' => count($messages)
                ]);
            }

            // 草稿创建失败，清除缓存标记
            cache($globalCacheKey, null);
            return json([
                'code' => 500,
                'msg' => $result['error'] ?? '草稿创建失败',
                'debug_time' => $startTime,
                'process_time' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            // 异常时清除缓存标记
            if (isset($globalCacheKey)) {
                cache($globalCacheKey, null);
            }
            return json([
                'code' => 500,
                'msg' => '生成草稿失败：' . $e->getMessage(),
                'debug_time' => $startTime ?? date('Y-m-d H:i:s'),
                'process_time' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * 发布微信公众号草稿
     *
     * @param string $mediaId 草稿的media_id
     * @param string $accessToken 微信AccessToken
     * @return array 发布结果
     */
    public function publishWechatDraft(string $mediaId, string $accessToken): array
    {
        try {
            $url = "https://api.weixin.qq.com/cgi-bin/freepublish/submit?access_token={$accessToken}";

            $data = [
                'media_id' => $mediaId
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return ['error' => '发布草稿请求失败: ' . $error];
            }

            $result = json_decode($response, true);
            if (!$result) {
                return ['error' => '发布草稿响应解析失败'];
            }

            if (isset($result['errcode']) && $result['errcode'] != 0) {
                $errMsg = $result['errmsg'] ?? '未知错误';
                return ['error' => '发布草稿失败: ' . $errMsg];
            }

            return $result;

        } catch (\Exception $e) {
            return ['error' => '发布草稿异常：' . $e->getMessage()];
        }
    }

    /**
     * 上传消息中的所有图片
     *
     * @param array $messages 消息数组
     * @param string $accessToken 微信AccessToken
     * @return array 处理后的消息数组
     */
    public function uploadMessagesImages(array $messages, string $accessToken): array
    {
        foreach ($messages as &$message) {
            // 初始化处理后的图片数组
            $message['processed_images'] = [];
            $message['square_images'] = [];

            // 处理消息图片 - 先检查images字段的格式
            if (!empty($message['images'])) {
                // 尝试解析JSON格式的图片URL数组
                $imagesArray = json_decode($message['images'], true);

                // 确保解析后是数组并且不为空
                if (is_array($imagesArray) && !empty($imagesArray)) {
                    // 处理每一张图片，但仅处理前两张（减少API调用）
                    $maxImages = min(2, count($imagesArray));
                    for ($i = 0; $i < $maxImages; $i++) {
                        $imageUrl = $imagesArray[$i];

                        // 检查图片URL是否完整
                        if (strpos($imageUrl, 'http') !== 0) {
                            $imageUrl = $this->serverDomain . $imageUrl;
                        }

                        // 检查图片是否存在本地文件版本
                        $localPath = $this->getLocalImagePath($imageUrl);
                        $squareMediaId = ''; // 只保存裁剪后的方形图片media_id

                        if (!empty($localPath) && file_exists($localPath)) {
                            // 只裁剪图片为正方形并上传，不上传原始图片
                            $squarePath = $this->cropImageToSquare($localPath);
                            if (!empty($squarePath)) {
                                $squareMediaId = $this->uploadLocalImageAsPermanentMaterial($squarePath, $accessToken);
                                // 删除临时裁剪文件
                                if ($squarePath != $localPath && file_exists($squarePath)) {
                                    @unlink($squarePath);
                                }
                            }
                        } else {
                            // 通过URL上传图片为永久素材
                            // 下载图片到临时文件
                            $tempFile = $this->downloadImage($imageUrl);
                            if (!empty($tempFile)) {
                                // 只裁剪并上传方形图片，不上传原始图片
                                $squarePath = $this->cropImageToSquare($tempFile);
                                if (!empty($squarePath)) {
                                    $squareMediaId = $this->uploadLocalImageAsPermanentMaterial($squarePath, $accessToken);
                                    // 删除临时裁剪文件
                                    if ($squarePath != $tempFile && file_exists($squarePath)) {
                                        @unlink($squarePath);
                                    }
                                }

                                // 删除原始临时文件
                                @unlink($tempFile);
                            }
                        }

                        // 保存裁剪后的图片信息（同时作为原始图片和方形图片使用）
                        if (!empty($squareMediaId)) {
                            // 保存为原始图片信息（用于显示）
                            $message['processed_images'][] = [
                                'url' => $imageUrl,
                                'media_id' => $squareMediaId
                            ];

                            // 保存为方形图片信息
                            $message['square_images'][] = [
                                'url' => $imageUrl,
                                'media_id' => $squareMediaId
                            ];
                        }
                    }
                }
            }
        }

        return $messages;
    }

    /**
     * 上传消息中的所有图片(用于图文消息正文)
     *
     * @param array $messages 消息数组
     * @param string $accessToken 微信AccessToken
     * @return array 处理后的消息数组
     */
    public function uploadMessagesImagesForContent(array $messages, string $accessToken): array
    {
        foreach ($messages as &$message) {
            // 初始化处理后的图片数组
            $message['content_images'] = [];
            $message['square_content_images'] = [];

            // 处理消息图片 - 先检查images字段的格式
            if (!empty($message['images'])) {
                // 尝试解析JSON格式的图片URL数组
                $imagesArray = json_decode($message['images'], true);

                // 确保解析后是数组并且不为空
                if (is_array($imagesArray) && !empty($imagesArray)) {
                    // 处理每一张图片，但仅处理前两张（减少API调用）
                    $maxImages = min(2, count($imagesArray));
                    for ($i = 0; $i < $maxImages; $i++) {
                        $imageUrl = $imagesArray[$i];

                        // 检查图片URL是否完整
                        if (strpos($imageUrl, 'http') !== 0) {
                            $imageUrl = $this->serverDomain . $imageUrl;
                        }

                        $squareUploadedUrl = ''; // 只保存裁剪后的方形图片URL

                        // 检查图片是否存在本地文件版本
                        $localPath = $this->getLocalImagePath($imageUrl);
                        if (!empty($localPath) && file_exists($localPath)) {
                            // 只裁剪并上传方形图片，不上传原始图片
                            $squarePath = $this->cropImageToSquare($localPath);
                            if (!empty($squarePath)) {
                                $squareUploadedUrl = $this->uploadImageForNewsContent($squarePath, $accessToken);
                                // 删除临时裁剪文件
                                if ($squarePath != $localPath && file_exists($squarePath)) {
                                    @unlink($squarePath);
                                }
                            }
                        } else {
                            // 通过URL下载图片后上传
                            $tempFile = $this->downloadImage($imageUrl);
                            if (!empty($tempFile)) {
                                // 只裁剪并上传方形图片，不上传原始图片
                                $squarePath = $this->cropImageToSquare($tempFile);
                                if (!empty($squarePath)) {
                                    $squareUploadedUrl = $this->uploadImageForNewsContent($squarePath, $accessToken);
                                    // 删除临时裁剪文件
                                    if ($squarePath != $tempFile && file_exists($squarePath)) {
                                        @unlink($squarePath);
                                    }
                                }

                                // 删除原始临时文件
                                @unlink($tempFile);
                            }
                        }

                        // 保存裁剪后的图片信息（同时作为原始图片和方形图片使用）
                        if (!empty($squareUploadedUrl)) {
                            // 保存为原始图片信息（用于显示）
                            $message['content_images'][] = [
                                'original_url' => $imageUrl,
                                'url' => $squareUploadedUrl
                            ];

                            // 保存为方形图片信息
                            $message['square_content_images'][] = [
                                'original_url' => $imageUrl,
                                'url' => $squareUploadedUrl
                            ];
                        }
                    }
                }
            }
        }

        return $messages;
    }

    /**
     * 测试生成草稿接口 - 用于调试
     *
     * @param Request $request
     * @return Json
     */
    public function testGenerateDraft(Request $request): Json
    {
        $debugInfo = [
            'request_time' => date('Y-m-d H:i:s'),
            'request_params' => $request->param(),
            'request_method' => $request->method(),
            'request_url' => $request->url(true),
        ];

        try {
            // 1. 测试AccessToken获取
            $accessToken = $this->getAccessToken();
            $debugInfo['access_token_status'] = !empty($accessToken) ? 'success' : 'failed';
            $debugInfo['access_token_length'] = strlen($accessToken);

            if (empty($accessToken)) {
                return json(['code' => 500, 'msg' => '获取AccessToken失败', 'debug' => $debugInfo]);
            }

            // 2. 测试消息获取
            $limit = $request->param('limit', 10, 'intval');
            $messages = $this->getLatestMessages($limit);
            $debugInfo['messages_count'] = count($messages);
            $debugInfo['messages_sample'] = array_slice($messages, 0, 2); // 只显示前2条作为样本

            if (empty($messages)) {
                return json(['code' => 500, 'msg' => '获取消息数据失败', 'debug' => $debugInfo]);
            }

            // 3. 测试封面图片
            $coverImageUrl = $request->param('cover_image', '');
            if (empty($coverImageUrl)) {
                $coverImageUrl = $this->completeUrl($this->coverImagePath) . '?t=' . time();
            }
            $debugInfo['cover_image_url'] = $coverImageUrl;

            // 4. 生成标题
            $title = str_replace('{date}', date('Y-m-d H:i'), $this->titleTemplate) . ' - ' . date('H:i:s');
            $debugInfo['generated_title'] = $title;

            // 5. 返回调试信息
            return json([
                'code' => 200,
                'msg' => '调试信息获取成功',
                'debug' => $debugInfo,
                'next_step' => '如果以上信息正常，请调用正式的generateDraft接口'
            ]);

        } catch (\Exception $e) {
            $debugInfo['exception'] = $e->getMessage();
            return json(['code' => 500, 'msg' => '调试过程异常', 'debug' => $debugInfo]);
        }
    }

    /**
     * 上传点赞图标图片
     *
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回点赞图标的URL，失败返回空字符串
     */
    public function uploadLikeIconImage(string $accessToken): string
    {
        // 点赞图标路径
        $likeIconPath = root_path() . 'public/uploads/dianzan1.png';


        // 使用uploadImageForNewsContent上传点赞图标，获取URL
        $imageUrl = $this->uploadImageForNewsContent($likeIconPath, $accessToken);
        if (empty($imageUrl)) {
            return '';
        }

        return $imageUrl;
    }

    /**
     * 上传评论图标图片
     *
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回评论图标的URL，失败返回空字符串
     */
    public function uploadCommentIconImage(string $accessToken): string
    {
        // 评论图标路径
        $commentIconPath = root_path() . 'public/uploads/pinglun1.png';

        
        // 使用uploadImageForNewsContent上传评论图标，获取URL
        $imageUrl = $this->uploadImageForNewsContent($commentIconPath, $accessToken);
        if (empty($imageUrl)) {
            return '';
        }

        return $imageUrl;
    }

    /**
     * 检查草稿防重复缓存状态
     *
     * @param Request $request
     * @return Json
     */
    public function checkDraftCache(Request $request): Json
    {
        try {
            $title = $request->param('title', '测试标题');
            $thumbMediaId = $request->param('thumb_media_id', 'test_media_id');
            $content = $request->param('content', '测试内容');

            // 生成草稿的唯一标识
            $draftKey = md5($title . $thumbMediaId . substr($content, 0, 100));
            $cacheKey = 'draft_creating_' . $draftKey;

            $cacheValue = cache($cacheKey);

            return json([
                'code' => 200,
                'msg' => '缓存状态检查完成',
                'data' => [
                    'draft_key' => $draftKey,
                    'cache_key' => $cacheKey,
                    'cache_value' => $cacheValue,
                    'is_creating' => !empty($cacheValue),
                    'cache_time' => $cacheValue ? date('Y-m-d H:i:s', $cacheValue) : null
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '检查缓存状态失败：' . $e->getMessage()]);
        }
    }
}