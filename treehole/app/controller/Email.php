<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Request;

// 手动引入 PHPMailer 文件(根据实际路径调整)
require_once public_path() . 'PHPMailer/src/PHPMailer.php';
require_once public_path() . 'PHPMailer/src/SMTP.php';
require_once public_path() . 'PHPMailer/src/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

class Email extends BaseController
{
    public function sendVerificationCode() {
    if (Request::isPost()) {
        $email = Request::post('email');
        $user_id = Request::post('user_id');

        if (empty($user_id)) {
            return json(['error' => '请先登录再认证']);
        }

        if (empty($email)) {
            return json(['error' => '请输入邮箱地址']);
        }

        // 检查用户是否在一分钟内发送过验证码
        $user = Db::name('user')->where('id', $user_id)->find();
        if ($user) {
            $last_generated_time = !empty($user['code_generated_at']) 
    ? strtotime($user['code_generated_at']) 
    : 0; // 如果为空，设定为 0 表示一个很早的时间
            $current_time = time();

            $time_diff = $current_time - $last_generated_time;
            if ($time_diff < 60) {
                $remaining_time = 60 - $time_diff;
                return json(['error' => "验证码1分钟最多发送1次，请 {$remaining_time} 秒后重试"]);
            }
        }

        // 生成6位验证码
        $verification_code = $this->generateVerificationCode();
        $current_time = date('Y-m-d H:i:s'); // 获取当前时间
        // 更新用户表中的 code 和 code_generated_at 字段
        
        Db::name('user')->where('id', $user_id)->update([

            'code' => md5($verification_code),

            'code_generated_at' => $current_time,
        ]);

        // 发送邮件
        try {
            $this->sendEmail($email, $verification_code);
            return json(['message' => '验证码已成功发送到您的邮箱']);
        } catch (Exception $e) {
            return json(['error' => '邮件发送失败: ' . $e->getMessage()]);
        }
    } else {
        return json(['error' => '无效的请求方式']);
    }
}

    public function verifyCode() {
        if (Request::isPost()) {
            $email = Request::post('email');
            $schoolname = Request::post('schoolname');
            $user_id = Request::post('user_id');
            $code = Request::post('code');

            if (empty($user_id)) {
                return json(['error' => '请先登录再认证']);
            }

            if (empty($email)) {
                return json(['error' => '请输入邮箱地址']);
            }

            if (empty($code)) {
                return json(['error' => '请输入验证码']);
            }

            // 查询用户信息
            $user = Db::name('user')->where('id', $user_id)->find();

            if (!$user) {
                return json(['error' => '用户不存在']);
            }

            // 检查邮箱是否已被认证
$existing_user = Db::name('user')->where('xuehao', $email)->find();
if ($existing_user) {
    if ($existing_user['id'] == $user_id) {
        return json(['error' => '您已成功认证该邮箱']);
    } else {
        return json(['error' => '该邮箱已被认证，请联系管理员']);
    }
}

            // 检查用户状态是否允许更新
            $allowed_statuses = ['verified','unverified', 'temporary_verified'];
            if (!in_array($user['status'], $allowed_statuses)) {
                return json(['error' => '当前身份不可更改，请联系管理员']);
            }

            // 检查验证码是否正确
            if ($user['code'] == md5($code)) {
                // 检查验证码是否在10分钟内有效
                $code_generated_at = !empty($user['code_generated_at'])
                    ? strtotime($user['code_generated_at'])
                    : 0; // 如果为空，则设置为一个很早的时间
                $current_time = time();
                $time_diff = $current_time - $code_generated_at;

                if ($time_diff <= 600) { // 600秒即10分钟
                    // 验证成功，更新状态为 verified 并将邮箱存入 xuehao 字段
                    Db::name('user')->where('id', $user_id)->update([
                        'status' => 'verified',
                        'xuehao' => $email,
                        'schoolname' => $schoolname,
                        'status_code' => Db::raw('status_code + 1')
                    ]);

                    return json(['message' => '认证成功']);
                } else {
                    return json(['error' => '验证码已过期']);
                }
            } else {
                return json(['error' => '验证码错误']);
            }
        } else {
            return json(['error' => '无效的请求方式']);
        }
    }

    /**
     * 生成6位数字验证码
     *
     * @return string
     */
    private function generateVerificationCode() {
        // 生成一个6位随机数字字符串
        return str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * 使用PHPMailer发送邮件
     *
     * @param string $email 收件人邮箱地址
     * @param string $verification_code 验证码
     * @throws Exception
     */
    private function sendEmail($email, $verification_code) {
        // 邮件主题和内容
        $subject = "【灵行BUAA】学生认证,您的验证码";
        $body = "您的验证码是: " . $verification_code . "\n十分钟之内有效";

        $mail = new PHPMailer(true);  // 启用异常处理
        try {
            // 配置SMTP服务器
            $mail->isSMTP();
            $mail->Host = 'smtp.qq.com';      // 你的SMTP服务器
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';  // SMTP用户名
            $mail->Password = 'auwcvrhmwqubdbhi';   // SMTP密码或授权码
            $mail->SMTPSecure = 'ssl';        // 启用SSL加密
            $mail->Port = 465;                // SMTP端口（QQ邮箱一般为465）

            // 发件人和收件人设置
            $mail->setFrom('<EMAIL>', '月光下的温柔');
            $mail->addAddress($email);  // 收件人
            $mail->CharSet = 'UTF-8';

            // 邮件内容设置
            $mail->isHTML(false);
            $mail->Subject = $subject;
            $mail->Body = $body;

            // 发送邮件
            if (!$mail->send()) {
                throw new Exception('Mailer Error: ' . $mail->ErrorInfo);
            }
        } catch (Exception $e) {
            // 抛出异常以便外层捕获并返回相应错误信息
            throw new Exception($e->getMessage());
        }
    }
}