<?php
namespace app\controller;

use app\BaseController;
use app\util\JwtUtil;
use think\facade\Db;
use think\Request;
use think\response\Json;

class Comment extends BaseController
{
    public function getComments(Request $request): Json
    {
        $page = $request->post('page', 1);
        $limit = 10;
        $messageId = $request->post('message_id', 0);
        $userId = $request->post('user_id', 0);
        $sortType = $request->post('sort_type', 'time'); // 默认按时间排序
        $sortOrder = $request->post('sort_order', 'desc'); // 默认降序

        if (empty($messageId)) {
            return json(['error_code' => 1, 'msg' => '缺少 message_id 参数', 'data' => []]);
        }

        // 检查帖子是否存在且未被删除
        $message = Db::name('message')->where('id', $messageId)->find();
        if (!$message) {
            return json(['error_code' => 1, 'msg' => '帖子不存在', 'data' => []]);
        }
        
        // 检查帖子是否已被删除
        if ($message['choose'] >= 200) {
            return json(['error_code' => 1, 'msg' => '该帖子已被删除', 'data' => []]);
        }

        $offset = ($page - 1) * $limit;

        // 构建查询
        $query = Db::name('comment')->alias('c')
            ->where('c.message_id', $messageId)
            ->where('c.is_deleted', '<>', 1); // 过滤掉已删除的评论
        
        // 设置排序
        if ($sortType === 'likes') {
            // 使用子查询获取实际点赞数进行排序
            $query->field([
                'c.*',
                '(SELECT COUNT(*) FROM unified_likes WHERE unified_likes.target_type = "comment" AND unified_likes.target_id = c.id) as real_likes'
            ])
            ->order('real_likes ' . $sortOrder)
            ->order('c.send_timestamp DESC'); // 点赞数相同时按时间降序
        } else {
            $query->field('c.*')->order('c.send_timestamp ' . $sortOrder);
        }
        
        // 获取评论列表
        $comments = $query->limit($offset, $limit)
            ->select()
            ->toArray();

        // 获取与 message_id 相关的所有未删除的回复，包含用户信息
        $replies = Db::name('post')
            ->alias('p')
            ->join('user u', 'p.user_id = u.id')
            ->where('p.message_id', $messageId)
            ->where('p.is_deleted', '<>', 1) // 过滤掉已删除的回复
            ->field([
                'p.*',
                'u.username',
                'u.face_url',
                'u.titlename',
                'u.titlecolor'
            ])
            ->order('p.send_timestamp', 'desc')  // 按发送时间倒序排序
            ->select()
            ->toArray();

        // 获取评论的点赞数和点赞状态
        foreach ($comments as &$comment) {
            // 获取评论的点赞数
            $comment['total_likes'] = isset($comment['real_likes']) ? $comment['real_likes'] : Db::name('unified_likes')
                ->where('target_type', 'comment')
                ->where('target_id', $comment['id'])
                ->count();
            unset($comment['real_likes']); // 删除临时字段

            // 检查当前用户是否点赞过该评论
            if ($userId) {
                $comment['is_liked'] = Db::name('unified_likes')
                    ->where('target_type', 'comment')
                    ->where('target_id', $comment['id'])
                    ->where('user_id', $userId)
                    ->count() > 0;
            } else {
                $comment['is_liked'] = false;
            }
        }
        unset($comment);

        // 获取回复的点赞数和点赞状态
        foreach ($replies as &$reply) {
            // 获取回复的点赞数
            $reply['total_likes'] = Db::name('unified_likes')
                ->where('target_type', 'reply')
                ->where('target_id', $reply['id'])
                ->count();

            // 检查当前用户是否点赞过该回复
            if ($userId) {
                $reply['is_liked'] = Db::name('unified_likes')
                    ->where('target_type', 'reply')
                    ->where('target_id', $reply['id'])
                    ->where('user_id', $userId)
                    ->count() > 0;
            } else {
                $reply['is_liked'] = false;
            }
        }
        unset($reply);

        // 构建评论的树状结构
        $commentsTree = $this->buildCommentsTree($comments, $replies);

        // 处理匿名评论
        if (isset($message['is_anonymous']) && $message['is_anonymous'] == 1) {
            try {
                // 引入匿名工具类
                $anonymousUtil = new \app\utils\AnonymousUtil();

                // 处理评论列表
                $commentsTree = $anonymousUtil::processAnonymousCommentList(
                    $commentsTree,
                    $message['user_id'],
                    $messageId,
                    true
                );
            } catch (\Exception $e) {
                // 匿名处理失败时记录错误但不影响正常流程
                error_log("匿名评论处理失败: " . $e->getMessage());
            }
        }

        // 格式化时间戳
        foreach ($commentsTree as &$comment) {
            $comment['send_timestamp'] = $this->formatTimestamp($comment['send_timestamp']);

            // 格式化回复的时间戳
            if (isset($comment['replies']) && is_array($comment['replies'])) {
                foreach ($comment['replies'] as &$reply) {
                    $reply['send_timestamp'] = $this->formatTimestamp($reply['send_timestamp']);
                }
            }
        }

        return json(['error_code' => 0, 'msg' => '数据获取成功', 'data' => $commentsTree]);
    }

    private function buildCommentsTree($comments, $replies): array
    {
        $commentsTree = [];
        foreach ($comments as $comment) {
            $comment['replies'] = $this->getReplies($comment['id'], $replies);
            $commentsTree[] = $comment;
        }
        return $commentsTree;
    }

    private function getReplies($commentId, $replies): array
    {
        $replyList = [];
        foreach ($replies as $reply) {
            if ($reply['parent_comment_id'] == $commentId) {
                $reply['replies'] = $this->getReplies($reply['id'], $replies);
                // 移除这里的时间格式化，在外层统一处理
                $replyList[] = $reply;
            }
        }
        // 反转回复列表顺序，实现回复顺序反过来显示
        return array_reverse($replyList);
    }

    private function formatTimestamp($timestamp): string
    {
        if (empty($timestamp)) {
            return '未知时间';
        }

        // 确保时间戳是整数类型
        if (is_string($timestamp)) {
            $timestamp = (int)$timestamp;
        }

        // 如果时间戳无效，返回默认值
        if ($timestamp <= 0) {
            return '未知时间';
        }

        $currentDate = date('Y-m-d');
        $commentDate = date('Y-m-d', $timestamp);

        if ($commentDate == $currentDate) {
            return '今天 ' . date('H:i', $timestamp);
        } elseif ($commentDate == date('Y-m-d', strtotime('-1 day'))) {
            return '昨天 ' . date('H:i', $timestamp);
        } else {
            return date('n月j日 H:i', $timestamp);
        }
    }

    public function publishNewComment(Request $request): Json
    {
        // Token验证
        $token = $request->header('token');
        if (!$token) {
            return json(['error_code' => 401, 'msg' => '请先登录']);
        }

        $userData = JwtUtil::validateToken($token);
        if (!$userData) {
            return json(['error_code' => 401, 'msg' => 'token无效或已过期']);
        }

        // 检查用户状态
        $user = Db::name('user')->where('id', $userData['user_id'])->find();
        if (!$user) {
            return json(['error_code' => 404, 'msg' => '用户不存在']);
        }
        if ($user['status'] === '禁言') {
            return json(['error_code' => 403, 'msg' => '您已被禁言，无法发表评论']);
        }

        $data = $request->post();
        if (empty($data['user_id']) || empty($data['message_id']) || empty($data['content'])) {
            return json(['error_code' => 2, 'msg' => '参数不足']);
        }

        // 验证用户ID是否匹配
        if ((int)$data['user_id'] !== (int)$userData['user_id']) {
            return json(['error_code' => 403, 'msg' => '用户身份验证失败']);
        }

        // 获取用户openid
        $user = Db::name('user')->where('id', $data['user_id'])->find();
        if (!$user || empty($user['openid'])) {
            return json(['error_code' => 2, 'msg' => '用户信息不完整']);
        }


        // 处理图片数据
        $images = $request->post('images', '');
        if ($images !== '' && $images !== null && $images !== '[]') {  // 更严格的空值判断
            // 确保images是一个JSON数组字符串
            if (is_array($images)) {
                $images = json_encode($images);
            }
            // 验证图片数量不超过3张
            $imagesArray = json_decode($images, true);
            if (!is_array($imagesArray)) {
                return json(['error_code' => 3, 'msg' => '图片格式错误']);
            }
            if (count($imagesArray) > 3) {
                return json(['error_code' => 3, 'msg' => '图片数量不能超过3张']);
            }
            $data['images'] = $images;
        } else {
            $data['images'] = null;  // 确保没有图片时设置为null
        }

        // 处理回复目标用户信息
        if (!empty($data['reply_to_user_id'])) {
            $replyToUser = Db::name('user')
                ->where('id', $data['reply_to_user_id'])
                ->field(['id', 'username'])
                ->find();
            if ($replyToUser) {
                $data['reply_to_username'] = $replyToUser['username'];
            }
        }

        $data['total_likes'] = 0;
        $data['send_timestamp'] = time();

        Db::startTrans();
        try {
            // 插入评论
            $result = Db::name('comment')->insert($data);
            if (!$result) {
                throw new \Exception('评论添加失败');
            }

            // 获取评论ID
            $commentId = Db::name('comment')->getLastInsID();
            
            // 获取消息信息（包含匿名字段）
            $message = Db::name('message')
                ->where('id', $data['message_id'])
                ->find();

            if ($message) {
                // 获取评论用户信息
                $user = Db::name('user')
                    ->where('id', $data['user_id'])
                    ->field(['id', 'username', 'face_url'])
                    ->find();

                // 获取第一张图片URL（如果有的话）
                $firstImage = '';
                if (!empty($data['images']) && $data['images'] !== '[]') {
                    $imagesArray = json_decode($data['images'], true);
                    $firstImage = is_array($imagesArray) && !empty($imagesArray) && isset($imagesArray[0]) ? $imagesArray[0] : '';
                }

                // 只有当评论者不是消息作者本人时才添加通知
                if ($message['user_id'] != $data['user_id']) {
                    // 添加通知记录
                    $notificationData = [
                        'user_id' => $message['user_id'],
                        'from_user_id' => $data['user_id'],
                        'type' => 'comment',
                        'target_type' => 'message',
                        'target_id' => $data['message_id'],
                        'message_id' => $data['message_id'],
                        'content' => $user['username'] . '评论了你的帖子',
                        'target_content' => mb_substr($data['content'], 0, 50),
                        'content_image' => $firstImage,
                        'created_at' => date('Y-m-d H:i:s'),
                        'is_read' => 0
                    ];
                    
                    Db::name('notification')->insert($notificationData);

                    // 更新用户未读消息数
                    Db::name('user')
                        ->where('id', $message['user_id'])
                        ->inc('unread', 1)
                        ->update();

                    // WebSocket通知
                    try {
                        $wsServer = app()->make('websocket.server');
                        if ($wsServer && isset($wsServer->userConnections[$message['user_id']])) {
                            $connection = $wsServer->userConnections[$message['user_id']];
                            
                            $wsServer->sendMessage($connection, [
                                'type' => 'notification',
                                'data' => [
                                    'type' => 'comment',
                                    'id' => Db::name('notification')->getLastInsID(),
                                    'user_id' => $message['user_id'],
                                    'from_user_id' => $user['id'],
                                    'from_username' => $user['username'],
                                    'from_user_avatar' => $user['face_url'] ?? '',
                                    'message_id' => $message['id'],
                                    'target_type' => 'message',
                                    'target_id' => $message['id'],
                                    'content' => $user['username'] . '评论了你的帖子',
                                    'target_content' => mb_substr($data['content'], 0, 50),
                                    'content_image' => $firstImage,
                                    'created_at' => date('Y-m-d H:i:s')
                                ]
                            ]);
                        }
                    } catch (\Exception $e) {
                        // WebSocket通知发送失败不影响评论功能
                        error_log("WebSocket通知发送失败: " . $e->getMessage());
                    }
                }
            }

            // 获取刚插入的评论数据
            $newComment = Db::name('comment')
                ->alias('c')
                ->join('user u', 'c.user_id = u.id')
                ->where('c.id', $commentId)
                ->field([
                    'c.*',
                    'u.username',
                    'u.face_url',
                    'u.titlename',
                    'u.titlecolor'
                ])
                ->find();

            // 处理图片数据
            if (!empty($newComment['images'])) {
                $newComment['images'] = json_decode($newComment['images'], true);
            } else {
                $newComment['images'] = [];
            }

            // 格式化时间戳
            $newComment['send_timestamp'] = $this->formatTimestamp($newComment['send_timestamp']);
            $newComment['replies'] = [];
            $newComment['total_likes'] = 0;
            $newComment['is_liked'] = false;

            // 如果是匿名帖子，处理匿名数据
            if (isset($message['is_anonymous']) && $message['is_anonymous'] == 1) {
                $anonymousUtil = new \app\utils\AnonymousUtil();
                $originalComment = $newComment; // 保存原始数据用于调试
                $newComment = $anonymousUtil::processAnonymousCommentData(
                    $newComment,
                    $message['user_id'],
                    $data['message_id'],
                    true
                );
                // 调试日志
                error_log("匿名评论处理 - 原始用户名: " . ($originalComment['username'] ?? 'null') .
                         ", 匿名用户名: " . ($newComment['username'] ?? 'null') .
                         ", 头像: " . ($newComment['face_url'] ?? 'null'));
            }

            Db::commit();

            // 发送模板消息通知（如果不是自己评论自己的帖子）
            if ($message['user_id'] != $data['user_id']) {
                // 获取评论者的显示用户名（如果是匿名帖子则使用匿名用户名）
                $displayUsername = $user['username'];
                if ($message['is_anonymous'] == 1) {
                    // 如果是匿名帖子，使用匿名处理后的用户名
                    $displayUsername = $newComment['username'] ?? $user['username'];
                }

                // 添加调试日志
                error_log("准备发送评论模板消息 - 目标用户ID: " . $message['user_id'] . ", 评论者: " . $displayUsername . ", 内容: " . mb_substr($data['content'], 0, 20));

                $result = \app\controller\WechatTemplate::sendCommentNotificationToUser($message['user_id'], $displayUsername, '评论了你', $data['content'], $data['message_id']);

                error_log("评论模板消息发送结果: " . ($result ? '成功' : '失败'));
            } else {
                error_log("跳过发送评论模板消息 - 用户评论自己的帖子");
            }

            return json([
                'error_code' => 0,
                'msg' => '评论添加成功',
                'data' => [
                    'comment_id' => $commentId,
                    'comment_data' => $newComment
                ]
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return json(['error_code' => 2, 'msg' => $e->getMessage()]);
        }
    }

    public function publishNewPost(Request $request): Json
    {
        // Token验证
        $token = $request->header('token');
        if (!$token) {
            return json(['error_code' => 401, 'msg' => '请先登录']);
        }

        $userData = JwtUtil::validateToken($token);
        if (!$userData) {
            return json(['error_code' => 401, 'msg' => 'token无效或已过期']);
        }

        // 检查用户状态
        $user = Db::name('user')->where('id', $userData['user_id'])->find();
        if (!$user) {
            return json(['error_code' => 404, 'msg' => '用户不存在']);
        }
        if ($user['status'] === '禁言') {
            return json(['error_code' => 403, 'msg' => '您已被禁言，无法发表回复']);
        }

        $data = $request->post();

        // 参数检查
        if (empty($data['user_id']) || empty($data['username']) ||
            empty($data['message_id']) || empty($data['content']) ||
            empty($data['parent_comment_id']) || empty($data['reply_type']) ||
            empty($data['reply_to_user_id']) || empty($data['reply_to_username'])) {
            return json(['error_code' => 2, 'msg' => '参数不足']);
        }

        // 验证用户ID是否匹配
        if ((int)$data['user_id'] !== (int)$userData['user_id']) {
            return json(['error_code' => 403, 'msg' => '用户身份验证失败']);
        }

        // 获取用户openid
        $user = Db::name('user')->where('id', $data['user_id'])->find();
        if (!$user || empty($user['openid'])) {
            return json(['error_code' => 2, 'msg' => '用户信息不完整']);
        }


        // 验证回复类型
        if (!in_array($data['reply_type'], ['comment', 'reply'])) {
            return json(['error_code' => 2, 'msg' => '无效的回复类型']);
        }

        // 处理 reply_to_post_id 字段
        if ($data['reply_type'] === 'reply') {
            if (empty($data['reply_to_post_id'])) {
                return json(['error_code' => 2, 'msg' => '回复其他回复时需要提供 reply_to_post_id']);
            }
            $data['reply_to_post_id'] = intval($data['reply_to_post_id']);
        } else {
            // 如果是直接回复评论，将 reply_to_post_id 设为 null
            $data['reply_to_post_id'] = null;
        }

        // 处理图片数据
        $images = $request->post('images', '');
        if ($images !== '' && $images !== null && $images !== '[]') {
            // 确保images是一个JSON数组字符串
            if (is_array($images)) {
                $images = json_encode($images);
            }
            // 验证图片数量不超过3张
            $imagesArray = json_decode($images, true);
            if (!is_array($imagesArray)) {
                return json(['error_code' => 3, 'msg' => '图片格式错误']);
            }
            if (count($imagesArray) > 3) {
                return json(['error_code' => 3, 'msg' => '图片数量不能超过3张']);
            }
            $data['images'] = $images;
        } else {
            $data['images'] = null;
        }

        $data['total_likes'] = 0;
        $data['send_timestamp'] = time();

        Db::startTrans();
        try {
            // 插入回复
            $result = Db::name('post')->insertGetId($data);
            if (!$result) {
                throw new \Exception('回复添加失败');
            }

            // 获取原评论信息
            $comment = Db::name('comment')
                ->where('id', $data['parent_comment_id'])
                ->find();

            if (!$comment) {
                throw new \Exception('原评论不存在');
            }

            // 获取回复用户信息
            $user = Db::name('user')
                ->where('id', $data['user_id'])
                ->field(['id', 'username', 'face_url'])
                ->find();

            // 获取第一张图片URL（如果有的话）
            $firstImage = '';
            if (!empty($data['images']) && $data['images'] !== '[]') {
                $imagesArray = json_decode($data['images'], true);
                $firstImage = is_array($imagesArray) && !empty($imagesArray) && isset($imagesArray[0]) ? $imagesArray[0] : '';
            }

            // 1. 通知原评论作者
            if ($comment['user_id'] != $data['user_id']) {  // 如果回复者不是评论作者
                $notificationData = [
                    'user_id' => $comment['user_id'],
                    'from_user_id' => $data['user_id'],
                    'type' => 'reply',
                    'target_type' => 'comment',
                    'target_id' => $data['parent_comment_id'],
                    'message_id' => $data['message_id'],
                    'content' => $user['username'] . '回复了你的评论',
                    'target_content' => mb_substr($data['content'], 0, 50),
                    'content_image' => $firstImage,
                    'created_at' => date('Y-m-d H:i:s'),
                    'is_read' => 0
                ];
                
                Db::name('notification')->insert($notificationData);
                Db::name('user')->where('id', $comment['user_id'])->inc('unread', 1)->update();

                // WebSocket通知评论作者
                try {
                    $wsServer = app()->make('websocket.server');
                    if ($wsServer && isset($wsServer->userConnections[$comment['user_id']])) {
                        $connection = $wsServer->userConnections[$comment['user_id']];
                        $wsServer->sendMessage($connection, [
                            'type' => 'notification',
                            'data' => [
                                'type' => 'reply',
                                'id' => Db::name('notification')->getLastInsID(),
                                'user_id' => $comment['user_id'],
                                'from_user_id' => $user['id'],
                                'from_username' => $user['username'],
                                'from_user_avatar' => $user['face_url'] ?? '',
                                'message_id' => $data['message_id'],
                                'target_type' => 'comment',
                                'target_id' => $data['parent_comment_id'],
                                'content' => $user['username'] . '回复了你的评论',
                                'target_content' => mb_substr($data['content'], 0, 50),
                                'content_image' => $firstImage,
                                'created_at' => date('Y-m-d H:i:s')
                            ]
                        ]);
                    }
                } catch (\Exception $e) {
                    error_log("WebSocket通知评论作者失败: " . $e->getMessage());
                }
            }

            // 2. 如果是回复其他人的回复，还需要通知被回复的用户
            if ($data['reply_type'] == 'reply' && $data['reply_to_user_id'] != $comment['user_id'] && $data['reply_to_user_id'] != $data['user_id']) {
                $notificationData = [
                    'user_id' => $data['reply_to_user_id'],
                    'from_user_id' => $data['user_id'],
                    'type' => 'reply',
                    'target_type' => 'reply',
                    'target_id' => $result,  // 新回复的ID
                    'message_id' => $data['message_id'],
                    'content' => $user['username'] . '回复了你的回复',
                    'target_content' => mb_substr($data['content'], 0, 50),
                    'content_image' => $firstImage,
                    'created_at' => date('Y-m-d H:i:s'),
                    'is_read' => 0
                ];
                
                Db::name('notification')->insert($notificationData);
                Db::name('user')->where('id', $data['reply_to_user_id'])->inc('unread', 1)->update();

                // WebSocket通知被回复的用户
                try {
                    $wsServer = app()->make('websocket.server');
                    if ($wsServer && isset($wsServer->userConnections[$data['reply_to_user_id']])) {
                        $connection = $wsServer->userConnections[$data['reply_to_user_id']];
                        $wsServer->sendMessage($connection, [
                            'type' => 'notification',
                            'data' => [
                                'type' => 'reply',
                                'id' => Db::name('notification')->getLastInsID(),
                                'user_id' => $data['reply_to_user_id'],
                                'from_user_id' => $user['id'],
                                'from_username' => $user['username'],
                                'from_user_avatar' => $user['face_url'] ?? '',
                                'message_id' => $data['message_id'],
                                'target_type' => 'reply',
                                'target_id' => $result,
                                'content' => $user['username'] . '回复了你的回复',
                                'target_content' => mb_substr($data['content'], 0, 50),
                                'content_image' => $firstImage,
                                'created_at' => date('Y-m-d H:i:s')
                            ]
                        ]);
                    }
                } catch (\Exception $e) {
                    error_log("WebSocket通知被回复用户失败: " . $e->getMessage());
                }
            }

            // 获取刚插入的回复数据
            $newReply = Db::name('post')
                ->alias('p')
                ->join('user u', 'p.user_id = u.id')
                ->where('p.id', $result)
                ->field([
                    'p.*',
                    'u.username',
                    'u.face_url',
                    'u.titlename',
                    'u.titlecolor'
                ])
                ->find();

            // 处理图片数据
            if (!empty($newReply['images'])) {
                $newReply['images'] = json_decode($newReply['images'], true);
            } else {
                $newReply['images'] = [];
            }

            // 格式化时间戳
            $newReply['send_timestamp'] = $this->formatTimestamp($newReply['send_timestamp']);
            $newReply['total_likes'] = 0;
            $newReply['is_liked'] = false;

            // 检查是否是匿名帖子
            $messageInfo = Db::name('message')->where('id', $data['message_id'])->find();
            if (isset($messageInfo['is_anonymous']) && $messageInfo['is_anonymous'] == 1) {
                $anonymousUtil = new \app\utils\AnonymousUtil();
                $originalReply = $newReply; // 保存原始数据用于调试
                $newReply = $anonymousUtil::processAnonymousCommentData(
                    $newReply,
                    $messageInfo['user_id'],
                    $data['message_id'],
                    true
                );
                // 调试日志
                error_log("匿名回复处理 - 原始用户名: " . ($originalReply['username'] ?? 'null') .
                         ", 匿名用户名: " . ($newReply['username'] ?? 'null') .
                         ", 头像: " . ($newReply['face_url'] ?? 'null'));
            }

            Db::commit();

            // 发送模板消息通知
            // 获取回复者的显示用户名（如果是匿名帖子则使用匿名用户名）
            $displayUsername = $user['username'];
            if (isset($messageInfo['is_anonymous']) && $messageInfo['is_anonymous'] == 1) {
                // 如果是匿名帖子，使用匿名处理后的用户名
                $displayUsername = $newReply['username'] ?? $user['username'];
            }

            // 1. 通知原评论作者（如果不是自己回复自己的评论）
            if ($comment['user_id'] != $data['user_id']) {
                \app\controller\WechatTemplate::sendCommentNotificationToUser($comment['user_id'], $displayUsername, '回复了你的评论', $data['content'], $data['message_id']);
            }

            // 2. 如果是回复其他人的回复，还需要通知被回复的用户（如果不是自己且不是评论作者）
            if ($data['reply_type'] == 'reply' && $data['reply_to_user_id'] != $comment['user_id'] && $data['reply_to_user_id'] != $data['user_id']) {
                \app\controller\WechatTemplate::sendCommentNotificationToUser($data['reply_to_user_id'], $displayUsername, '回复了你的回复', $data['content'], $data['message_id']);
            }

            return json([
                'error_code' => 0,
                'msg' => '回复成功',
                'data' => [
                    'reply_id' => $result,
                    'reply_data' => $newReply
                ]
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return json(['error_code' => 2, 'msg' => $e->getMessage()]);
        }
    }

    private function checkTextContent($content): array
    {
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/wxa/msg_sec_check?access_token={$accessToken}";
        $data = json_encode(['content' => $content]);

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $response = curl_exec($ch);
        if ($response === false) {
            return ['errcode' => -1, 'errmsg' => 'cURL Error: ' . curl_error($ch)];
        }

        curl_close($ch);
        return json_decode($response, true);
    }

    private function getAccessToken(): string
    {
        // 获取微信小程序配置
        $wechatConfig = \app\util\SecretUtil::getWechatMiniprogram('main');
        $appid = $wechatConfig['appid'];
        $appsecret = $wechatConfig['secret'];

        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$appsecret}";
        $response = file_get_contents($url);
        $result = json_decode($response, true);
        return $result['access_token'];
    }

    /**
     * 获取"评论我的"列表
     */
    public function getMyComments(Request $request): Json
    {
        $userId = $request->post('user_id', 0);
        $page = $request->post('page', 1);
        $limit = 10;

        if (empty($userId)) {
            return json(['error_code' => 1, 'msg' => '缺少 user_id 参数']);
        }

        $offset = ($page - 1) * $limit;

        try {
            // 1. 获取评论我帖子的评论（包含已删除的，后续处理中显示删除提示）
            $comments = Db::name('comment')
                ->alias('c')
                ->join('message m', 'm.id = c.message_id')
                ->join('user u', 'u.id = c.user_id')
                ->where('m.user_id', $userId)
                ->where('c.user_id', '<>', $userId)  // 排除自己的评论
                ->field([
                    'c.id',
                    'c.content',
                    'c.images',
                    'c.send_timestamp',
                    'c.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'c.message_id',
                    'm.content as message_content',
                    'm.images as message_images',
                    'm.is_anonymous as message_is_anonymous',
                    '"comment" as type',  // 类型：评论
                    'NULL as parent_comment_id',
                    'NULL as reply_to_user_id',
                    'NULL as reply_to_username',
                    'NULL as original_content',  // 原评论内容
                    'NULL as original_reply_content',  // 原回复内容
                    'c.is_deleted'  // 添加删除状态
                ])
                ->select()
                ->toArray();

            // 2. 获取回复我评论的回复（包含已删除的，后续处理中显示删除提示）
            $replies1 = Db::name('post')
                ->alias('p')
                ->join('message m', 'm.id = p.message_id')
                ->join('user u', 'u.id = p.user_id')
                ->join('comment c', 'c.id = p.parent_comment_id')
                ->where('c.user_id', $userId)  // 我是评论作者
                ->where('p.user_id', '<>', $userId)  // 排除自己的回复
                ->where('p.reply_type', 'comment')  // 直接回复评论
                ->field([
                    'p.id',
                    'p.content',
                    'p.images',
                    'p.send_timestamp',
                    'p.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'p.message_id',
                    'm.content as message_content',
                    'm.images as message_images',
                    'm.is_anonymous as message_is_anonymous',
                    '"reply_to_comment" as type',  // 类型：回复评论
                    'p.parent_comment_id',
                    'p.reply_to_user_id',
                    'p.reply_to_username',
                    'c.content as original_content',  // 原评论内容
                    'NULL as original_reply_content',
                    'p.is_deleted'  // 添加删除状态
                ])
                ->select()
                ->toArray();

            // 3. 获取回复我回复的回复（包含已删除的，后续处理中显示删除提示）
            $replies2 = Db::name('post')
                ->alias('p')
                ->join('message m', 'm.id = p.message_id')
                ->join('user u', 'u.id = p.user_id')
                ->join('post original_reply', 'original_reply.id = p.reply_to_post_id')
                ->join('comment c', 'c.id = p.parent_comment_id')
                ->where('p.reply_to_user_id', $userId)  // 回复目标是我
                ->where('p.user_id', '<>', $userId)  // 排除自己的回复
                ->where('p.reply_type', 'reply')  // 回复的回复
                ->field([
                    'p.id',
                    'p.content',
                    'p.images',
                    'p.send_timestamp',
                    'p.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'p.message_id',
                    'm.content as message_content',
                    'm.images as message_images',
                    'm.is_anonymous as message_is_anonymous',
                    '"reply_to_reply" as type',  // 类型：回复的回复
                    'p.parent_comment_id',
                    'p.reply_to_user_id',
                    'p.reply_to_username',
                    'c.content as original_content',  // 原评论内容
                    'original_reply.content as original_reply_content',  // 原回复内容
                    'p.is_deleted'  // 添加删除状态
                ])
                ->select()
                ->toArray();

            // 4. 获取了然几分评论
            $liaoranComments = Db::name('liaoran_comments')
                ->alias('lc')
                ->join('liaoran_objects lo', 'lo.id = lc.object_id')
                ->join('user u', 'u.id = lc.user_id')
                ->where('lo.created_by', $userId)  // 我创建的对象
                ->where('lc.user_id', '<>', $userId)  // 排除自己的评论
                ->where('lc.status', 1)  // 未删除
                ->where('lc.parent_id', 0)  // 主评论
                ->field([
                    'lc.id',
                    'lc.content',
                    'lc.images',
                    'UNIX_TIMESTAMP(lc.created_at) as send_timestamp',
                    'lc.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'lc.object_id as message_id',  // 使用object_id作为message_id
                    'lo.name as message_content',  // 对象名称作为内容
                    'lo.image_url as message_images',  // 对象图片
                    '"liaoran_comment" as type',
                    'NULL as parent_comment_id',
                    'NULL as reply_to_user_id',
                    'NULL as reply_to_username',
                    'NULL as original_content',
                    'NULL as original_reply_content',
                    '0 as is_deleted'
                ])
                ->select()
                ->toArray();

            // 5. 获取了然几分回复（回复我的评论）
            $liaoranReplies1 = Db::name('liaoran_comments')
                ->alias('lc')
                ->join('liaoran_comments lcp', 'lcp.id = lc.parent_id')  // 父评论
                ->join('liaoran_objects lo', 'lo.id = lc.object_id')
                ->join('user u', 'u.id = lc.user_id')
                ->where('lcp.user_id', $userId)  // 我是父评论作者
                ->where('lc.user_id', '<>', $userId)  // 排除自己的回复
                ->where('lc.status', 1)  // 未删除
                ->where('lc.parent_id', '>', 0)  // 回复
                ->field([
                    'lc.id',
                    'lc.content',
                    'lc.images',
                    'UNIX_TIMESTAMP(lc.created_at) as send_timestamp',
                    'lc.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'lc.object_id as message_id',
                    'lo.name as message_content',
                    'lo.image_url as message_images',
                    '"liaoran_reply" as type',
                    'lc.parent_id as parent_comment_id',
                    'lc.reply_to_user_id',
                    'NULL as reply_to_username',
                    'lcp.content as original_content',
                    'NULL as original_reply_content',
                    '0 as is_deleted'
                ])
                ->select()
                ->toArray();

            // 5.1. 获取了然几分回复（回复我的回复）
            $liaoranReplies2 = Db::name('liaoran_comments')
                ->alias('lc')
                ->join('liaoran_objects lo', 'lo.id = lc.object_id')
                ->join('user u', 'u.id = lc.user_id')
                ->leftJoin('liaoran_comments original_reply', 'original_reply.user_id = lc.reply_to_user_id AND original_reply.object_id = lc.object_id')
                ->where('lc.reply_to_user_id', $userId)  // 我是被回复的用户
                ->where('lc.user_id', '<>', $userId)  // 排除自己的回复
                ->where('lc.status', 1)  // 未删除
                ->where('lc.parent_id', '>', 0)  // 回复
                ->field([
                    'lc.id',
                    'lc.content',
                    'lc.images',
                    'UNIX_TIMESTAMP(lc.created_at) as send_timestamp',
                    'lc.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'lc.object_id as message_id',
                    'lo.name as message_content',
                    'lo.image_url as message_images',
                    '"liaoran_reply_to_reply" as type',
                    'lc.parent_id as parent_comment_id',
                    'lc.reply_to_user_id',
                    'NULL as reply_to_username',
                    'COALESCE(original_reply.content, "你的回复") as original_content',
                    'NULL as original_reply_content',
                    '0 as is_deleted'
                ])
                ->select()
                ->toArray();

            // 6. 获取食堂窗口评论
            $windowComments = Db::name('window_comments')
                ->alias('wc')
                ->join('windows w', 'w.id = wc.window_id')
                ->join('user u', 'u.id = wc.user_id')
                ->where('w.created_by', $userId)  // 我创建的窗口
                ->where('wc.user_id', '<>', $userId)  // 排除自己的评论
                ->where('wc.status', 1)  // 未删除
                ->whereNull('wc.parent_id')  // 主评论
                ->field([
                    'wc.id',
                    'wc.content',
                    'wc.images',
                    'UNIX_TIMESTAMP(wc.created_at) as send_timestamp',
                    'wc.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'wc.window_id as message_id',
                    'w.name as message_content',
                    'w.image_url as message_images',
                    '"window_comment" as type',
                    'NULL as parent_comment_id',
                    'NULL as reply_to_user_id',
                    'NULL as reply_to_username',
                    'NULL as original_content',
                    'NULL as original_reply_content',
                    '0 as is_deleted'
                ])
                ->select()
                ->toArray();

            // 7. 获取食堂窗口回复（回复我的评论）
            $windowReplies1 = Db::name('window_comments')
                ->alias('wc')
                ->join('window_comments wcp', 'wcp.id = wc.parent_id')  // 父评论
                ->join('windows w', 'w.id = wc.window_id')
                ->join('user u', 'u.id = wc.user_id')
                ->where('wcp.user_id', $userId)  // 我是父评论作者
                ->where('wc.user_id', '<>', $userId)  // 排除自己的回复
                ->where('wc.status', 1)  // 未删除
                ->whereNotNull('wc.parent_id')  // 回复
                ->field([
                    'wc.id',
                    'wc.content',
                    'wc.images',
                    'UNIX_TIMESTAMP(wc.created_at) as send_timestamp',
                    'wc.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'wc.window_id as message_id',
                    'w.name as message_content',
                    'w.image_url as message_images',
                    '"window_reply" as type',
                    'wc.parent_id as parent_comment_id',
                    'wc.reply_to_user_id',
                    'NULL as reply_to_username',
                    'wcp.content as original_content',
                    'NULL as original_reply_content',
                    '0 as is_deleted'
                ])
                ->select()
                ->toArray();

            // 8. 获取食堂窗口回复（回复我的回复）
            $windowReplies2 = Db::name('window_comments')
                ->alias('wc')
                ->join('windows w', 'w.id = wc.window_id')
                ->join('user u', 'u.id = wc.user_id')
                ->leftJoin('window_comments original_reply', 'original_reply.user_id = wc.reply_to_user_id AND original_reply.window_id = wc.window_id')
                ->where('wc.reply_to_user_id', $userId)  // 我是被回复的用户
                ->where('wc.user_id', '<>', $userId)  // 排除自己的回复
                ->where('wc.status', 1)  // 未删除
                ->whereNotNull('wc.parent_id')  // 回复
                ->field([
                    'wc.id',
                    'wc.content',
                    'wc.images',
                    'UNIX_TIMESTAMP(wc.created_at) as send_timestamp',
                    'wc.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'wc.window_id as message_id',
                    'w.name as message_content',
                    'w.image_url as message_images',
                    '"window_reply_to_reply" as type',
                    'wc.parent_id as parent_comment_id',
                    'wc.reply_to_user_id',
                    'NULL as reply_to_username',
                    'COALESCE(original_reply.content, "你的回复") as original_content',
                    'NULL as original_reply_content',
                    '0 as is_deleted'
                ])
                ->select()
                ->toArray();

            // 9. 获取专业评论（通过我的评论被别人评论来获取）
            // 由于专业没有创建者概念，我们通过查找我发表的专业评论被别人回复的情况
            $majorComments = Db::name('major_comments')
                ->alias('mc')
                ->join('major_comments my_comment', 'my_comment.id = mc.parent_id')  // 我的原评论
                ->join('buaa_majors bm', 'bm.id = mc.major_id')
                ->join('user u', 'u.id = mc.user_id')
                ->where('my_comment.user_id', $userId)  // 我是原评论作者
                ->where('mc.user_id', '<>', $userId)  // 排除自己的回复
                ->where('mc.status', 1)  // 未删除
                ->where('mc.parent_id', '>', 0)  // 这是回复
                ->where('my_comment.parent_id', 0)  // 原评论是主评论（不是回复）
                ->field([
                    'mc.id',
                    'mc.content',
                    'mc.images',
                    'UNIX_TIMESTAMP(mc.created_at) as send_timestamp',
                    'mc.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'mc.major_id as message_id',
                    'bm.major_name as message_content',
                    'NULL as message_images',
                    '"major_comment" as type',
                    'mc.parent_id as parent_comment_id',
                    'mc.reply_to_user_id',
                    'NULL as reply_to_username',
                    'my_comment.content as original_content',
                    'NULL as original_reply_content',
                    '0 as is_deleted'
                ])
                ->select()
                ->toArray();

            // 10. 获取专业回复（回复我的评论）
            $majorReplies1 = Db::name('major_comments')
                ->alias('mc')
                ->join('major_comments mcp', 'mcp.id = mc.parent_id')  // 父评论
                ->join('buaa_majors bm', 'bm.id = mc.major_id')
                ->join('user u', 'u.id = mc.user_id')
                ->where('mcp.user_id', $userId)  // 我是父评论作者
                ->where('mc.user_id', '<>', $userId)  // 排除自己的回复
                ->where('mc.status', 1)  // 未删除
                ->where('mc.parent_id', '>', 0)  // 回复
                ->field([
                    'mc.id',
                    'mc.content',
                    'mc.images',
                    'UNIX_TIMESTAMP(mc.created_at) as send_timestamp',
                    'mc.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'mc.major_id as message_id',
                    'bm.major_name as message_content',
                    'NULL as message_images',
                    '"major_reply" as type',
                    'mc.parent_id as parent_comment_id',
                    'mc.reply_to_user_id',
                    'NULL as reply_to_username',
                    'mcp.content as original_content',
                    'NULL as original_reply_content',
                    '0 as is_deleted'
                ])
                ->select()
                ->toArray();

            // 11. 获取专业回复（回复我的回复）
            $majorReplies2 = Db::name('major_comments')
                ->alias('mc')
                ->join('buaa_majors bm', 'bm.id = mc.major_id')
                ->join('user u', 'u.id = mc.user_id')
                ->leftJoin('major_comments original_reply', 'original_reply.user_id = mc.reply_to_user_id AND original_reply.major_id = mc.major_id')
                ->where('mc.reply_to_user_id', $userId)  // 我是被回复的用户
                ->where('mc.user_id', '<>', $userId)  // 排除自己的回复
                ->where('mc.status', 1)  // 未删除
                ->where('mc.parent_id', '>', 0)  // 回复
                ->field([
                    'mc.id',
                    'mc.content',
                    'mc.images',
                    'UNIX_TIMESTAMP(mc.created_at) as send_timestamp',
                    'mc.user_id as from_user_id',
                    'u.username as from_username',
                    'u.face_url as from_user_avatar',
                    'mc.major_id as message_id',
                    'bm.major_name as message_content',
                    'NULL as message_images',
                    '"major_reply_to_reply" as type',
                    'mc.parent_id as parent_comment_id',
                    'mc.reply_to_user_id',
                    'NULL as reply_to_username',
                    'COALESCE(original_reply.content, "你的回复") as original_content',
                    'NULL as original_reply_content',
                    '0 as is_deleted'
                ])
                ->select()
                ->toArray();

            // 合并所有结果
            $allComments = array_merge($comments, $replies1, $replies2, $liaoranComments, $liaoranReplies1, $liaoranReplies2, $windowComments, $windowReplies1, $windowReplies2, $majorComments, $majorReplies1, $majorReplies2);

            // 按时间排序
            usort($allComments, function($a, $b) {
                return $b['send_timestamp'] - $a['send_timestamp'];
            });

            // 处理图片字段和删除状态
            foreach ($allComments as &$item) {
                // 处理评论/回复的图片
                if (!empty($item['images'])) {
                    $item['images'] = json_decode($item['images'], true);
                } else {
                    $item['images'] = [];
                }

                // 处理原帖子的图片
                if (!empty($item['message_images'])) {
                    // 对于messagedetail类型，message_images是JSON字符串
                    if (in_array($item['type'], ['comment', 'reply_to_comment', 'reply_to_reply'])) {
                        $item['message_images'] = json_decode($item['message_images'], true);
                    }
                    // 对于liaoran、window和major类型，image_url是单个URL字符串或为空
                    else if (in_array($item['type'], ['liaoran_comment', 'liaoran_reply', 'liaoran_reply_to_reply', 'window_comment', 'window_reply', 'window_reply_to_reply', 'major_comment', 'major_reply', 'major_reply_to_reply'])) {
                        if (!empty($item['message_images'])) {
                            $item['message_images'] = [$item['message_images']]; // 转换为数组格式
                        } else {
                            $item['message_images'] = [];
                        }
                    }
                    else {
                        $item['message_images'] = json_decode($item['message_images'], true);
                    }
                } else {
                    $item['message_images'] = [];
                }

                // 处理匿名显示（仅对树洞相关的评论和回复）
                if (isset($item['message_is_anonymous']) && $item['message_is_anonymous'] == 1 &&
                    in_array($item['type'], ['comment', 'reply_to_comment', 'reply_to_reply'])) {
                    $anonymousUtil = new \app\utils\AnonymousUtil();
                    $anonymousName = $anonymousUtil::generateAnonymousName($item['from_user_id'], $item['message_id']);
                    $anonymousAvatar = $anonymousUtil::generateAnonymousAvatar($item['from_user_id'], $item['message_id']);

                    // 替换用户信息为匿名信息
                    $item['from_username'] = $anonymousName;
                    $item['from_user_avatar'] = $anonymousAvatar;
                    $item['is_anonymous_notification'] = true;

                    // 清理敏感信息
                    unset($item['from_user_id']);
                } else {
                    $item['is_anonymous_notification'] = false;
                }

                // 清理不需要返回给前端的字段
                unset($item['message_is_anonymous']);

                // 格式化时间
                $item['send_timestamp'] = $this->formatTimestamp($item['send_timestamp']);

                // 处理删除状态
                if (!empty($item['is_deleted']) && $item['is_deleted'] == 1) {
                    // 根据类型显示不同的删除提示
                    if (in_array($item['type'], ['comment', 'liaoran_comment', 'window_comment', 'major_comment'])) {
                        $item['content'] = '该条评论已被删除';
                    } else if (in_array($item['type'], ['reply_to_comment', 'reply_to_reply', 'liaoran_reply', 'liaoran_reply_to_reply', 'window_reply', 'window_reply_to_reply', 'major_reply', 'major_reply_to_reply'])) {
                        $item['content'] = '该条回复已被删除';
                    } else {
                        $item['content'] = '该条内容已被删除';
                    }
                    $item['images'] = [];
                    $item['is_deleted_content'] = true;  // 标记为已删除内容
                } else {
                    $item['is_deleted_content'] = false;
                }

                // 确保必要字段不为空
                $item['message_content'] = $item['message_content'] ?: '';
                $item['original_content'] = $item['original_content'] ?: '';
                $item['original_reply_content'] = $item['original_reply_content'] ?: '';
            }
            unset($item);

            // 分页处理
            $total = count($allComments);
            $comments = array_slice($allComments, $offset, $limit);

            return json([
                'error_code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'list' => $comments,
                    'total' => $total,
                    'has_more' => ($offset + $limit) < $total
                ]
            ]);

        } catch (\Exception $e) {
            return json(['error_code' => 2, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 软删除评论
     */
    public function softDeleteComment(Request $request): Json
    {
        $commentId = $request->post('comment_id');
        $userId = $request->post('user_id');
        $token = $request->post('token');

        // 参数验证
        if (!$commentId || !$userId || !$token) {
            return json([
                'error_code' => 1, 
                'msg' => '参数不足', 
                'debug' => [
                    'commentId' => $commentId,
                    'userId' => $userId,
                    'token' => $token
                ]
            ]);
        }

        // 查找评论
        $comment = Db::name('comment')->where('id', $commentId)->find();
        if (!$comment) {
            return json(['error_code' => 1, 'msg' => '评论不存在']);
        }

        // 验证是否是评论作者
        if ($comment['user_id'] != $userId) {
            return json(['error_code' => 1, 'msg' => '无权限删除此评论']);
        }

        try {
            // 软删除评论（将is_deleted设为1）
            $result = Db::name('comment')
                ->where('id', $commentId)
                ->update(['is_deleted' => 1]);

            if ($result) {
                return json(['error_code' => 0, 'msg' => '删除成功']);
            } else {
                return json(['error_code' => 1, 'msg' => '删除失败']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 软删除回复
     */
    public function softDeleteReply(Request $request): Json
    {
        $replyId = $request->post('reply_id');
        $userId = $request->post('user_id');
        $token = $request->post('token');

        // 参数验证
        if (!$replyId || !$userId || !$token) {
            return json([
                'error_code' => 1, 
                'msg' => '参数不足', 
                'debug' => [
                    'replyId' => $replyId,
                    'userId' => $userId,
                    'token' => $token
                ]
            ]);
        }

        // 查找回复
        $reply = Db::name('post')->where('id', $replyId)->find();
        if (!$reply) {
            return json(['error_code' => 1, 'msg' => '回复不存在']);
        }

        // 验证是否是回复作者
        if ($reply['user_id'] != $userId) {
            return json(['error_code' => 1, 'msg' => '无权限删除此回复']);
        }

        try {
            // 软删除回复（将is_deleted设为1）
            $result = Db::name('post')
                ->where('id', $replyId)
                ->update(['is_deleted' => 1]);

            if ($result) {
                return json(['error_code' => 0, 'msg' => '删除成功']);
            } else {
                return json(['error_code' => 1, 'msg' => '删除失败']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }


}