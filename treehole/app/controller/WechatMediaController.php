<?php
namespace app\controller;

use app\BaseController;
use think\facade\Config;
use think\Request;
use think\response\Json;
use think\App;

/**
 * 微信素材管理控制器
 * 功能：上传图片为永久素材、调用草稿生成接口等
 */
class WechatMediaController extends BaseController
{
    // 微信公众号配置
    protected $appId = '';
    protected $appSecret = '';
    
    /**
     * 构造函数，加载配置
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        
        // 加载公众号配置
        $this->appId = Config::get('wechat.official_account.app_id', '');
        $this->appSecret = Config::get('wechat.official_account.secret', '');
    }

    /**
     * 上传图片为永久素材
     * 
     * @param Request $request
     * @return Json
     */
    public function uploadImage(Request $request): Json
    {
        try {
            // 获取上传的文件或图片URL
            $file = $request->file('image');
            $imageUrl = $request->param('image_url', '');
            
            if (empty($file) && empty($imageUrl)) {
                return json(['code' => 400, 'msg' => '请上传图片文件或提供图片URL']);
            }
            
            // 获取AccessToken
            $accessToken = $this->getAccessToken();
            if (empty($accessToken)) {
                return json(['code' => 500, 'msg' => '获取AccessToken失败']);
            }
            
            $mediaId = '';
            
            // 如果是文件上传
            if (!empty($file)) {
                // 验证文件类型
                $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
                $extension = strtolower($file->getOriginalExtension());
                
                if (!in_array($extension, $allowedTypes)) {
                    return json(['code' => 400, 'msg' => '不支持的图片格式，仅支持jpg、png、gif']);
                }
                
                // 验证文件大小 (2MB限制)
                if ($file->getSize() > 2 * 1024 * 1024) {
                    return json(['code' => 400, 'msg' => '图片大小不能超过2MB']);
                }
                
                // 保存临时文件
                $tempPath = $file->getRealPath();
                $mediaId = $this->uploadLocalImageAsPermanentMaterial($tempPath, $accessToken);
            }
            // 如果是URL上传
            else if (!empty($imageUrl)) {
                $mediaId = $this->uploadImageByUrl($imageUrl, $accessToken);
            }
            
            if (empty($mediaId)) {
                return json(['code' => 500, 'msg' => '上传图片失败']);
            }
            
            return json([
                'code' => 200, 
                'msg' => '图片上传成功', 
                'data' => [
                    'media_id' => $mediaId,
                    'upload_time' => date('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '上传图片异常：' . $e->getMessage()]);
        }
    }

    /**
     * 调用生成草稿接口
     * 
     * @param Request $request
     * @return Json
     */
    public function generateDraft(Request $request): Json
    {
        try {
            // 实例化草稿生成器
            $generator = new SimpleDraftGenerator($this->app);
            
            // 直接调用生成草稿方法
            return $generator->generateDraft($request);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '调用草稿生成接口失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量上传图片为永久素材
     * 
     * @param Request $request
     * @return Json
     */
    public function batchUploadImages(Request $request): Json
    {
        try {
            $files = $request->file('images');
            $imageUrls = $request->param('image_urls', []);
            
            if (empty($files) && empty($imageUrls)) {
                return json(['code' => 400, 'msg' => '请上传图片文件或提供图片URL列表']);
            }
            
            // 获取AccessToken
            $accessToken = $this->getAccessToken();
            if (empty($accessToken)) {
                return json(['code' => 500, 'msg' => '获取AccessToken失败']);
            }
            
            $results = [];
            $successCount = 0;
            $failCount = 0;
            
            // 处理文件上传
            if (!empty($files)) {
                foreach ($files as $index => $file) {
                    $result = [
                        'index' => $index,
                        'filename' => $file->getOriginalName(),
                        'success' => false,
                        'media_id' => '',
                        'error' => ''
                    ];
                    
                    try {
                        // 验证文件
                        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
                        $extension = strtolower($file->getOriginalExtension());
                        
                        if (!in_array($extension, $allowedTypes)) {
                            $result['error'] = '不支持的图片格式';
                        } else if ($file->getSize() > 2 * 1024 * 1024) {
                            $result['error'] = '图片大小超过2MB';
                        } else {
                            // 上传图片
                            $tempPath = $file->getRealPath();
                            $mediaId = $this->uploadLocalImageAsPermanentMaterial($tempPath, $accessToken);
                            
                            if (!empty($mediaId)) {
                                $result['success'] = true;
                                $result['media_id'] = $mediaId;
                                $successCount++;
                            } else {
                                $result['error'] = '上传失败';
                                $failCount++;
                            }
                        }
                    } catch (\Exception $e) {
                        $result['error'] = $e->getMessage();
                        $failCount++;
                    }
                    
                    $results[] = $result;
                }
            }
            
            // 处理URL上传
            if (!empty($imageUrls)) {
                foreach ($imageUrls as $index => $imageUrl) {
                    $result = [
                        'index' => $index,
                        'url' => $imageUrl,
                        'success' => false,
                        'media_id' => '',
                        'error' => ''
                    ];
                    
                    try {
                        $mediaId = $this->uploadImageByUrl($imageUrl, $accessToken);
                        
                        if (!empty($mediaId)) {
                            $result['success'] = true;
                            $result['media_id'] = $mediaId;
                            $successCount++;
                        } else {
                            $result['error'] = '上传失败';
                            $failCount++;
                        }
                    } catch (\Exception $e) {
                        $result['error'] = $e->getMessage();
                        $failCount++;
                    }
                    
                    $results[] = $result;
                }
            }
            
            return json([
                'code' => 200,
                'msg' => "批量上传完成，成功：{$successCount}个，失败：{$failCount}个",
                'data' => [
                    'success_count' => $successCount,
                    'fail_count' => $failCount,
                    'results' => $results
                ]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '批量上传图片异常：' . $e->getMessage()]);
        }
    }

    /**
     * 获取永久素材列表
     * 
     * @param Request $request
     * @return Json
     */
    public function getMaterialList(Request $request): Json
    {
        try {
            $type = $request->param('type', 'image'); // image, video, voice, news
            $offset = $request->param('offset', 0, 'intval');
            $count = $request->param('count', 20, 'intval');
            
            // 获取AccessToken
            $accessToken = $this->getAccessToken();
            if (empty($accessToken)) {
                return json(['code' => 500, 'msg' => '获取AccessToken失败']);
            }
            
            $url = "https://api.weixin.qq.com/cgi-bin/material/batchget_material?access_token={$accessToken}";
            
            $data = [
                'type' => $type,
                'offset' => $offset,
                'count' => min($count, 20) // 微信限制最多20个
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                return json(['code' => 500, 'msg' => '请求失败: ' . $error]);
            }
            
            $result = json_decode($response, true);
            if (!$result) {
                return json(['code' => 500, 'msg' => '响应解析失败']);
            }
            
            if (isset($result['errcode']) && $result['errcode'] != 0) {
                return json(['code' => 500, 'msg' => '获取素材列表失败: ' . ($result['errmsg'] ?? '未知错误')]);
            }
            
            return json([
                'code' => 200,
                'msg' => '获取素材列表成功',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取素材列表异常：' . $e->getMessage()]);
        }
    }

    /**
     * 删除永久素材
     *
     * @param Request $request
     * @return Json
     */
    public function deleteMaterial(Request $request): Json
    {
        try {
            $mediaId = $request->param('media_id', '');

            if (empty($mediaId)) {
                return json(['code' => 400, 'msg' => '请提供要删除的素材media_id']);
            }

            // 获取AccessToken
            $accessToken = $this->getAccessToken();
            if (empty($accessToken)) {
                return json(['code' => 500, 'msg' => '获取AccessToken失败']);
            }

            $url = "https://api.weixin.qq.com/cgi-bin/material/del_material?access_token={$accessToken}";

            $data = [
                'media_id' => $mediaId
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return json(['code' => 500, 'msg' => '请求失败: ' . $error]);
            }

            $result = json_decode($response, true);
            if (!$result) {
                return json(['code' => 500, 'msg' => '响应解析失败']);
            }

            if (isset($result['errcode']) && $result['errcode'] != 0) {
                return json(['code' => 500, 'msg' => '删除素材失败: ' . ($result['errmsg'] ?? '未知错误')]);
            }

            return json([
                'code' => 200,
                'msg' => '删除素材成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '删除素材异常：' . $e->getMessage()]);
        }
    }

    /**
     * 获取微信公众号的AccessToken
     *
     * @return string 成功返回AccessToken，失败返回空字符串
     */
    private function getAccessToken(): string
    {
        try {
            // 构建获取AccessToken的URL
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appId}&secret={$this->appSecret}";

            // 发送GET请求
            $response = file_get_contents($url);
            if (!$response) {
                return '';
            }

            // 解析响应
            $result = json_decode($response, true);
            if (!$result || isset($result['errcode'])) {
                return '';
            }

            // 返回AccessToken
            return $result['access_token'] ?? '';

        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * 将本地图片文件上传为微信永久素材
     *
     * @param string $localPath 本地图片路径
     * @param string $accessToken 微信AccessToken
     * @return string|null 成功返回media_id，失败返回null
     */
    private function uploadLocalImageAsPermanentMaterial(string $localPath, string $accessToken): ?string
    {
        // 检查文件是否存在
        if (!file_exists($localPath)) {
            return null;
        }

        // 获取图片MIME类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $localPath);
        finfo_close($finfo);

        // 确保MIME类型满足微信要求
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($mimeType, $allowedMimeTypes)) {
            return null;
        }

        // 检查文件大小
        $fileSize = filesize($localPath);

        // 检查文件大小是否超过微信限制 (2MB)
        if ($fileSize > 2 * 1024 * 1024) {
            return null;
        }

        // 使用永久素材接口上传图片
        $url = "https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={$accessToken}&type=image";

        // 获取文件后缀名
        $fileExt = strtolower(pathinfo($localPath, PATHINFO_EXTENSION));
        if (empty($fileExt) || !in_array($fileExt, ['jpg', 'jpeg', 'png', 'gif'])) {
            // 根据MIME类型设置正确的扩展名
            if ($mimeType == 'image/jpeg') {
                $fileExt = 'jpg';
            } elseif ($mimeType == 'image/png') {
                $fileExt = 'png';
            } elseif ($mimeType == 'image/gif') {
                $fileExt = 'gif';
            } else {
                $fileExt = 'jpg';
            }
        }

        $curl = curl_init();

        $data = [
            'media' => new \CURLFile($localPath, $mimeType, 'image.' . $fileExt)
        ];

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        if ($httpCode !== 200 || curl_errno($curl)) {
            curl_close($curl);
            return null;
        }

        curl_close($curl);

        // 解析响应内容
        $result = json_decode($response, true);
        if (isset($result['media_id'])) {
            return $result['media_id'];
        } else {
            return null;
        }
    }

    /**
     * 通过URL上传图片并存储为永久素材
     *
     * @param string $imageUrl 图片URL
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回media_id，失败返回空字符串
     */
    private function uploadImageByUrl(string $imageUrl, string $accessToken): string
    {
        // 从URL获取文件扩展名
        $pathInfo = pathinfo($imageUrl);
        $extension = isset($pathInfo['extension']) ? strtolower($pathInfo['extension']) : 'jpg';

        // 确保扩展名是微信支持的
        if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            $extension = 'jpg';  // 默认使用jpg
        }

        // 下载图片到临时文件 - 确保包含正确的扩展名
        $tempFilePath = tempnam(sys_get_temp_dir(), 'wx_img');
        $tempFilePathWithExt = $tempFilePath . '.' . $extension;
        rename($tempFilePath, $tempFilePathWithExt); // 重命名临时文件以包含扩展名

        // 下载图片内容
        $imageData = @file_get_contents($imageUrl);
        if (!$imageData) {
            return '';
        }

        // 保存图片到临时文件
        file_put_contents($tempFilePathWithExt, $imageData);

        // 获取图片MIME类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $tempFilePathWithExt);
        finfo_close($finfo);

        // 确保MIME类型满足微信要求
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($mimeType, $allowedMimeTypes)) {
            @unlink($tempFilePathWithExt);
            return '';
        }

        // 获取图片文件大小
        $fileSize = filesize($tempFilePathWithExt);

        // 检查文件大小是否超过微信限制 (2MB)
        if ($fileSize > 2 * 1024 * 1024) {
            @unlink($tempFilePathWithExt);
            return '';
        }

        // 使用永久素材接口上传图片
        $mediaId = $this->uploadLocalImageAsPermanentMaterial($tempFilePathWithExt, $accessToken);

        // 删除临时文件
        @unlink($tempFilePathWithExt);

        return $mediaId;
    }
}
