<?php
namespace app\controller;

use app\BaseController;
use app\util\SecretUtil;
use think\facade\Db;
use think\facade\View;
use app\util\JwtUtil;

class Sen extends BaseController
{
    private $session;
    private const PJXT_URL = "https://spoc.buaa.edu.cn/pjxt/";
    private const LOGIN_URL = "https://sso.buaa.edu.cn/login?service=";
    private $cookieFile;
    private $secretKey;  // 改为私有属性

    public function __construct()
    {
        parent::__construct();

        // 获取SSO加密密钥
        $this->secretKey = SecretUtil::getEncryptionKey('sso_key');

        $this->cookieFile = runtime_path() . 'cookies_' . uniqid() . '.txt';

        $this->session = curl_init();
        curl_setopt($this->session, CURLOPT_COOKIEJAR, $this->cookieFile);
        curl_setopt($this->session, CURLOPT_COOKIEFILE, $this->cookieFile);
        curl_setopt($this->session, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($this->session, CURLOPT_FOLLOWLOCATION, true);
    }

        private function decrypt($encryptedText) {
        try {
            // Base64 解码
            $encryptedBytes = base64_decode($encryptedText);
            $keyBytes = [];
            
            // 将密钥转换为字节数组
            for ($i = 0; $i < strlen($this->secretKey); $i++) {
                $keyBytes[] = ord($this->secretKey[$i]);
            }
            
            // 解密
            $decryptedBytes = '';
            for ($i = 0; $i < strlen($encryptedBytes); $i++) {
                $decryptedBytes .= chr(ord($encryptedBytes[$i]) ^ $keyBytes[$i % count($keyBytes)]);
            }
            
            return $decryptedBytes;
        } catch (\Exception $e) {
            throw new \Exception('密码解密失败：' . $e->getMessage());
        }
    }

    public function login()
{
    try {
        // 获取POST提交的用户名、加密密码和token
        $username = input('post.username');
        $encryptedPassword = input('post.password');
        $token = input('post.token');

        // 验证token
        if (empty($token)) {
            return json(['code' => 0, 'msg' => '未授权，请先登录']);
        }

        // 验证token
        $tokenData = JwtUtil::validateToken($token);
        if (!$tokenData) {
            return json(['code' => 0, 'msg' => 'token无效或已过期']);
        }

        // 获取user_id
        $user_id = $tokenData['user_id'] ?? null;
        if (!$user_id) {
            return json(['code' => 0, 'msg' => 'token信息不完整']);
        }

        // 检查用户状态
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return json(['code' => 0, 'msg' => '用户不存在']);
        }
        // 检查请求频率
        $current_time = time();
        $last_request_time = !empty($user['last_request_time']) ? strtotime($user['last_request_time']) : 0;
        $request_count = $user['request_count'] ?? 0;

        if ($current_time - $last_request_time < 60) { // 一分钟内
            if ($request_count >= 3) {
                $wait_time = 60 - ($current_time - $last_request_time);
                return json(['code' => 0, 'msg' => "请求过于频繁，请在{$wait_time}秒后重试"]);
            }
            // 更新请求次数
            Db::name('user')->where('id', $user_id)->update([
                'request_count' => $request_count + 1
            ]);
        } else {
            // 超过一分钟，重置计数
            Db::name('user')->where('id', $user_id)->update([
                'last_request_time' => date('Y-m-d H:i:s', $current_time),
                'request_count' => 1
            ]);
        }
        // 检查用户状态是否允许更改（已认证用户也可以重新认证以保存凭据）
        $allowed_statuses = ['unverified', 'temporary_verified', 'verified'];
        if (!in_array($user['status'], $allowed_statuses)) {
            return json(['code' => 0, 'msg' => '当前用户状态不可更改，请联系管理员']);
        }

        // 检查学号是否已被其他账号认证
        $existing_user = Db::name('user')->where('xuehao', $username)->find();
        if ($existing_user && $existing_user['id'] != $user_id) {
            return json(['code' => 0, 'msg' => '该学号已被其他账号认证，请联系管理员']);
        }

        // 解密密码
        try {
            $password = $this->decrypt($encryptedPassword);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
        
        $token = $this->getToken();
        if (isset($token['code'])) {
            return json($token);
        }

        $loginUrl = self::LOGIN_URL . urlencode(self::PJXT_URL) . 'cas';
        $postData = [
            'username' => $username,
            'password' => $password,
            'execution' => $token,
            '_eventId' => 'submit',
            'type' => 'username_password',
            'submit' => 'LOGIN'
        ];

        curl_setopt($this->session, CURLOPT_URL, $loginUrl);
        curl_setopt($this->session, CURLOPT_POST, true);
        curl_setopt($this->session, CURLOPT_POSTFIELDS, http_build_query($postData));

        $response = curl_exec($this->session);
        if ($response === false) {
            throw new \Exception(curl_error($this->session));
        }

        if (strpos($response, '综合评教系统') !== false) {
            // 更新用户认证状态
            try {
                // 获取北航的学校信息（学院路校区，is_active=1）
                $beihangInfo = Db::name('beijing_universities')
                    ->where('university_id', 5)
                    ->where('is_active', 1)
                    ->find();

                // 检查用户是否已经认证过
                if ($user['status'] === 'verified' && $user['verified_university_id'] == 5) {
                    // 用户已认证，返回本地SSO账密更新成功的消息
                    $message = '本地SSO账密更新成功';
                } else {
                    // 用户未认证，更新认证信息
                    $updateData = [
                        'status' => 'verified',
                        'xuehao' => $username,
                        'verified_university_id' => 5, // 北航的university_id
                        'status_code' => Db::raw('status_code + 1')
                    ];

                    Db::name('user')->where('id', $user_id)->update($updateData);
                    $message = 'SSO认证成功，认证学校：北京航空航天大学';
                }

                // 返回认证结果和学校信息
                return json([
                    'code' => 1,
                    'msg' => $message,
                    'data' => [
                        'verified_school_info' => $beihangInfo
                    ]
                ]);
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '登录成功但更新认证状态失败：' . $e->getMessage()]);
            }
        } else {
            return json(['code' => 0, 'msg' => '密码错误或接口失效']);
        }

    } catch (\Exception $e) {
        return json(['code' => 0, 'msg' => '登录失败：' . $e->getMessage()]);
    }
}
    public function __destruct()
    {
        // 关闭CURL会话
        if ($this->session) {
            curl_close($this->session);
        }
        // 删除临时cookie文件
        if (file_exists($this->cookieFile)) {
            unlink($this->cookieFile);
        }
    }
    private function getToken()
    {
        try {
                $loginUrl = self::LOGIN_URL . urlencode(self::PJXT_URL) . 'cas';
            curl_setopt($this->session, CURLOPT_URL, $loginUrl);
            
            $response = curl_exec($this->session);
            if ($response === false) {
                throw new \Exception(curl_error($this->session));
            }

                // 使用DOMDocument解析HTML
            $dom = new \DOMDocument();
            @$dom->loadHTML($response);
            $xpath = new \DOMXPath($dom);
            
            // 查找execution输入字段
            $token = $xpath->query("//input[@name='execution']/@value")->item(0);
            
            if ($token) {
                return $token->nodeValue;
            }
            throw new \Exception('未找到登录令牌');
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取登录令牌失败：' . $e->getMessage()];
        }
    }

    // ... 其他方法保持不变 ...
}