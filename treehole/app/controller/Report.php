<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Request;
use app\util\JwtUtil;

class Report extends BaseController
{
    public function report()
    {
        try {
            $request = request();
            // 获取token，优先从header获取，其次从post参数获取
            $token = $request->header('token');
            if (empty($token)) {
                $token = $request->post('access_token');
            }
            if (empty($token)) {
                return json(['code' => 401, 'msg' => '未授权，请先登录']);
            }

            // 验证token
            $tokenData = JwtUtil::validateToken($token);
            if (!$tokenData || !isset($tokenData['user_id'])) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userid = $tokenData['user_id'];
            $post_id = $request->post('post_id');
            if (!$post_id) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 用户身份校验
            $user = Db::name('user')->where('id', $userid)->find();
            if (!$user) {
                return json(['code' => 404, 'msg' => '用户不存在']);
            }
            if ($user['status'] == 'unverified') {
                return json(['code' => 403, 'msg' => '请认证后再举报']);
            }
            if ($user['status'] == '禁言') {
                return json(['code' => 403, 'msg' => '你已被禁言，无法举报']);
            }

            // 检查是否已经举报过
            $exists = Db::name('report')->where('post_id', $post_id)->where('reporter_id', $userid)->find();
            if ($exists) {
                return json(['code' => 400, 'msg' => '您已经举报过该帖子']);
            }

            // 开启事务
            Db::startTrans();
            try {
                // 添加举报记录
                $result = Db::name('report')->insert([
                    'post_id' => $post_id,
                    'reporter_id' => $userid,
                    'create_time' => date('Y-m-d H:i:s')
                ]);
                if (!$result) {
                    throw new \Exception('举报失败');
                }
                // 检查举报数量
                $reportCount = Db::name('report')->where('post_id', $post_id)->count();
                $isDeleted = false;
                if ($reportCount >= 10) {
                    // 软删除：将choose字段设为998
                    $updateResult = Db::name('message')->where('id', $post_id)->update(['choose' => 998]);
                    if ($updateResult === false) {
                        throw new \Exception('更新帖子状态失败');
                    }
                    $isDeleted = true;
                }
                // 提交事务
                Db::commit();
                return json([
                    'code' => 200, 
                    'msg' => '举报成功',
                    'data' => [
                        'report_count' => $reportCount,
                        'is_deleted' => $isDeleted
                    ]
                ]);
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return json(['code' => 500, 'msg' => $e->getMessage()]);
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '服务器错误：' . $e->getMessage()]);
        }
    }

    public function getReportCount()
    {
        try {
            $request = request();
            // 获取token，优先从header获取，其次从post参数获取
            $token = $request->header('token');
            if (empty($token)) {
                $token = $request->post('access_token');
            }
            if (empty($token)) {
                return json(['code' => 401, 'msg' => '未授权，请先登录']);
            }

            // 验证token
            $tokenData = JwtUtil::validateToken($token);
            if (!$tokenData || !isset($tokenData['user_id'])) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }
            
            $post_id = $request->post('post_id');
            if (!$post_id) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            $reportCount = Db::name('report')->where('post_id', $post_id)->count();
            $isDeleted = Db::name('message')->where('id', $post_id)->value('choose') == 998 ? true : false;

            return json([
                'code' => 200,
                'data' => [
                    'report_count' => $reportCount,
                    'is_deleted' => $isDeleted
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '服务器错误：' . $e->getMessage()]);
        }
    }
} 