<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\Request;
use think\response\Json;

class Userstatus extends BaseController
{
    public function resetTemporaryStatus()
    {
        // 更新所有处于临时认证状态的用户为未认证状态
        $affectedRows = M('users')
            ->where(['status' => 'temporary_verified'])
            ->save(['status' => 'unverified']);

        // 输出执行结果
        echo "Cron job executed successfully at " . date('Y-m-d H:i:s') . ". " . $affectedRows . " users updated.\n";
    }
}
