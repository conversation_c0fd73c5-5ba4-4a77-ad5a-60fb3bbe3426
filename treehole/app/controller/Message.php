<?php
namespace app\controller;

use app\BaseController;
use app\util\JwtUtil;
use app\util\ImageSecurityUtil;
use think\facade\Db;
use think\facade\View;
use think\Request;
use think\response\Json;

class Message extends BaseController
{
    public function getGridConfig()
    {
        // 假设条件决定是否显示 id=4 和 id=99
        $showExtraItems = true; // 根据需求调整逻辑

        return json([
            'status' => 1,
            'show_extra_items' => $showExtraItems,
        ]);
    }
    public function getGridConfig2()
    {
        // 假设条件决定是否显示 id=4 和 id=99
        $showExtraItems = true; // 根据需求调整逻辑

        return json([
            'status' => 1,
            'show_extra_items' => $showExtraItems,
        ]);
    }
    public function uploadImage(Request $request): Json
    {
        if ($file = $request->file('file')) {
            try {
                // 严格的文件安全验证
                $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
                if (!$securityCheck['success']) {
                    return json(['error_code' => 1, 'msg' => $securityCheck['message']]);
                }

                // 使用相对路径，指向public/file目录
                $uploadPath = 'file/';

                // 按日期创建子目录
                $currentDate = date('Y-m-d');
                $uploadPath .= $currentDate . '/';

                // 确保目录存在
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }

                // 根据文件内容生成安全的文件名
                $safeExtension = ImageSecurityUtil::getSafeExtension($securityCheck['mime_type']);
                $saveName = ImageSecurityUtil::generateSafeFileName('msg', $safeExtension);
                
                // 移动文件
                $info = $file->move($uploadPath, $saveName);
                if (!$info) {
                    return json(['error_code' => 1, 'msg' => '文件上传失败']);
                }

                // 生成相对路径
                $relativePath = '/file/' . $currentDate . '/' . $saveName;
                
                // 使用固定的域名
                $baseUrl = 'https://www.bjgaoxiaoshequ.store';
                $fullUrl = $baseUrl . $relativePath;

                return json([
                    'error_code' => 0, 
                    'msg' => '上传成功',
                    'data' => [
                        'image_url' => $fullUrl  // 返回完整的HTTPS URL
                    ]
                ]);
                
            } catch (\Exception $e) {
                return json(['error_code' => 1, 'msg' => '上传失败：' . $e->getMessage()]);
            }
        }
        return json(['error_code' => 1, 'msg' => '没有上传文件']);
    }

        public function publish(Request $request): Json
    {
        // Token验证
        $token = $request->header('token');
        if (!$token) {
            return json(['error_code' => 401, 'msg' => '请先登录']);
        }

        $userData = JwtUtil::validateToken($token);
        if (!$userData) {
            return json(['error_code' => 401, 'msg' => 'token无效或已过期']);
        }

        // 检查用户状态是否为禁言
        $user = Db::name('user')->where('id', $userData['user_id'])->find();
        if (!$user) {
            return json(['error_code' => 404, 'msg' => '用户不存在']);
        }
        if ($user['status'] === '禁言') {
            return json(['error_code' => 403, 'msg' => '您已被禁言，无法发布内容']);
        }

        $params = $request->post();

        // 添加调试日志
        trace('发布消息接收到的参数：' . json_encode($params, JSON_UNESCAPED_UNICODE), 'debug');

        // 检查images参数
        if (isset($params['images'])) {
            trace('接收到的images参数：' . (is_string($params['images']) ? $params['images'] : json_encode($params['images'])), 'debug');
        } else {
            trace('未接收到images参数', 'debug');
        }

        // 验证用户ID是否匹配
        if ((int)$params['user_id'] !== (int)$userData['user_id']) {
            return json(['error_code' => 403, 'msg' => '用户身份验证失败']);
        }

        // 校验必需的字段
        foreach (['user_id', 'username', 'face_url'] as $param) {
            if (empty($params[$param])) {
                return json(['error_code' => 2, 'msg' => '参数不足：' . $param]);
            }
        }
        if (empty($params['content'])) {
            return json(['error_code' => 2, 'msg' => '请输入内容']);
        }

        Db::startTrans();
        try {
            // 处理 images 字段
            $images = $params['images'] ?? '[]';
            if (is_string($images)) {
                $images = json_decode($images, true);
            }
            
            // 过滤掉null和空字符串
            if (is_array($images)) {
                $images = array_filter($images, function($url) {
                    return $url !== null && $url !== '';
                });
            } else {
                $images = [];
            }
            
            // 确保images字符串长度不超过2000
            $imagesJson = json_encode($images, JSON_UNESCAPED_SLASHES);
            if (strlen($imagesJson) > 2000) {
                return json(['error_code' => 2, 'msg' => '图片数量过多']);
            }

            // 检查是否为匿名发布
            $isAnonymous = (int)($params['is_anonymous'] ?? 0);

            // 如果是匿名发布，生成匿名信息
            if ($isAnonymous) {
                // 临时使用用户ID生成匿名信息，后续会在插入后用真实的message_id重新生成
                $anonymousUtil = new \app\utils\AnonymousUtil();
                $tempAnonymousName = $anonymousUtil::generateAnonymousName($params['user_id'], time());
                $tempAnonymousAvatar = $anonymousUtil::generateAnonymousAvatar($params['user_id'], time());

                $username = $tempAnonymousName;
                $faceUrl = $tempAnonymousAvatar;
                $titlename = '';
                $titlecolor = 0;
            } else {
                $username = substr($params['username'], 0, 50);
                $faceUrl = $params['face_url'] ?? 'https://www.bjgaoxiaoshequ.store/images/weixiao.png';
                $titlename = substr($params['titlename'] ?? '', 0, 50);
                $titlecolor = (int)($params['titlecolor'] ?? 0);
            }

            // 根据校园交易的价格类型设置ispull字段和flag字段
            $ispull = 1; // 默认值
            $flag = 0; // 默认值：普通消息
            if ((int)($params['choose'] ?? 0) === 3) { // 校园交易
                if (isset($params['price_type']) && $params['price_type'] === 'urgent') {
                    $ispull = 2; // 低价急出
                    $flag = 2; // 低价急出标识
                } else {
                    $ispull = 1; // 普通价格出售
                    $flag = 0; // 普通消息标识
                }
            }

            // 组装消息数据
            $messageData = [
                'choose' => (int)($params['choose'] ?? 0),  // 确保choose为整数
                'user_id' => (int)$params['user_id'],      // 确保user_id为整数
                'username' => $username,
                'face_url' => $faceUrl,
                'content' => mb_convert_encoding(substr($params['content'], 0, 5000), 'UTF-8', 'UTF-8'),   // 确保UTF-8编码  // 限制长度
                'jine' => substr($params['jine'] ?? '', 0, 100),
                'weizhi' => substr($params['weizhi'] ?? '', 0, 200),
                'titlename' => $titlename,
                'titlecolor' => $titlecolor,
                'vx' => substr($params['vx'] ?? '', 0, 50),
                'qq' => substr($params['qq'] ?? '', 0, 50),
                'phonenumber' => substr($params['phonenumber'] ?? '', 0, 50),
                'total_likes' => 0,
                'send_timestamp' => time(),
                'images' => $imagesJson,
                'ispull' => $ispull,
                'has_vote' => 0,
                'is_anonymous' => $isAnonymous,
                'flag' => $flag
            ];

            // 处理投票数据
            if (!empty($params['voteData'])) {
                $voteData = is_string($params['voteData']) ? 
                           json_decode($params['voteData'], true) : 
                           $params['voteData'];

                if (!empty($voteData['options'])) {
                    // 验证投票选项
                    if (count($voteData['options']) < 2 || count($voteData['options']) > 5) {
                        return json(['error_code' => 2, 'msg' => '投票选项数量必须在2-5个之间']);
                    }

                    $messageData['has_vote'] = 1;
                }
            }

            // 插入消息数据
            $message_id = Db::name('message')->insertGetId($messageData);

            // 如果是匿名发布，使用真实的message_id重新生成匿名信息并缓存
            if ($isAnonymous) {
                $anonymousUtil = new \app\utils\AnonymousUtil();
                $realAnonymousName = $anonymousUtil::generateAnonymousName($params['user_id'], $message_id);
                $realAnonymousAvatar = $anonymousUtil::generateAnonymousAvatar($params['user_id'], $message_id);

                // 更新消息的匿名信息
                Db::name('message')->where('id', $message_id)->update([
                    'username' => $realAnonymousName,
                    'face_url' => $realAnonymousAvatar
                ]);

                // 缓存匿名信息到映射表
                try {
                    Db::name('anonymous_mapping')->insert([
                        'message_id' => $message_id,
                        'user_id' => $params['user_id'],
                        'anonymous_name' => $realAnonymousName,
                        'anonymous_avatar' => $realAnonymousAvatar,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                } catch (\Exception $e) {
                    // 如果插入失败（可能是重复键），忽略错误
                    error_log("匿名映射缓存失败: " . $e->getMessage());
                }
            }

            // 如果包含投票，创建投票记录
            if ($messageData['has_vote']) {
                $voteInsertData = [
                    'message_id' => $message_id,
                    'vote_type' => $voteData['type'] ?? 'single',
                    'title' => substr($voteData['title'] ?? '', 0, 255),
                    'options' => json_encode($voteData['options'], JSON_UNESCAPED_UNICODE),  // 保留 JSON 编码
                    'deadline' => !empty($voteData['deadline']) ? 
                                date('Y-m-d H:i:s', strtotime($voteData['deadline'])) : 
                                null,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                // 插入投票数据并获取自增ID
                $vote_id = Db::name('vote')->insertGetId($voteInsertData);
                
                // 更新消息的vote_id
                Db::name('message')->where('id', $message_id)
                                  ->update(['vote_id' => $vote_id]);
            }

            // 仅对 choose 小于 100 的信息生成 scheme 并推送消息
            if ((int)$messageData['choose'] < 100) {
                // 获取微信小程序配置
                $wechatConfig = \app\util\SecretUtil::getWechatMiniprogram('main');
                $appid = $wechatConfig['appid'];
                $appsecret = $wechatConfig['secret'];

                // 获取 access_token
                $token_url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$appsecret}";
                $token_response = file_get_contents($token_url);
                $token_result = json_decode($token_response, true);

                if (isset($token_result['access_token'])) {
                    $access_token = $token_result['access_token'];

                    // 生成 scheme 码
                    $scheme_url = "https://api.weixin.qq.com/wxa/genwxashortlink?access_token={$access_token}";
                    // 构建请求参数
                    $post_data = [
                        'page_url' => 'packageEmoji/pages/messageDetail/messageDetail?id=' . $message_id,
                        'is_permanent' => false
                    ];

                    // 编码为 JSON 字符串
                    $json_data = json_encode($post_data);
                    if ($json_data === false) {
                        Db::rollback();
                        return json(['error_code' => 2, 'msg' => 'JSON 编码失败', 'detail' => json_last_error_msg()]);
                    }

                    // 使用 curl 发送 POST 请求
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $scheme_url);
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    // 设置 header
                    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                    $scheme_response = curl_exec($ch);
                    if ($scheme_response === false) {
                        $curl_error = curl_error($ch);
                        curl_close($ch);
                        Db::rollback();
                        return json(['error_code' => 2, 'msg' => 'cURL 请求失败', 'detail' => $curl_error]);
                    }
                    curl_close($ch);

                    $scheme_result = json_decode($scheme_response, true);
                    if ($scheme_result === null) {
                        Db::rollback();
                        return json(['error_code' => 2, 'msg' => '响应 JSON 解码失败', 'detail' => json_last_error_msg()]);
                    }

                    if (isset($scheme_result['link'])) {
                        $scheme = $scheme_result['link'];

                        // 更新数据库，保存 scheme
                        Db::name('message')->where('id', $message_id)->update(['scheme' => $scheme]);
                    } else {
                        // 处理生成 scheme 失败的情况
                        Db::rollback();
                        return json(['error_code' => 2, 'msg' => '生成 scheme 码失败', 'detail' => $scheme_result]);
                    }
                } else {
                    // 处理获取 access_token 失败的情况
                    Db::rollback();
                    return json(['error_code' => 2, 'msg' => '获取 access_token 失败', 'detail' => $token_result]);
                }
            }

            // 所有操作成功后，提交事务
            Db::commit();

            // 检查是否为意见反馈（choose = 102）
            if ((int)$messageData['choose'] === 102) {
                // 发送模板消息通知管理员
                $this->sendFeedbackNotification($user['openid'], $messageData['content'], $messageData['username']);
            }

            // 返回成功信息和消息ID
            return json([
                'error_code' => 0,
                'msg' => '发布成功',
                'data' => [
                    'message_id' => $message_id
                ]
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return json(['error_code' => 2, 'msg' => '发布失败：' . $e->getMessage()]);
        }
    }

    /**
     * 发送意见反馈模板消息通知
     */
    private function sendFeedbackNotification($userOpenid, $content, $username)
    {
        try {
            // 调用WechatTemplate的静态方法
            \app\controller\WechatTemplate::sendFeedbackNotification($userOpenid, $content, $username);

        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            error_log("发送意见反馈通知异常: " . $e->getMessage());
        }
    }


    public function getMessages(Request $request): Json
    {
        $page = $request->post('page', 1);
        $limit = 10;
        $choose = $request->post('choose', '');
        $userId = $request->post('user_id', 0);

        $conditions = [];
        if ($choose === '1') {
            $conditions[] = ['choose', '<=', 100];
            // 树洞消息不显示flag=2的消息（低价急出）
            $conditions[] = ['flag', '<>', 2];
        } elseif ($choose) {
            $conditions[] = ['choose', '=', $choose];
        }

        $offset = ($page - 1) * $limit;
        $allMessages = Db::name('message')
            ->where($conditions)
            ->order('id', 'desc')
            ->limit($offset, $limit)
            ->select()
            ->toArray();

        // 只给第一页（最新）的5条消息增加浏览量
        if ($page === 1) {
            Db::startTrans();
            try {
                // 只处理前5条消息
                $messagesToUpdate = array_slice($allMessages, 0, 5);
                foreach ($messagesToUpdate as $message) {
                    Db::name('message')
                        ->where('id', $message['id'])
                        ->inc('views', 1)
                        ->update();
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                // 记录错误但不影响正常显示
            }
        }

        foreach ($allMessages as &$message) {
            // 转换choose为字符串
            $message['choose'] = (string)$message['choose'];
            
            // 获取评论和回复数量
            $commentCount = Db::name('comment')
                ->where('message_id', $message['id'])
                ->where('is_deleted', 0)
                ->count();
            $postCount = Db::name('post')
                ->where('message_id', $message['id'])
                ->where('is_deleted', 0)
                ->count();
            $message['total_pinglun'] = $commentCount + $postCount;

            // 获取实际点赞数
            $message['total_likes'] = Db::name('unified_likes')
                ->where('target_type', 'message')
                ->where('target_id', $message['id'])
                ->count();

            // 检查当前用户是否点赞
            if ($userId) {
                $message['is_liked'] = Db::name('unified_likes')
                    ->where('target_type', 'message')
                    ->where('target_id', $message['id'])
                    ->where('user_id', $userId)
                    ->count() > 0;
            } else {
                $message['is_liked'] = false;
            }

            // 处理时间戳显示
            $timestamp = $message['send_timestamp'];
            $messageDate = date('Y-m-d', $timestamp);
            $currentDate = date('Y-m-d');

            if ($messageDate == $currentDate) {
                $message['send_timestamp'] = '今天 ' . date('H:i', $timestamp);
            } elseif ($messageDate == date('Y-m-d', strtotime('-1 day'))) {
                $message['send_timestamp'] = '昨天 ' . date('H:i', $timestamp);
            } else {
                $message['send_timestamp'] = date('n月j日 H:i', $timestamp);
            }

            // 处理投票数据
            if ($message['has_vote'] && $message['vote_id']) {
                $vote = Db::name('vote')->where('id', $message['vote_id'])->find();
                if ($vote) {
                    // 获取投票选项
                    $options = json_decode($vote['options'], true);
                    
                    // 获取投票记录
                    $voteRecords = Db::name('vote_record')
                        ->where('vote_id', $vote['id'])
                        ->select()
                        ->toArray();

                    // 统计每个选项的投票数
                    $results = [];
                    foreach ($voteRecords as $record) {
                        $optionIndex = $record['option_index'];
                        if (!isset($results[$optionIndex])) {
                            $results[$optionIndex] = 0;
                        }
                        $results[$optionIndex]++;
                    }

                    // 组装投票数据
                    $message['voteData'] = [
                        'title' => $vote['title'],
                        'type' => $vote['vote_type'],
                        'options' => $options,
                        'voters' => count($voteRecords),
                        'results' => $results,
                        'deadline' => $vote['deadline']
                    ];
                }
            }

            // 处理图片数据
            if (!empty($message['images'])) {
                $message['images'] = json_decode($message['images'], true);
            } else {
                $message['images'] = [];
            }

            // 处理匿名帖子数据
            if (isset($message['is_anonymous']) && $message['is_anonymous'] == 1) {
                $anonymousUtil = new \app\utils\AnonymousUtil();
                $message = $anonymousUtil::processAnonymousPostData($message, false);
            }
        }
        unset($message);

        return json([
            'status' => 1,
            'msg' => '获取成功',
            'data' => $allMessages
        ]);
    }

   public function getOneUserMessage(Request $request): Json
{
    $userId = $request->post('user_id');
    if (!$userId) {
        return json(['error_code' => 2, 'msg' => '请先登录']);
    }

    // 获取分页参数
    $page = $request->post('page', 1);
    $pageSize = 20; // 每页20条数据，避免节点过多
    $offset = ($page - 1) * $pageSize;

    $allMessages = Db::name('message')
        ->where('user_id', $userId)
        ->where('choose', '<', 200)
        ->order('id', 'desc')
        ->limit($offset, $pageSize)
        ->select()
        ->toArray(); // 将查询结果转换为数组

    foreach ($allMessages as &$message) {
        $message['choose'] = (string)$message['choose'];
        
        // 获取评论和回复数量
        $commentCount = Db::name('comment')
            ->where('message_id', (int)$message['id'])
            ->where('is_deleted', 0)
            ->count();
        $postCount = Db::name('post')
            ->where('message_id', (int)$message['id'])
            ->where('is_deleted', 0)
            ->count();
        $message['total_pinglun'] = $commentCount + $postCount;

        // 获取实际点赞数
        $message['total_likes'] = Db::name('unified_likes')
            ->where('target_type', 'message')
            ->where('target_id', $message['id'])
            ->count();

        // 检查当前用户是否点赞
        $message['is_liked'] = Db::name('unified_likes')
            ->where('target_type', 'message')
            ->where('target_id', $message['id'])
            ->where('user_id', $userId)
            ->count() > 0;

        // 处理时间戳显示
        $timestamp = $message['send_timestamp'];
        $messageDate = date('Y-m-d', $timestamp);
        $currentDate = date('Y-m-d');

        if ($messageDate == $currentDate) {
            $message['send_timestamp'] = '今天 ' . date('H:i', $timestamp);
        } elseif ($messageDate == date('Y-m-d', strtotime('-1 day'))) {
            $message['send_timestamp'] = '昨天 ' . date('H:i', $timestamp);
        } else {
            $message['send_timestamp'] = date('n月j日 H:i', $timestamp);
        }

        // 处理图片数据
        if (!empty($message['images'])) {
            $message['images'] = json_decode($message['images'], true);
        } else {
            $message['images'] = [];
        }

        // 处理匿名帖子数据
        if (isset($message['is_anonymous']) && $message['is_anonymous'] == 1) {
            $anonymousUtil = new \app\utils\AnonymousUtil();
            $message = $anonymousUtil::processAnonymousPostData($message, true);
        }

        // 处理投票数据
        if ($message['has_vote'] && $message['vote_id']) {
            $vote = Db::name('vote')->where('id', $message['vote_id'])->find();
            if ($vote) {
                // 获取投票选项
                $options = json_decode($vote['options'], true);
                
                // 获取投票记录
                $voteRecords = Db::name('vote_record')
                    ->where('vote_id', $vote['id'])
                    ->select()
                    ->toArray();

                // 统计每个选项的投票数
                $results = [];
                foreach ($voteRecords as $record) {
                    $optionIndex = $record['option_index'];
                    if (!isset($results[$optionIndex])) {
                        $results[$optionIndex] = 0;
                    }
                    $results[$optionIndex]++;
                }

                // 组装投票数据
                $message['voteData'] = [
                    'title' => $vote['title'],
                    'type' => $vote['vote_type'],
                    'options' => $options,
                    'voters' => count($voteRecords),
                    'results' => $results,
                    'deadline' => $vote['deadline']
                ];
            }
        }
    }

    return json(['error_code' => 0, 'msg' => '数据获取成功', 'data' => $allMessages]);
}

    public function searchMessages(Request $request): Json
{
    $page = $request->post('page', 1);
    $limit = 10;
    $keyword = $request->post('keyword', '');

    $conditions = [];
    if ($keyword) {
        $conditions[] = ['content', 'like', '%' . $keyword . '%'];
    }
    $conditions[] = ['choose', '<', 100];
    $offset = ($page - 1) * $limit;
    $allMessages = Db::name('message')
        ->where($conditions)
        ->order('id', 'desc')
        ->limit($offset, $limit)
        ->select()
        ->toArray(); // 将查询结果转换为数组
// 使用事务更新浏览量
    Db::startTrans();
    try {
        foreach ($allMessages as $message) {
            Db::name('message')
                ->where('id', $message['id'])
                ->inc('views', 1)
                ->update();
        }
        Db::commit();
    } catch (\Exception $e) {
        Db::rollback();
        // 记录错误但不影响正常显示
    }
    $commentModel = Db::name('comment');
    $postModel = Db::name('post');

    foreach ($allMessages as &$message) {
        $message['choose'] = (string)$message['choose'];
        $commentCount =Db::name('comment')
            ->where('message_id', $message['id'])
            ->where('is_deleted', 0)
            ->count();
        $postCount = Db::name('post')
            ->where('message_id', $message['id'])
            ->where('is_deleted', 0)
            ->count();
        $message['total_pinglun'] = $commentCount + $postCount;

        $timestamp = $message['send_timestamp'];
        $messageDate = date('Y-m-d', $timestamp);
        $currentDate = date('Y-m-d');
        if ($messageDate == $currentDate) {
            $message['send_timestamp'] = '今天 ' . date('H:i', $timestamp);
        } elseif ($messageDate == date('Y-m-d', strtotime('-1 day'))) {
            $message['send_timestamp'] = '昨天 ' . date('H:i', $timestamp);
        } else {
            $message['send_timestamp'] = date('n月j日 H:i', $timestamp);
        }
    }

    return json(['error_code' => 0, 'msg' => '数据获取成功', 'data' => $allMessages]);
}

    public function doLike(Request $request): Json
    {
        // Token验证
        $token = $request->header('token');
        if (!$token) {
            return json(['error_code' => 401, 'msg' => '请先登录']);
        }

        $userData = JwtUtil::validateToken($token);
        if (!$userData) {
            return json(['error_code' => 401, 'msg' => 'token无效或已过期']);
        }

        // 检查用户状态
        $user = Db::name('user')->where('id', $userData['user_id'])->find();
        if (!$user) {
            return json(['error_code' => 404, 'msg' => '用户不存在']);
        }
        if ($user['status'] === '禁言') {
            return json(['error_code' => 403, 'msg' => '您已被禁言，无法进行+1操作']);
        }

        $messageId = $request->post('message_id');
        $userId = $request->post('user_id');

        if (!$messageId || !$userId) {
            return json(['error_code' => 1, 'msg' => '参数不足']);
        }

        // 验证用户ID是否匹配
        if ((int)$userId !== (int)$userData['user_id']) {
            return json(['error_code' => 403, 'msg' => '用户身份验证失败']);
        }

        $message = Db::name('message')->where('id', $messageId)->find();

        if (!$message) {
            return json(['error_code' => 1, 'msg' => '数据不存在']);
        }

        $result = Db::name('message')
            ->where('id', $messageId)
            ->inc('total_likes', 1)
            ->update();

        if ($result) {
            return json(['error_code' => 0, 'msg' => '点赞成功', 'data' => ['message_id' => $messageId, 'total_likes' => $message['total_likes'] + 1]]);
        } else {
            return json(['error_code' => 1, 'msg' => '点赞失败']);
        }
    }

    public function doLike2(Request $request): Json
    {
        // Token验证
        $token = $request->header('token');
        if (!$token) {
            return json(['error_code' => 401, 'msg' => '请先登录']);
        }

        $userData = JwtUtil::validateToken($token);
        if (!$userData) {
            return json(['error_code' => 401, 'msg' => 'token无效或已过期']);
        }

        // 检查用户状态
        $user = Db::name('user')->where('id', $userData['user_id'])->find();
        if (!$user) {
            return json(['error_code' => 404, 'msg' => '用户不存在']);
        }
        if ($user['status'] === '禁言') {
            return json(['error_code' => 403, 'msg' => '您已被禁言，无法进行+1操作']);
        }

        $messageId = $request->post('message_id');
        $userId = $request->post('user_id');
        $commentId = $request->post('comment_id');

        if (!$messageId || !$userId || !$commentId) {
            return json(['error_code' => 1, 'msg' => '参数不足']);
        }

        // 验证用户ID是否匹配
        if ((int)$userId !== (int)$userData['user_id']) {
            return json(['error_code' => 403, 'msg' => '用户身份验证失败']);
        }

        $comment = Db::name('comment')->where('id', $commentId)->find();

        if (!$comment) {
            return json(['error_code' => 1, 'msg' => '评论数据不存在']);
        }

        $result = Db::name('comment')
            ->where('id', $commentId)
            ->inc('total_likes', 1)
            ->update();

        if ($result) {
            return json(['error_code' => 0, 'msg' => '点赞成功', 'data' => ['comment_id' => $commentId, 'total_likes' => $comment['total_likes'] + 1]]);
        } else {
            return json(['error_code' => 1, 'msg' => '点赞失败']);
        }
    }
    public function getMessageDetails(Request $request): Json
    {
        $id = $request->post('id', 0);
        $userId = $request->post('user_id', 0);

        // 验证 id 参数
        if (!$id) {
            return json(['error_code' => 1, 'msg' => '缺少参数 id', 'data' => null]);
        }

        // 查询消息详情
        $message = Db::name('message')->where('id', $id)->find();

        if (!$message) {
            return json(['error_code' => 1, 'msg' => '消息不存在', 'data' => null]);
        }
        // 检查帖子是否已被删除或限制访问
        if ($message['choose'] >= 200) {
            return json(['error_code' => 1, 'msg' => '该帖子已被删除', 'data' => null]);
        }
        // 增加浏览量（使用事务确保数据一致性）
        Db::startTrans();
        try {
            // 更新浏览量
            Db::name('message')
                ->where('id', $id)
                ->inc('views', 1)
                ->update();
            
            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            // 记录错误但不影响正常显示
        }

        // 获取评论和回复数量
        $commentCount = Db::name('comment')
            ->where('message_id', $message['id'])
            ->where('is_deleted', 0)
            ->count();
        $postCount = Db::name('post')
            ->where('message_id', $message['id'])
            ->where('is_deleted', 0)
            ->count();
        $message['total_pinglun'] = $commentCount + $postCount;

        // 获取实际点赞数
        $message['total_likes'] = Db::name('unified_likes')
            ->where('target_type', 'message')
            ->where('target_id', $message['id'])
            ->count();

        // 检查当前用户是否点赞
        if ($userId) {
            $message['is_liked'] = Db::name('unified_likes')
                ->where('target_type', 'message')
                ->where('target_id', $message['id'])
                ->where('user_id', $userId)
                ->count() > 0;
        } else {
            $message['is_liked'] = false;
        }

        // 格式化时间戳
        $timestamp = $message['send_timestamp'];
        $messageDate = date('Y-m-d', $timestamp);
        $currentDate = date('Y-m-d');

        if ($messageDate == $currentDate) {
            $message['send_timestamp'] = '今天 ' . date('H:i', $timestamp);
        } elseif ($messageDate == date('Y-m-d', strtotime('-1 day'))) {
            $message['send_timestamp'] = '昨天 ' . date('H:i', $timestamp);
        } else {
            $message['send_timestamp'] = date('n月j日 H:i', $timestamp);
        }

        // 处理 images 字段
        if (!empty($message['images'])) {
            $message['images'] = json_decode($message['images'], true);
        } else {
            $message['images'] = [];
        }
// 查询该消息相关的投票信息
        $vote = Db::name('vote')->where('message_id', $id)->find();
        
        if ($vote) {
            // 将投票数据添加到消息数据中
            $message['has_vote'] = true;
            $message['vote'] = [
                'id' => $vote['id'],
                'title' => $vote['title'],
                'vote_type' => $vote['vote_type'],
                'options' => json_decode($vote['options'], true),
                'deadline' => $vote['deadline'],
                'is_expired' => $vote['deadline'] && strtotime($vote['deadline']) < time(),
            ];
            
            // 获取投票统计数据
            $voteRecords = Db::name('vote_record')
                ->where('vote_id', $vote['id'])
                ->select()
                ->toArray();
                
            // 统计结果
            $results = [];
            foreach ($voteRecords as $record) {
                $optionIndex = $record['option_index'];
                if (!isset($results[$optionIndex])) {
                    $results[$optionIndex] = 0;
                }
                $results[$optionIndex]++;
            }
            
            $message['vote']['statistics'] = [
                'total_voters' => count($voteRecords),
                'options_count' => $results
            ];
            
            // 获取用户的投票选择
            if ($userId) {
                $userVote = Db::name('vote_record')
                    ->where([
                        'vote_id' => $vote['id'],
                        'user_id' => $userId
                    ])
                    ->select()
                    ->toArray();
                    
                $userChoices = [];
                foreach ($userVote as $record) {
                    $userChoices[] = $record['option_index'];
                }
                
                $message['vote']['has_voted'] = !empty($userVote);
                $message['vote']['user_choices'] = $userChoices;
            } else {
                $message['vote']['has_voted'] = false;
                $message['vote']['user_choices'] = [];
            }
        } else {
            $message['has_vote'] = false;
        }
        // 处理匿名帖子
        if (isset($message['is_anonymous']) && $message['is_anonymous'] == 1) {
            // 引入匿名工具类
            $anonymousUtil = new \app\utils\AnonymousUtil();

            // 生成匿名名称和头像
            $anonymousName = $anonymousUtil::generateAnonymousName($message['user_id'], $message['id']);
            $anonymousAvatar = $anonymousUtil::generateAnonymousAvatar($message['user_id'], $message['id']);

            // 保存原始用户ID用于判断是否是帖主
            $originalUserId = $message['user_id'];

            // 清理敏感信息
            unset($message['user_id']);
            unset($message['openid']);
            unset($message['phone']);
            unset($message['xuehao']);

            // 设置匿名显示信息
            $message['username'] = $anonymousName;
            $message['anonymous_name'] = $anonymousName; // 添加这个字段
            $message['face_url'] = $anonymousAvatar;
            $message['is_anonymous_display'] = true;
            $message['is_original_poster'] = true; // 在详情页中，这个帖子的作者就是帖主
            $message['titlename'] = '帖主';
            $message['original_poster_id'] = $originalUserId; // 用于评论区判断帖主
        }

        // 将 null 值转换为空字符串
        array_walk_recursive($message, function (&$item) {
            $item = $item === null ? '' : $item;
        });

        return json(['error_code' => 0, 'msg' => '数据获取成功', 'data' => $message]);
    }

    /**
     * 提交投票
     */
    public function submitVote(Request $request): Json
    {
        $params = $request->post();
        
        // 校验必需参数
        foreach (['vote_id', 'user_id', 'option_indexes'] as $param) {
            if (!isset($params[$param])) {
                return json(['error_code' => 2, 'msg' => '参数不足：' . $param]);
            }
        }

        try {
            // 开启事务
            Db::startTrans();
            
            // 获取投票信息
            $vote = Db::name('vote')->where('id', $params['vote_id'])->find();
            if (!$vote) {
                return json(['error_code' => 2, 'msg' => '投票不存在']);
            }

            // 检查投票是否已截止
            if ($vote['deadline'] && strtotime($vote['deadline']) < time()) {
                return json(['error_code' => 2, 'msg' => '投票已截止']);
            }

            // 检查是否已投票
            $existingVotes = Db::name('vote_record')
                ->where([
                    'vote_id' => $params['vote_id'],
                    'user_id' => $params['user_id']
                ])
                ->select()
                ->toArray();
            
            if (!empty($existingVotes)) {
                return json(['error_code' => 2, 'msg' => '您已经投过票了']);
            }

            // 获取选项
            $options = json_decode($vote['options'], true);
            
            // 处理投票选项
            $optionIndexes = is_array($params['option_indexes']) ? 
                            $params['option_indexes'] : 
                            [$params['option_indexes']];

            // 验证选项
            if ($vote['vote_type'] === 'single' && count($optionIndexes) > 1) {
                return json(['error_code' => 2, 'msg' => '单选投票只能选择一个选项']);
            }

            // 验证选项是否有效
            foreach ($optionIndexes as $index) {
                if ($index < 0 || $index >= count($options)) {
                    return json(['error_code' => 2, 'msg' => '无效的选项']);
                }
            }

            // 删除该用户之前的投票记录（如果有的话）
            Db::name('vote_record')
                ->where([
                    'vote_id' => $params['vote_id'],
                    'user_id' => $params['user_id']
                ])
                ->delete();

            // 记录新的投票
            foreach ($optionIndexes as $optionIndex) {
                Db::name('vote_record')->insert([
                    'vote_id' => $params['vote_id'],
                    'user_id' => $params['user_id'],
                    'option_index' => $optionIndex,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            // 提交事务
            Db::commit();

            // 获取最新投票统计
            $voteRecords = Db::name('vote_record')
                ->where('vote_id', $params['vote_id'])
                ->select()
                ->toArray();

            // 统计每个选项的票数
            $results = [];
            foreach ($voteRecords as $record) {
                $idx = $record['option_index'];
                if (!isset($results[$idx])) {
                    $results[$idx] = 0;
                }
                $results[$idx]++;
            }

            return json([
                'error_code' => 0,
                'msg' => '投票成功',
                'data' => [
                    'vote_id' => $params['vote_id'],
                    'current_results' => [
                        'total_voters' => count($voteRecords),
                        'options_count' => $results
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return json(['error_code' => 2, 'msg' => '投票失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取投票详情
     */
    public function getVoteDetails(Request $request): Json
    {
        try {
            $voteId = $request->post('vote_id');
            $userId = $request->post('user_id');

            if (!$voteId || !$userId) {
                return json(['error_code' => 2, 'msg' => '参数不足']);
            }

            // 获取投票信息
            $vote = Db::name('vote')->where('id', $voteId)->find();
            if (!$vote) {
                return json(['error_code' => 2, 'msg' => '投票不存在']);
            }

            // 检查用户是否已投票
            $userVote = Db::name('vote_record')
                ->where([
                    'vote_id' => $voteId,
                    'user_id' => $userId
                ])
                ->select()  // 改为select以获取所有选项
                ->toArray();

            // 获取所有投票记录
            $voteRecords = Db::name('vote_record')
                ->where('vote_id', $voteId)
                ->select()
                ->toArray();

            // 统计结果
            $results = [];
            foreach ($voteRecords as $record) {
                $optionIndex = $record['option_index'];
                if (!isset($results[$optionIndex])) {
                    $results[$optionIndex] = 0;
                }
                $results[$optionIndex]++;
            }

            // 获取用户的选择
            $userChoices = [];
            foreach ($userVote as $record) {
                $userChoices[] = $record['option_index'];
            }

            return json([
                'error_code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'vote_info' => [
                        'title' => $vote['title'],
                        'type' => $vote['vote_type'],
                        'options' => json_decode($vote['options'], true),
                        'deadline' => $vote['deadline'],
                        'is_expired' => $vote['deadline'] && strtotime($vote['deadline']) < time(),
                        'has_voted' => !empty($userVote),
                        'user_choices' => $userChoices  // 返回用户的所有选择
                    ],
                    'statistics' => [
                        'total_voters' => count($voteRecords),
                        'options_count' => $results
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json(['error_code' => 2, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    public function softDelete(Request $request): Json
    {
        // 从POST参数获取数据
        $messageId = $request->post('messageId');
        $userId = $request->post('userId');
        $token = $request->post('token');  // 从POST参数获取token
        
        // 参数验证
        if (!$messageId || !$userId || !$token) {
            return json([
                'error_code' => 1, 
                'msg' => '参数不足', 
                'debug' => [
                    'messageId' => $messageId,
                    'userId' => $userId,
                    'token' => $token,
                    'post_data' => $request->post()  // 输出所有POST数据用于调试
                ]
            ]);
        }

        // 查找帖子
        $message = Db::name('message')->where('id', $messageId)->find();
        if (!$message) {
            return json(['error_code' => 1, 'msg' => '帖子不存在']);
        }

        // 获取用户信息
        $user = Db::name('user')->where('id', $userId)->find();
        if (!$user) {
            return json(['error_code' => 1, 'msg' => '用户不存在']);
        }

        // 验证是否是帖子作者或管理员
        if ($message['user_id'] != $userId && $user['status'] != '管理员') {
            return json(['error_code' => 1, 'msg' => '无权限删除此帖子']);
        }

        try {
            // 软删除帖子（将choose设为999）
            $result = Db::name('message')
                ->where('id', $messageId)
                ->update(['choose' => 999]);

            if ($result) {
                return json(['error_code' => 0, 'msg' => '删除成功']);
            } else {
                return json(['error_code' => 1, 'msg' => '删除失败']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
    /**
     * 获取热门消息列表
     */
    public function getHotMessages(Request $request): Json
    {
        $page = $request->post('page', 1);
        $limit = $request->post('limit', 10);
        $userId = $request->post('user_id', 0);

        $offset = ($page - 1) * $limit;
        $threeDaysAgo = strtotime('-1 days');

        try {
            // 使用子查询统计评论和回复总数
            $commentCounts = Db::name('comment')
                ->field('message_id, COUNT(*) as comment_count')
                ->where('is_deleted', 0)
                ->group('message_id')
                ->buildSql();
            
            $postCounts = Db::name('post')
                ->field('message_id, COUNT(*) as post_count')
                ->where('is_deleted', 0)
                ->group('message_id')
                ->buildSql();
            
            $likeCounts = Db::name('unified_likes')
                ->field('target_id as message_id, COUNT(*) as like_count')
                ->where('target_type', 'message')
                ->group('target_id')
                ->buildSql();
            
            // 主查询，直接在SQL中计算热度
            $allMessages = Db::name('message')
                ->alias('m')
                ->leftJoin([$commentCounts => 'c'], 'm.id = c.message_id')
                ->leftJoin([$postCounts => 'p'], 'm.id = p.message_id')
                ->leftJoin([$likeCounts => 'l'], 'm.id = l.message_id')
                ->field('m.*, 
                        IFNULL(c.comment_count, 0) + IFNULL(p.post_count, 0) as total_pinglun,
                        IFNULL(l.like_count, 0) as total_likes,
                        (m.views + (IFNULL(c.comment_count, 0) + IFNULL(p.post_count, 0)) * 50 + IFNULL(l.like_count, 0) * 30) AS hot_score')
                ->where('m.choose', '<', 100)
                ->where('m.send_timestamp', '>=', $threeDaysAgo)
                ->where('m.flag', '<>', 2)  // 热门消息不显示flag=2的消息（低价急出）
                ->order('hot_score', 'desc')
                ->order('m.id', 'desc')
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            foreach ($allMessages as &$message) {
                $message['choose'] = (string)$message['choose'];
                
                // 检查当前用户是否点赞
                if ($userId) {
                    $message['is_liked'] = Db::name('unified_likes')
                        ->where('target_type', 'message')
                        ->where('target_id', $message['id'])
                        ->where('user_id', $userId)
                        ->count() > 0;
                } else {
                    $message['is_liked'] = false;
                }

                // 处理时间戳显示
                $timestamp = $message['send_timestamp'];
                $messageDate = date('Y-m-d', $timestamp);
                $currentDate = date('Y-m-d');
                if ($messageDate == $currentDate) {
                    $message['send_timestamp'] = '今天 ' . date('H:i', $timestamp);
                } elseif ($messageDate == date('Y-m-d', strtotime('-1 day'))) {
                    $message['send_timestamp'] = '昨天 ' . date('H:i', $timestamp);
                } else {
                    $message['send_timestamp'] = date('n月j日 H:i', $timestamp);
                }

                // 处理投票数据
                if ($message['has_vote'] && $message['vote_id']) {
                    $vote = Db::name('vote')->where('id', $message['vote_id'])->find();
                    if ($vote) {
                        $options = json_decode($vote['options'], true);
                        $voteRecords = Db::name('vote_record')
                            ->where('vote_id', $vote['id'])
                            ->select()
                            ->toArray();

                        $results = [];
                        foreach ($voteRecords as $record) {
                            $optionIndex = $record['option_index'];
                            if (!isset($results[$optionIndex])) {
                                $results[$optionIndex] = 0;
                            }
                            $results[$optionIndex]++;
                        }

                        $message['voteData'] = [
                            'title' => $vote['title'],
                            'type' => $vote['vote_type'],
                            'options' => $options,
                            'voters' => count($voteRecords),
                            'results' => $results,
                            'deadline' => $vote['deadline']
                        ];
                    }
                }

                // 处理图片数据
                if (!empty($message['images'])) {
                    $message['images'] = json_decode($message['images'], true);
                } else {
                    $message['images'] = [];
                }

                // 处理匿名帖子数据
                if (isset($message['is_anonymous']) && $message['is_anonymous'] == 1) {
                    $anonymousUtil = new \app\utils\AnonymousUtil();
                    $message = $anonymousUtil::processAnonymousPostData($message, false);
                }

                // 移除hot_score字段
                unset($message['hot_score']);
            }
            unset($message);

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $allMessages
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '获取热门消息失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 获取我发布的内容列表
     * @param Request $request
     * @return \think\Response
     */
    public function getMyPublished(Request $request)
    {
        try {
            // 验证token
            $token = $request->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }
            $userId = $payload['user_id'];
            $page = (int)$request->param('page', 1);
            $pageSize = (int)$request->param('page_size', 20);
            $offset = ($page - 1) * $pageSize;

            // 查询我发布的消息
            $messages = Db::table('message')
                ->alias('m')
                ->leftJoin('user u', 'm.user_id = u.id')
                ->where('m.user_id', $userId)
                ->where('m.flag', '<>', 2) // 排除被删除的消息
                ->field('m.*, u.username, u.face_url')
                ->order('m.send_timestamp desc')
                ->limit($offset, $pageSize)
                ->select();

            // 格式化数据
            $formattedMessages = [];
            foreach ($messages as $message) {
                // 处理图片
                $images = [];
                if (!empty($message['images'])) {
                    // images字段可能是JSON格式或者逗号分隔的字符串
                    $imageData = $message['images'];
                    if (is_string($imageData)) {
                        // 尝试解析JSON
                        $jsonImages = json_decode($imageData, true);
                        if (json_last_error() === JSON_ERROR_NONE && is_array($jsonImages)) {
                            $images = $jsonImages;
                        } else {
                            // 如果不是JSON，尝试按逗号分隔
                            $images = array_filter(explode(',', $imageData));
                        }
                    }
                }

                // 查询评论数量
                $commentCount = Db::table('comment')
                    ->where('message_id', $message['id'])
                    ->count();

                $formattedMessages[] = [
                    'id' => $message['id'],
                    'username' => $message['username'] ?: '匿名用户',
                    'face_url' => $message['face_url'] ?: '/images/weixiao.png',
                    'content' => $message['content'],
                    'choose' => $message['choose'],
                    'images' => $images,
                    'created_at' => date('Y-m-d H:i:s', $message['send_timestamp']),
                    'like_count' => $message['total_likes'] ?? 0,
                    'comment_count' => $commentCount
                ];
            }

            return json(['code' => 200, 'msg' => '获取成功', 'data' => $formattedMessages]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 格式化时间
     * @param string $datetime 时间字符串
     * @return string 格式化后的时间
     */
    private function formatTime($datetime)
    {
        $timestamp = strtotime($datetime);
        $now = time();
        $diff = $now - $timestamp;

        // 小于1小时，显示xx分钟前
        if ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes > 0 ? "{$minutes}分钟前" : "刚刚";
        }

        // 今天的消息，显示今天xx:xx
        if (date('Y-m-d', $timestamp) == date('Y-m-d')) {
            return '今天' . date('H:i', $timestamp);
        }

        // 昨天的消息，显示昨天xx:xx
        if (date('Y-m-d', $timestamp) == date('Y-m-d', strtotime('-1 day'))) {
            return '昨天' . date('H:i', $timestamp);
        }

        // 一周内的消息，显示x天前
        if ($diff < 7 * 24 * 3600) {
            $days = floor($diff / (24 * 3600));
            return "{$days}天前";
        }

        // 超过一周的消息，显示月-日
        return date('m月d日', $timestamp);
    }
}