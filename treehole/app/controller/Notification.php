<?php
declare (strict_types = 1);

namespace app\controller;

use think\Request;
use think\facade\Db;
use app\BaseController;
use app\util\JwtUtil;

class Notification extends BaseController
{
    /**
     * 格式化时间
     * @param string $datetime 时间字符串
     * @return string 格式化后的时间
     */
    private function formatTime($datetime)
    {
        $timestamp = strtotime($datetime);
        $now = time();
        $diff = $now - $timestamp;
        
        // 小于1小时，显示xx分钟前
        if ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes > 0 ? "{$minutes}分钟前" : "刚刚";
        }
        
        // 今天的消息，显示今天xx:xx
        if (date('Y-m-d', $timestamp) == date('Y-m-d')) {
            return '今天' . date('H:i', $timestamp);
        }
        
        // 昨天的消息，显示昨天xx:xx
        if (date('Y-m-d', $timestamp) == date('Y-m-d', strtotime('-1 day'))) {
            return '昨天' . date('H:i', $timestamp);
        }
        
        // 一周内的消息，显示x天前
        if ($diff < 7 * 24 * 3600) {
            $days = floor($diff / (24 * 3600));
            return "{$days}天前";
        }
        
        // 超过一周的消息，显示月-日
        return date('m月d日', $timestamp);
    }

    /**
     * 统一获取消息列表接口
     * @param Request $request
     * @return \think\Response
     */
    public function getMessages(Request $request)
    {
        try {
            // 从请求头获取token
            $token = $request->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            // 验证token
            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }
            $userId = $payload['user_id'];

            $type = $request->param('type', 'notifications');
            $page = (int)$request->param('page', 1);
            $pageSize = (int)$request->param('page_size', 20);

            // 统一处理所有类型的消息
            return $this->getNotificationData($userId, $type, $page, $pageSize);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }



    /**
     * 统一标记消息为已读接口
     * @param Request $request
     * @return \think\Response
     */
    public function markRead(Request $request)
    {
        try {
            // 从请求头获取token
            $token = $request->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            // 验证token
            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }
            $userId = $payload['user_id'];

            $type = $request->param('type', 'all'); // likes, replies, notifications, all
            $ids = $request->param('ids', []); // 指定要标记的消息ID数组
            $markAll = (bool)$request->param('mark_all', false); // 是否标记所有未读

            Db::startTrans();

            // 构建查询条件
            $query = Db::name('notification')
                ->where('user_id', $userId)
                ->where('is_read', 0);

            // 如果指定了具体的ID，只标记这些ID
            if (!empty($ids)) {
                $query->whereIn('id', $ids);
            } else {
                // 根据类型添加条件
                $typeConditions = $this->getMarkReadTypeConditions($type);
                if ($typeConditions) {
                    if (is_array($typeConditions)) {
                        $query->whereIn('type', $typeConditions);
                    } else {
                        $query->where('type', $typeConditions);
                    }
                }
                // 如果type是'all'或无效类型，不添加额外条件，标记所有未读
            }

            // 更新为已读
            $affectedRows = $query->update([
                'is_read' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 重新计算用户未读消息数
            $unreadCount = Db::name('notification')
                ->where('user_id', $userId)
                ->where('is_read', 0)
                ->count();

            // 更新用户表的未读数
            Db::name('user')
                ->where('id', $userId)
                ->update(['unread' => $unreadCount]);

            // WebSocket推送未读数更新
            $this->pushUnreadCountUpdate($userId, $unreadCount);

            Db::commit();

            return json([
                'code' => 200,
                'msg' => '标记成功',
                'data' => [
                    'unread_count' => $unreadCount,
                    'affected_rows' => $affectedRows
                ]
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return json([
                'code' => 500,
                'msg' => '标记失败：' . $e->getMessage()
            ]);
        }
    }



    /**
     * 发送通知给管理员
     */
    public function sendToAdmins($type, $title, $message, $relatedId = null, $icon = null)
    {
        try {
            // 获取所有管理员用户
            $admins = Db::table('user')
                ->where('status', '管理员')
                ->column('id');

            if (empty($admins)) {
                return false;
            }

            // 为每个管理员创建通知（使用现有notification表结构）
            $notifications = [];
            foreach ($admins as $adminId) {
                $notifications[] = [
                    'user_id' => $adminId,
                    'from_user_id' => 0, // 系统通知
                    'type' => $type,
                    'target_type' => $type,
                    'target_id' => $relatedId ?: 0,
                    'message_id' => 0,
                    'content' => $title,
                    'target_content' => $message,
                    'content_image' => $icon,
                    'is_read' => 0,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }

            // 批量插入通知
            Db::name('notification')->insertAll($notifications);

            return true;

        } catch (\Exception $e) {
            error_log('发送通知失败：' . $e->getMessage());
            return false;
        }
    }





    /**
     * 根据target_type获取对应内容的图片
     */
    private function getTargetImage($type, $target_id, $message_images)
    {
        try {
            switch ($type) {
                case 'message':
                    // 帖子的图片直接从message_images获取
                    if (!empty($message_images)) {
                        $images = json_decode($message_images, true);
                        if (is_array($images) && !empty($images)) {
                            return $images[0]; // 取第一张图片
                        }
                    }
                    break;

                case 'comment':
                    // 评论的图片需要从comment表获取
                    $comment = Db::name('comment')
                        ->where('id', $target_id)
                        ->field('images')
                        ->find();
                    if ($comment && !empty($comment['images'])) {
                        $images = json_decode($comment['images'], true);
                        if (is_array($images) && !empty($images)) {
                            return $images[0];
                        }
                    }
                    break;

                case 'reply':
                    // 回复的图片需要从post表获取
                    $reply = Db::name('post')
                        ->where('id', $target_id)
                        ->field('images')
                        ->find();
                    if ($reply && !empty($reply['images'])) {
                        $images = json_decode($reply['images'], true);
                        if (is_array($images) && !empty($images)) {
                            return $images[0];
                        }
                    }
                    break;

                default:
                    // 其他类型暂时返回null
                    break;
            }
        } catch (\Exception $e) {
            // 出错时返回null
        }

        return null;
    }

    /**
     * 统一的通知数据获取方法
     * @param int $userId 用户ID
     * @param string $type 消息类型：like, comment, reply, notifications
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @param bool $showAll 是否显示所有（包括已读）
     * @return \think\Response
     */
    private function getNotificationData($userId, $type, $page, $pageSize)
    {
        try {
            // 根据类型确定查询条件
            $typeConditions = $this->getTypeConditions($type);
            if (!$typeConditions) {
                return json(['code' => 400, 'msg' => '无效的消息类型']);
            }

            // 系统通知使用简化查询
            if ($type === 'notifications') {
                return $this->getSystemNotifications($userId, $page, $pageSize);
            }

            // 构建查询
            $query = Db::name('notification')
                ->alias('n')
                ->join('user u', 'n.from_user_id = u.id')
                ->leftJoin('message m', 'n.target_type = "message" AND n.target_id = m.id')
                ->leftJoin('comment c', 'n.target_type = "comment" AND n.target_id = c.id')
                ->leftJoin('message m2', 'n.target_type = "comment" AND c.message_id = m2.id')
                ->leftJoin('post p', 'n.target_type = "reply" AND n.target_id = p.id')
                ->leftJoin('message m3', 'n.target_type = "reply" AND p.message_id = m3.id')
                ->leftJoin('major_comments mc', '(n.target_type = "major_comment" OR n.target_type = "major_reply") AND n.target_id = mc.id')
                ->leftJoin('buaa_majors bm', '(n.target_type = "major_comment" OR n.target_type = "major_reply") AND n.message_id = bm.id')
                ->where('n.user_id', $userId)
                ->where('n.from_user_id', '<>', $userId);

            // 应用类型条件
            if (is_array($typeConditions)) {
                $query->whereIn('n.type', $typeConditions);
            } else {
                $query->where('n.type', $typeConditions);
            }

            // 获取总数（所有消息）
            $total = $query->count();

            // 获取未读消息总数
            $unreadTotal = Db::name('notification')
                ->alias('n')
                ->join('user u', 'n.from_user_id = u.id')
                ->where('n.user_id', $userId)
                ->where('n.from_user_id', '<>', $userId)
                ->where('n.is_read', 0);

            // 应用类型条件到未读查询
            if (is_array($typeConditions)) {
                $unreadTotal->whereIn('n.type', $typeConditions);
            } else {
                $unreadTotal->where('n.type', $typeConditions);
            }
            $unreadCount = $unreadTotal->count();



            // 获取列表数据
            $list = $query->field([
                'n.id',
                'n.user_id',
                'n.from_user_id',
                'u.username as from_username',
                'u.face_url as from_user_avatar',
                'n.type',
                'n.target_type',
                'n.target_id',
                'n.message_id',
                'CASE
                    WHEN n.target_type = "reply" THEN p.parent_comment_id
                    WHEN n.target_type = "major_reply" THEN mc.parent_id
                    ELSE NULL
                END as comment_id',
                'n.content',
                'n.target_content',
                'n.content_image',
                'n.created_at',
                'n.is_read',
                'CASE
                    WHEN n.target_type = "message" THEN m.is_anonymous
                    WHEN n.target_type = "comment" THEN m2.is_anonymous
                    WHEN n.target_type = "reply" THEN m3.is_anonymous
                    WHEN n.target_type = "major_comment" OR n.target_type = "major_reply" THEN 0
                    ELSE 0
                END as message_is_anonymous',
                'CASE
                    WHEN n.target_type = "message" THEN m.images
                    WHEN n.target_type = "comment" THEN c.images
                    WHEN n.target_type = "reply" THEN p.images
                    ELSE NULL
                END as target_images'
            ])
            ->order('n.created_at', 'desc')
            ->page($page, $pageSize)
            ->select()
            ->each(function($item) {
                return $this->formatNotificationItem($item);
            })
            ->toArray();

            // 计算是否还有更多
            $hasMore = ($page * $pageSize) < $total;



            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $list,
                'total' => $total,
                'unread_count' => $unreadCount,
                'has_more' => $hasMore
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取类型查询条件
     */
    private function getTypeConditions($type)
    {
        switch ($type) {
            case 'like':
            case 'likes':
                return 'like';
            case 'comment':
                return 'comment';
            case 'reply':
                return 'reply';
            case 'replies':
                return ['comment', 'reply'];
            case 'notifications':
                return ['system', 'student_auth', 'auth_result'];
            default:
                return false;
        }
    }

    /**
     * 获取标记已读的类型查询条件
     */
    private function getMarkReadTypeConditions($type)
    {
        switch ($type) {
            case 'like':
            case 'likes':
                return 'like';
            case 'comment':
                return 'comment';
            case 'reply':
                return 'reply';
            case 'replies':
                return ['comment', 'reply'];
            case 'notifications':
                return ['system', 'student_auth', 'auth_result'];
            case 'all':
                return null; // 不添加类型限制，标记所有
            default:
                return null;
        }
    }

    /**
     * 推送未读数更新到WebSocket
     */
    private function pushUnreadCountUpdate($userId, $unreadCount)
    {
        try {
            $wsServer = app()->make('websocket.server');
            if ($wsServer && isset($wsServer->userConnections[$userId])) {
                $connection = $wsServer->userConnections[$userId];
                $wsServer->sendMessage($connection, [
                    'type' => 'unread_count',
                    'count' => $unreadCount
                ]);
            }
        } catch (\Exception $e) {
            error_log("WebSocket通知发送失败: " . $e->getMessage());
        }
    }

    /**
     * 格式化通知项目
     */
    private function formatNotificationItem($item)
    {
        // 格式化时间
        $item['created_at'] = $this->formatTime($item['created_at']);

        // 确保所有必要字段都存在
        $item['comment_id'] = $item['comment_id'] ?? null;
        $item['target_content'] = $item['target_content'] ?? '';
        $item['content_image'] = $item['content_image'] ?? '';

        // 处理图片
        if (!empty($item['target_images'])) {
            $images = json_decode($item['target_images'], true);
            if (is_array($images) && !empty($images)) {
                $item['content_image'] = $images[0];
            }
        }
        unset($item['target_images']);

        // 处理匿名通知显示
        if (isset($item['message_is_anonymous']) && $item['message_is_anonymous'] == 1) {
            $anonymousUtil = new \app\utils\AnonymousUtil();
            $anonymousName = $anonymousUtil::generateAnonymousName($item['from_user_id'], $item['message_id']);
            $anonymousAvatar = $anonymousUtil::generateAnonymousAvatar($item['from_user_id'], $item['message_id']);

            // 替换用户信息为匿名信息
            $item['from_username'] = $anonymousName;
            $item['from_user_avatar'] = $anonymousAvatar;
            $item['is_anonymous_notification'] = true;

            // 清理敏感信息
            unset($item['from_user_id']);
        } else {
            $item['is_anonymous_notification'] = false;
            $item['from_user_avatar'] = $item['from_user_avatar'] ?: '/images/weixiao.png';
        }

        // 统一转换is_read为布尔值
        $item['is_read'] = $item['is_read'] == 1;

        // 清理不需要返回给前端的字段
        unset($item['message_is_anonymous']);

        return $item;
    }

    /**
     * 获取系统通知
     */
    private function getSystemNotifications($userId, $page, $pageSize)
    {
        try {
            $query = Db::name('notification')
                ->where('user_id', $userId)
                ->whereIn('type', ['system', 'student_auth', 'auth_result']);

            // 获取总数（所有消息）
            $total = $query->count();

            // 获取未读消息总数
            $unreadCount = Db::name('notification')
                ->where('user_id', $userId)
                ->whereIn('type', ['system', 'student_auth', 'auth_result'])
                ->where('is_read', 0)
                ->count();

            // 获取列表数据
            $list = $query->field('id, content, target_content, content_image, is_read, created_at, target_type, target_id, type')
                ->order('created_at', 'desc')
                ->page($page, $pageSize)
                ->select()
                ->each(function($item) {
                    // 格式化时间
                    $item['created_at'] = $this->formatTime($item['created_at']);

                    // 格式化为统一格式
                    return [
                        'id' => $item['id'],
                        'from_user_id' => 0, // 系统通知
                        'from_username' => '系统通知',
                        'from_user_avatar' => $item['content_image'] ?: '/images/xiaoxi.png',
                        'target_type' => $item['type'],
                        'target_id' => $item['target_id'],
                        'target_content' => $item['target_content'] ?: '',
                        'message_id' => 0,
                        'content_image' => $item['content_image'] ?: '/images/xiaoxi.png',
                        'created_at' => $item['created_at'],
                        'is_read' => $item['is_read'] == 1,
                        'content' => $item['content'],
                        'type' => $item['type'],
                        'is_anonymous_notification' => false
                    ];
                })
                ->toArray();

            // 计算是否还有更多
            $hasMore = ($page * $pageSize) < $total;

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $list,
                'total' => $total,
                'unread_count' => $unreadCount,
                'has_more' => $hasMore
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取通知数据失败: ' . $e->getMessage()
            ]);
        }
    }


}