<?php
declare (strict_types = 1);

namespace app\controller;

use think\Request;
use think\facade\Db;
use app\BaseController;
use app\util\JwtUtil;

class Like extends BaseController
{
    /**
     * 统一点赞/取消点赞接口
     * 支持帖子、评论、回复等所有类型的点赞
     */
    public function unifiedLike(Request $request)
    {
        $type = $request->param('type'); // message/comment/reply/liaoran_comment/liaoran_reply
        $target_id = $request->param('target_id');
        $message_id = $request->param('message_id', null); // 可选，用于通知
        $comment_id = $request->param('comment_id', null); // 可选，回复点赞时使用

        // 确保空字符串转换为null
        $message_id = empty($message_id) ? null : $message_id;
        $comment_id = empty($comment_id) ? null : $comment_id;

        // 验证必需参数
        if (empty($type) || empty($target_id)) {
            return json([
                'error_code' => 1,
                'msg' => '参数错误'
            ]);
        }

        // 验证点赞类型
        $allowedTypes = ['message', 'comment', 'reply', 'liaoran_comment', 'liaoran_reply'];
        if (!in_array($type, $allowedTypes)) {
            return json([
                'error_code' => 1,
                'msg' => '不支持的点赞类型'
            ]);
        }

        // 获取用户ID
        $token = $request->header('token');
        if (!$token) {
            return json(['error_code' => 401, 'msg' => '请先登录']);
        }

        $userData = JwtUtil::validateToken($token);
        if (!$userData) {
            return json(['error_code' => 401, 'msg' => 'token无效或已过期']);
        }
        $user_id = $userData['user_id'];

        // 检查用户状态
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return json(['error_code' => 404, 'msg' => '用户不存在']);
        }
        if ($user['status'] === '禁言') {
            return json(['error_code' => 403, 'msg' => '您已被禁言，无法进行点赞操作']);
        }

        try {
            // 检查是否已经点赞
            $existingLike = Db::name('unified_likes')
                ->where('user_id', $user_id)
                ->where('target_type', $type)
                ->where('target_id', $target_id)
                ->find();

            Db::startTrans();

            $is_liked = false;
            if (!$existingLike) {
                // 准备插入数据，只包含非空字段
                $insertData = [
                    'user_id' => $user_id,
                    'target_type' => $type,
                    'target_id' => $target_id,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                // 只有当message_id不为空时才添加
                if (!empty($message_id)) {
                    $insertData['message_id'] = $message_id;
                }

                // 只有当comment_id不为空时才添加
                if (!empty($comment_id)) {
                    $insertData['comment_id'] = $comment_id;
                }

                // 添加点赞记录
                Db::name('unified_likes')->insert($insertData);
                $is_liked = true;

                // 发送点赞通知
                $this->sendLikeNotification($user_id, $type, $target_id, $message_id, $comment_id);
            } else {
                // 删除点赞记录
                Db::name('unified_likes')
                    ->where('id', $existingLike['id'])
                    ->delete();
                $is_liked = false;
            }

            // 计算最新点赞数
            $total_likes = Db::name('unified_likes')
                ->where('target_type', $type)
                ->where('target_id', $target_id)
                ->count();

            // 更新对应表的点赞数
            $this->updateLikeCount($type, $target_id, $total_likes);

            Db::commit();

            return json([
                'error_code' => 0,
                'msg' => 'success',
                'data' => [
                    'is_liked' => $is_liked,
                    'total_likes' => $total_likes
                ]
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return json([
                'error_code' => 1,
                'msg' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送点赞通知
     */
    private function sendLikeNotification($user_id, $type, $target_id, $message_id, $comment_id)
    {
        try {
            // 获取点赞者信息
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user) return;

            // 根据类型获取目标内容和接收者
            $targetInfo = $this->getTargetInfo($type, $target_id);
            if (!$targetInfo || $targetInfo['user_id'] == $user_id) {
                // 不给自己发通知
                return;
            }

            // 构建通知内容
            $typeMap = [
                'message' => '点赞了你的帖子',
                'comment' => '点赞了你的评论',
                'reply' => '点赞了你的回复',
                'liaoran_comment' => '点赞了你的了然几分评论',
                'liaoran_reply' => '点赞了你的了然几分回复'
            ];

            $content = $user['username'] . $typeMap[$type];

            // 插入通知记录
            Db::name('notification')->insert([
                'user_id' => $targetInfo['user_id'],
                'from_user_id' => $user_id,
                'type' => 'like',
                'target_type' => $type,
                'target_id' => $target_id,
                'message_id' => $message_id ?: $targetInfo['message_id'],
                'content' => $content,
                'target_content' => mb_substr($targetInfo['content'], 0, 50),
                'content_image' => '',
                'created_at' => date('Y-m-d H:i:s'),
                'is_read' => 0
            ]);

            // 更新用户未读消息数
            Db::name('user')->where('id', $targetInfo['user_id'])->inc('unread', 1)->update();

        } catch (\Exception $e) {
            // 通知发送失败不影响点赞功能
            error_log("点赞通知发送失败: " . $e->getMessage());
        }
    }

    /**
     * 获取目标信息
     */
    private function getTargetInfo($type, $target_id)
    {
        switch ($type) {
            case 'message':
                return Db::name('message')
                    ->where('id', $target_id)
                    ->field('user_id, content, id as message_id')
                    ->find();

            case 'comment':
                return Db::name('comment')
                    ->where('id', $target_id)
                    ->field('user_id, content, message_id')
                    ->find();

            case 'reply':
                // 回复数据存储在post表中
                return Db::name('post')
                    ->where('id', $target_id)
                    ->field('user_id, content, message_id')
                    ->find();

            case 'liaoran_comment':
            case 'liaoran_reply':
                return Db::name('liaoran_comments')
                    ->where('id', $target_id)
                    ->field('user_id, content, object_id as message_id')
                    ->find();

            default:
                return null;
        }
    }

    /**
     * 更新对应表的点赞数
     */
    private function updateLikeCount($type, $target_id, $total_likes)
    {
        try {
            switch ($type) {
                case 'message':
                    Db::name('message')
                        ->where('id', $target_id)
                        ->update(['total_likes' => $total_likes]);
                    break;

                case 'comment':
                    Db::name('comment')
                        ->where('id', $target_id)
                        ->update(['total_likes' => $total_likes]);
                    break;

                case 'reply':
                    // 回复数据存储在post表中
                    Db::name('post')
                        ->where('id', $target_id)
                        ->update(['total_likes' => $total_likes]);
                    break;

                case 'liaoran_comment':
                case 'liaoran_reply':
                    Db::name('liaoran_comments')
                        ->where('id', $target_id)
                        ->update(['like_count' => $total_likes]);
                    break;
            }
        } catch (\Exception $e) {
            // 更新失败记录日志但不影响主流程
            error_log("更新点赞数失败: " . $e->getMessage());
        }
    }






    /**
     * 获取用户点赞的内容列表
     */
    public function getLikedList(Request $request)
    {
        $user_id = $request->param('user_id');
        $page = (int)$request->param('page', 1);
        $page_size = (int)$request->param('page_size', 20);
        $show_all = (bool)$request->param('show_all', false);

        // 获取用户ID - 优先从token获取，其次从参数获取
        $actualUserId = null;
        $token = $request->header('token');

        if ($token) {
            // 如果有token，使用token验证
            $userData = JwtUtil::validateToken($token);
            if (!$userData) {
                return json(['error_code' => 401, 'message' => 'token无效或已过期']);
            }
            $actualUserId = $userData['user_id'];
        } else if ($user_id) {
            // 如果没有token但有user_id参数，使用user_id
            $actualUserId = $user_id;
        } else {
            return json(['error_code' => 401, 'message' => '请先登录']);
        }

        // 如果同时有token和user_id，验证是否匹配
        if ($token && $user_id && (int)$user_id !== (int)$actualUserId) {
            return json(['error_code' => 403, 'message' => '用户身份验证失败']);
        }

        // 使用实际的用户ID
        $user_id = $actualUserId;

        try {
            // 使用统一点赞表查询所有点赞记录
            $query = Db::name('unified_likes')
                ->alias('ul')
                ->where('ul.user_id', $user_id);

            // 根据target_type进行不同的JOIN
            $message_likes = Db::name('unified_likes')
                ->alias('ul')
                ->join('message m', 'm.id = ul.target_id')
                ->join('user u', 'm.user_id = u.id')
                ->where('ul.user_id', $user_id)
                ->where('ul.target_type', 'message')
                ->field([
                    'ul.id',
                    'm.user_id as original_user_id',
                    'u.face_url as from_user_avatar',
                    'u.username as from_username',
                    '"message" as target_type',
                    'm.content as target_content',
                    'm.id as target_id',
                    'ul.message_id',
                    'NULL as comment_id',
                    'm.images as content_image',
                    'm.is_anonymous as message_is_anonymous',
                    'ul.created_at',
                    '1 as is_read'
                ])
                ->select()
                ->toArray();

            // 获取评论点赞列表
            $comment_likes = Db::name('unified_likes')
                ->alias('ul')
                ->join('comment c', 'c.id = ul.target_id')
                ->join('user u', 'c.user_id = u.id')
                ->join('message m', 'm.id = ul.message_id')
                ->where('ul.user_id', $user_id)
                ->where('ul.target_type', 'comment')
                ->field([
                    'ul.id',
                    'c.user_id as original_user_id',
                    'u.face_url as from_user_avatar',
                    'u.username as from_username',
                    '"comment" as target_type',
                    'c.content as target_content',
                    'c.id as target_id',
                    'ul.message_id',
                    'ul.comment_id',
                    'NULL as content_image',
                    'm.is_anonymous as message_is_anonymous',
                    'ul.created_at',
                    '1 as is_read'
                ])
                ->select()
                ->toArray();

            // 获取回复点赞列表（回复数据存储在post表中）
            $reply_likes = Db::name('unified_likes')
                ->alias('ul')
                ->join('post r', 'r.id = ul.target_id')
                ->join('user u', 'r.user_id = u.id')
                ->join('message m', 'm.id = ul.message_id')
                ->where('ul.user_id', $user_id)
                ->where('ul.target_type', 'reply')
                ->field([
                    'ul.id',
                    'r.user_id as original_user_id',
                    'u.face_url as from_user_avatar',
                    'u.username as from_username',
                    '"reply" as target_type',
                    'r.content as target_content',
                    'r.id as target_id',
                    'ul.message_id',
                    'ul.comment_id',
                    'NULL as content_image',
                    'm.is_anonymous as message_is_anonymous',
                    'ul.created_at',
                    '1 as is_read'
                ])
                ->select()
                ->toArray();

            // 合并所有点赞记录
            $list = array_merge($message_likes, $comment_likes, $reply_likes);

            // 按时间排序
            usort($list, function($a, $b) {
                return strtotime($b['created_at']) - strtotime($a['created_at']);
            });

            // 处理图片字段和匿名显示
            foreach ($list as &$item) {
                if ($item['content_image']) {
                    $images = json_decode($item['content_image'], true);
                    $item['content_image'] = !empty($images) ? $images[0] : '';
                }

                // 处理匿名显示
                if (isset($item['message_is_anonymous']) && $item['message_is_anonymous'] == 1) {
                    $anonymousUtil = new \app\utils\AnonymousUtil();
                    $anonymousName = $anonymousUtil::generateAnonymousName($item['original_user_id'], $item['message_id']);
                    $anonymousAvatar = $anonymousUtil::generateAnonymousAvatar($item['original_user_id'], $item['message_id']);

                    // 替换用户信息为匿名信息
                    $item['from_username'] = $anonymousName;
                    $item['from_user_avatar'] = $anonymousAvatar;
                    $item['is_anonymous_notification'] = true;
                } else {
                    $item['is_anonymous_notification'] = false;
                }

                // 清理不需要返回给前端的字段
                unset($item['original_user_id']);
                unset($item['message_is_anonymous']);

                // 格式化时间
                $item['created_at'] = date('Y-m-d H:i:s', strtotime($item['created_at']));
            }

            // 分页处理
            $total = count($list);
            $offset = ($page - 1) * $page_size;
            $list = array_slice($list, $offset, $page_size);

            return json([
                'error_code' => 0,
                'message' => 'success',
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'has_more' => ($offset + $page_size) < $total
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'error_code' => 1,
                'message' => $e->getMessage()
            ]);
        }
    }
} 