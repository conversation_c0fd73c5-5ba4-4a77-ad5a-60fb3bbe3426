<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\Request;
use think\response\Json;

class Title extends BaseController
{
    /**
     * 添加头衔
     */
    public function addTitle(Request $request): Json
    {
        $openid = $request->post('openid');
        $duihuanma = $request->post('duihuanma');

        if (empty($openid) || empty($duihuanma)) {
            return json(['status' => 2, 'info' => '缺少参数']);
        }

        // 查询 user 表，id 在 1 到 603 范围内，phone 等于 duihuanma
        $user = Db::name('user')
            ->where('id', '>=', 1)
            ->where('id', '<=', 603)
            ->where('phone', '=', $duihuanma)
            ->find();

        if ($user) {
            // 检查 touxian 表中是否已有相同 openid 和 touxian_id 的记录
            $existingRecord = Db::name('touxian')
                ->where('openid', $openid)
                ->where('touxian_id', 4)
                ->find();

            if ($existingRecord) {
                return json(['status' => 2, 'info' => '已经兑换过该头衔']);
            }

            // 插入新记录
            $data = [
                'openid' => $openid,
                'touxian_id' => 4,
                'touxian_name' => '开服玩家',
            ];

            $result = Db::name('touxian')->insert($data);

            if ($result) {
                return json(['status' => 1, 'info' => '兑换成功']);
            } else {
                return json(['status' => 0, 'info' => '插入数据失败']);
            }
        } else {
            return json(['status' => 2, 'info' => '兑换码无效']);
        }
    }

    /**
     * 获取用户头衔
     */
    public function getTitle(Request $request): Json
    {
        $openid = $request->post('openid');

        if (empty($openid)) {
            return json(['status' => 0, 'message' => 'Missing open_id']);
        }

        // 查询 touxian 表
        $titles = Db::name('touxian')
            ->where('openid', $openid)
            ->field('touxian_id, touxian_name')
            ->select();

        if ($titles) {
            return json(['status' => 1, 'data' => $titles]);
        } else {
            return json(['status' => 0, 'message' => 'No titles found for the given open_id']);
        }
    }

    public function checkAndAddAllDefaultTitles(): Json
    {
        // 设置脚本执行时间，避免超时
        set_time_limit(300); // 5分钟
        
        // 默认头衔列表
        $defaultTitles = [
            ['touxian_id' => 1, 'touxian_name' => '北航小子'],
            ['touxian_id' => 2, 'touxian_name' => '学航小子'],
            ['touxian_id' => 3, 'touxian_name' => '沙航小子'],
            ['touxian_id' => 5, 'touxian_name' => '航小萱'],
            ['touxian_id' => 6, 'touxian_name' => '学小萱'],
            ['touxian_id' => 7, 'touxian_name' => '沙小萱']
        ];

        // 统计变量
        $totalUsers = 0;
        $updatedUsers = 0;
        $titlesAdded = 0;
        
        // 分批次处理参数
        $batchSize = 500; // 每批处理500个用户
        $page = 1;
        
        do {
            // 分页获取用户数据
            $users = Db::name('user')
                ->field('id, openid')
                ->where('openid', '<>', '') // 只处理有openid的用户
                ->page($page, $batchSize)
                ->select();
            
            $count = count($users);
            $totalUsers += $count;
            
            if ($count > 0) {
                // 处理当前批次的用户
                foreach ($users as $user) {
                    // 获取该用户的所有头衔ID
                    $existingTitles = Db::name('touxian')
                        ->where('openid', $user['openid'])
                        ->column('touxian_id');
                    
                    $needsUpdate = false;
                    $titlesToAdd = [];
                    
                    // 检查缺少的头衔
                    foreach ($defaultTitles as $title) {
                        if (!in_array($title['touxian_id'], $existingTitles)) {
                            $needsUpdate = true;
                            $titlesToAdd[] = [
                                'openid' => $user['openid'],
                                'touxian_id' => $title['touxian_id'],
                                'touxian_name' => $title['touxian_name'],
                                'create_time' => time(),
                            ];
                        }
                    }
                    
                    // 如果有缺少的头衔，批量添加
                    if ($needsUpdate && !empty($titlesToAdd)) {
                        Db::name('touxian')->insertAll($titlesToAdd);
                        $titlesAdded += count($titlesToAdd);
                        $updatedUsers++;
                    }
                }
                
                // 继续下一批
                $page++;
            }
        } while ($count > 0); // 当没有更多用户时退出循环

        return json([
            'status' => 'success', 
            'message' => '头衔检查补全完成', 
            'stats' => [
                'total_users' => $totalUsers,
                'updated_users' => $updatedUsers,
                'titles_added' => $titlesAdded
            ]
        ]);
    }
}