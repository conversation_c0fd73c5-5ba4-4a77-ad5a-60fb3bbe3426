<?php
namespace app\controller;

use app\BaseController;
use app\model\Activity as ActivityModel;
use app\model\Organization;
use app\model\OrganizationCategory;
use app\model\User;
use think\facade\Request;
use think\facade\Filesystem;
use think\facade\Db;
use app\util\ImageSecurityUtil;

class Activity extends BaseController
{
    // 需要验证token的方法
    protected $middleware = [
        'app\middleware\CheckToken' => ['only' => ['publish', 'getMyActivities', 'getUserOrganizer']]
    ];
    
    /**
     * 获取活动列表 - 直接使用数据库查询
     */
    public function getList()
    {
        try {
            // 直接使用数据库连接，显式指定mysql配置
            $db = \think\facade\Db::connect('mysql');
            
            // 简化SQL，减少可能的连接问题
            $activities = $db->query("SELECT * FROM activities WHERE status = 1 ORDER BY create_time DESC");
            
            // 添加调试信息
            \think\facade\Log::info('查询活动数据: ' . json_encode([
                'count' => count($activities),
                'sql' => "SELECT * FROM activities WHERE status = 1 ORDER BY create_time DESC"
            ]));
            
            // 如果没有数据，返回空数组并带有调试信息
            if (empty($activities)) {
                $tables = $db->query("SHOW TABLES");
                $tableNames = [];
                foreach ($tables as $table) {
                    $tableNames[] = reset($table); // 获取每行的第一个值（表名）
                }
                
                return json([
                    'code' => 200,
                    'msg' => '暂无活动数据',
                    'data' => [
                        'list' => [],
                        'total' => 0,
                        'debug' => [
                            'tables' => $tableNames,
                            'database' => $db->connect()->getConfig('database'),
                            'query_time' => date('Y-m-d H:i:s')
                        ]
                    ]
                ]);
            }
            
            // 处理数据格式
            $activityList = [];
            foreach ($activities as $activity) {
                // 获取组织信息
                $organization = null;
                if (!empty($activity['organization_id'])) {
                    $orgs = $db->query("SELECT * FROM organizations WHERE id = ?", [$activity['organization_id']]);
                    if (!empty($orgs)) {
                        $organization = $orgs[0];
                    }
                }
                
                // 计算时间差
                $timeAgo = $this->getTimeAgo($activity['create_time']);
                
                // 处理图片路径
                $image = $activity['cover_image'];
                if (!empty($image) && strpos($image, 'http') !== 0) {
                    $image = 'https://www.bjgaoxiaoshequ.store' . $image;
                }
                
                // 处理二维码路径
                $qrcode = $organization && $organization['qrcode'] ? $organization['qrcode'] : '/tupian/公众号/default.jpg';
                if (!empty($qrcode) && strpos($qrcode, 'http') !== 0) {
                    $qrcode = 'https://www.bjgaoxiaoshequ.store' . $qrcode;
                }
                
                // 格式化活动数据
                $activityList[] = [
                    'id' => $activity['id'],
                    'title' => $activity['title'],
                    'description' => $activity['description'],
                    'organizer' => $organization ? $organization['name'] : '未知组织',
                    'hasReward' => $activity['has_reward'] == 1,
                    'isOnline' => $activity['is_online'] == 1,
                    'image' => $image,
                    'article_url' => $activity['article_url'],
                    'timeAgo' => $timeAgo,
                    'create_time' => $activity['create_time'],
                    'end_time' => $activity['end_time'],
                    'qrcode' => $qrcode
                ];
            }
            
            // 返回数据
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'list' => $activityList,
                    'total' => count($activityList),
                    'debug' => [
                        'query_time' => date('Y-m-d H:i:s'),
                        'raw_count' => count($activities)
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('获取活动列表失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            return json([
                'code' => 500,
                'msg' => '获取活动列表失败: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'debug' => [
                    'php_version' => PHP_VERSION,
                    'time' => date('Y-m-d H:i:s')
                ]
            ]);
        }
    }
    
    /**
     * 获取活动详情 - 直接使用数据库查询
     */
    public function getDetail()
    {
        $id = Request::param('id');
        
        if (!$id) {
            return json([
                'code' => 400,
                'msg' => '缺少活动ID'
            ]);
        }
        
        try {
            // 直接使用数据库连接，显式指定mysql配置
            $db = \think\facade\Db::connect('mysql');
            
            // 使用简单查询获取活动数据
            $activityResult = $db->query("SELECT * FROM activities WHERE id = ? AND status = 1 LIMIT 1", [$id]);
            
            // 记录日志
            \think\facade\Log::info('查询活动详情: ' . json_encode([
                'id' => $id,
                'found' => !empty($activityResult),
                'sql' => "SELECT * FROM activities WHERE id = {$id} AND status = 1 LIMIT 1"
            ]));
            
            // 如果没有找到活动
            if (empty($activityResult)) {
                return json([
                    'code' => 404,
                    'msg' => '活动不存在'
                ]);
            }
            
            $activity = $activityResult[0];
            
            // 获取组织信息
            $organization = null;
            if (!empty($activity['organization_id'])) {
                $orgResult = $db->query("SELECT * FROM organizations WHERE id = ? LIMIT 1", [$activity['organization_id']]);
                if (!empty($orgResult)) {
                    $organization = $orgResult[0];
                }
            }
            
            // 获取用户信息
            $user = null;
            if (!empty($activity['user_id'])) {
                $userResult = $db->query("SELECT * FROM user WHERE id = ? LIMIT 1", [$activity['user_id']]);
                if (!empty($userResult)) {
                    $user = $userResult[0];
                }
            }
            
            // 计算时间差
            $timeAgo = $this->getTimeAgo($activity['create_time']);
            
            // 处理图片路径
            $image = $activity['cover_image'];
            if (!empty($image) && strpos($image, 'http') !== 0) {
                $image = 'https://www.bjgaoxiaoshequ.store' . $image;
            }
            
            // 处理组织logo
            $orgLogo = $organization && isset($organization['logo']) ? $organization['logo'] : '';
            if (!empty($orgLogo) && strpos($orgLogo, 'http') !== 0) {
                $orgLogo = 'https://www.bjgaoxiaoshequ.store' . $orgLogo;
            }
            
            // 处理二维码路径
            $qrcode = $organization && isset($organization['qrcode']) ? $organization['qrcode'] : '/tupian/公众号/default.jpg';
            if (!empty($qrcode) && strpos($qrcode, 'http') !== 0) {
                $qrcode = 'https://www.bjgaoxiaoshequ.store' . $qrcode;
            }
            
            // 格式化活动数据
            $formattedActivity = [
                'id' => $activity['id'],
                'title' => $activity['title'],
                'description' => $activity['description'],
                'organizer' => $organization ? $organization['name'] : '未知组织',
                'hasReward' => $activity['has_reward'] == 1,
                'isOnline' => $activity['is_online'] == 1,
                'image' => $image,
                'article_url' => $activity['article_url'],
                'timeAgo' => $timeAgo,
                'create_time' => $activity['create_time'],
                'qrcode' => $qrcode,
                'publisher' => [
                    'id' => $user ? $user['id'] : 0,
                    'nickname' => $user ? $user['nickname'] : '未知用户'
                ],
                'organization' => [
                    'id' => $organization ? $organization['id'] : 0,
                    'name' => $organization ? $organization['name'] : '未知组织',
                    'description' => $organization ? $organization['description'] : '',
                    'logo' => $orgLogo
                ]
            ];
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $formattedActivity,
                'debug' => [
                    'query_time' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('获取活动详情失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            return json([
                'code' => 500,
                'msg' => '获取活动详情失败: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'debug' => [
                    'php_version' => PHP_VERSION,
                    'time' => date('Y-m-d H:i:s')
                ]
            ]);
        }
    }
    
    /**
     * 获取组织列表
     */
    public function getOrganizations()
    {
        try {
            // 获取并验证token，支持多种传递方式
            $token = Request::header('Authorization', '');
            if (empty($token)) {
                $token = Request::header('token', '');
            }
            
            // 如果没有在header中找到token，则尝试从GET或POST参数中获取
            if (empty($token)) {
                $token = Request::param('token', '');
                $token = Request::param('access_token', $token);
            }
            
            // 记录token信息用于调试
            \think\facade\Log::info('getOrganizations - token信息: ' . ($token ? substr($token, 0, 10) . '...' : 'null'));
            
            // 如果需要验证用户，可以在这里添加验证逻辑
            
            $categoryId = Request::param('category_id', 0, 'intval');
            $keyword = Request::param('keyword', '', 'trim');
            $page = Request::param('page', 1, 'intval');
            $pageSize = Request::param('page_size', 20, 'intval');
            
            // 获取所有启用的分类
            $categories = OrganizationCategory::where('status', OrganizationCategory::STATUS_ENABLE)
                ->order('sort', 'asc')
                ->select()
                ->toArray();
                
            // 构建组织查询条件
            $query = Organization::where('status', Organization::STATUS_ENABLE);
            
            // 按分类筛选
            if ($categoryId > 0) {
                $query->where('category_id', $categoryId);
            }
            
            // 按关键词搜索
            if (!empty($keyword)) {
                $query->where('name|description', 'like', "%{$keyword}%");
            }
            
            // 是否需要分页
            $isPaginate = Request::param('is_paginate', 0, 'intval');
            
            // 返回数据结构
            $result = [
                'categories' => $categories,
            ];
            
            // 根据请求参数决定返回数据结构
            $mode = Request::param('mode', 'default');
            
            if ($mode === 'by_category') {
                // 按分类分组返回
                $result['data'] = [];
                
                foreach ($categories as $category) {
                    $orgs = Organization::where('category_id', $category['id'])
                        ->where('status', Organization::STATUS_ENABLE)
                        ->order('id', 'desc')
                        ->select()
                        ->toArray();
                        
                    // 格式化组织数据
                    foreach ($orgs as &$org) {
                        $this->formatOrganization($org);
                    }
                    
                    $result['data'][] = [
                        'category' => $category,
                        'organizations' => $orgs
                    ];
                }
            } else {
                // 常规列表返回
                if ($isPaginate) {
                    // 分页查询
                    $organizations = $query->order('id', 'desc')
                        ->paginate([
                            'list_rows' => $pageSize,
                            'page' => $page
                        ]);
                    
                    $orgList = $organizations->toArray();
                    
                    // 格式化组织数据
                    foreach ($orgList['data'] as &$org) {
                        $this->formatOrganization($org);
                    }
                    
                    $result['organizations'] = $orgList['data'];
                    $result['total'] = $orgList['total'];
                    $result['current_page'] = $orgList['current_page'];
                    $result['last_page'] = $orgList['last_page'];
                } else {
                    // 不分页查询
                    $organizations = $query->order('id', 'desc')
                        ->select()
                        ->toArray();
                    
                    // 格式化组织数据
                    foreach ($organizations as &$org) {
                        $this->formatOrganization($org);
                    }
                    
                    $result['organizations'] = $organizations;
                }
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取组织列表失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 从请求中获取有效用户
     * @return \app\model\User|null
     */
    private function getValidUser()
    {
        // 获取当前用户ID
        $userId = Request::middleware('userId');
        
        // 记录类型信息
        \think\facade\Log::info('尝试获取用户, userId类型: ' . gettype($userId) . ', 值: ' . $userId);
        
        // 尝试不同的方式查询用户
        $methods = [
            'find' => function($id) { return \app\model\User::find($id); },
            'stringWhere' => function($id) { return \app\model\User::where('id', (string)$id)->find(); },
            'intWhere' => function($id) { return \app\model\User::where('id', intval($id))->find(); },
        ];
        
        foreach ($methods as $methodName => $method) {
            $user = $method($userId);
            \think\facade\Log::info("使用{$methodName}方法查询用户结果: " . ($user ? '成功' : '失败'));
            
            if ($user) {
                return $user;
            }
        }
        
        // 如果还是找不到，尝试直接从token获取用户ID
        $token = Request::header('token', '');
        if (empty($token)) {
            $token = Request::header('Authorization', '');
        }
        
        if (!empty($token) && is_string($token)) {
            // 处理带有Bearer前缀的token
            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }
            
            $userData = \app\util\JwtUtil::validateToken($token);
            if (!empty($userData) && isset($userData['user_id'])) {
                $tokenUserId = $userData['user_id'];
                \think\facade\Log::info('Token中的userId类型: ' . gettype($tokenUserId) . ', 值: ' . $tokenUserId);
                
                foreach ($methods as $methodName => $method) {
                    $user = $method($tokenUserId);
                    \think\facade\Log::info("使用token中的ID和{$methodName}方法查询结果: " . ($user ? '成功' : '失败'));
                    
                    if ($user) {
                        return $user;
                    }
                }
            }
        }
        
        // 用户不存在
        return null;
    }
    
    /**
     * 发布活动
     */
    public function publish()
    {
        // 获取请求数据
        $title = Request::param('title');
        $description = Request::param('description', '');
        $organization_id = Request::param('organization_id');
        $has_reward = Request::param('has_reward', 0);
        $is_online = Request::param('is_online', 0);
        $article_url = Request::param('article_url', '');
        $coverImage = Request::param('coverImage', '');
        $end_time = Request::param('end_time', '2099-12-31 23:59:59'); // 默认结束时间为2099年
        $save_organizer = Request::param('save_organizer', 0); // 是否保存主办方选择
        
        // 记录请求信息
        \think\facade\Log::info('发布活动请求参数: ' . json_encode(Request::param()));
        
        // 检查必填字段
        if (empty($title)) {
            return json([
                'code' => 400,
                'msg' => '活动标题不能为空'
            ]);
        }
        
        if (empty($organization_id)) {
            return json([
                'code' => 400,
                'msg' => '主办方不能为空'
            ]);
        }
        
        if (empty($coverImage)) {
            return json([
                'code' => 400,
                'msg' => '活动封面不能为空'
            ]);
        }
        
        // 获取当前登录用户信息
        $user_id = Request::header('token') ? \app\service\TokenService::getCurrentUid() : Request::param('user_id');
        
        if (!$user_id) {
            return json([
                'code' => 401,
                'msg' => '请先登录'
            ]);
        }
        
        try {
            // 使用数据库连接
            $db = \think\facade\Db::connect('mysql');
            
            // 构建插入数据
            $data = [
                'title' => $title,
                'description' => $description,
                'organization_id' => $organization_id,
                'has_reward' => $has_reward ? 1 : 0,
                'is_online' => $is_online ? 1 : 0,
                'article_url' => $article_url,
                'cover_image' => $coverImage,
                'user_id' => $user_id,
                'status' => 1,
                'end_time' => $end_time, // 添加结束时间
                'create_time' => date('Y-m-d H:i:s')
            ];
            
            // 执行插入操作
            $db->table('activities')->insert($data);
            
            // 记录日志
            \think\facade\Log::info('活动发布成功: ' . json_encode($data));
            
            // 如果用户选择保存主办方，保存到用户偏好表
            if ($save_organizer) {
                // 先删除该用户的旧设置
                $db->query("DELETE FROM user_organizer_preference WHERE user_id = ?", [$user_id]);
                
                // 插入新的主办方偏好
                $db->table('user_organizer_preference')->insert([
                    'user_id' => $user_id,
                    'organization_id' => $organization_id,
                    'create_time' => date('Y-m-d H:i:s')
                ]);
                
                \think\facade\Log::info('保存用户主办方选择: user_id=' . $user_id . ', organization_id=' . $organization_id);
            }
            
            return json([
                'code' => 200,
                'msg' => '发布成功',
                'data' => [
                    'id' => $db->getLastInsID($db->table('activities'))
                ]
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('发布活动失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            return json([
                'code' => 500,
                'msg' => '发布失败: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 上传活动封面图片
     */
    public function uploadImage()
    {
        // 检查是否有文件上传
        $file = Request::file('coverImage');
        
        if (!$file) {
            return json([
                'code' => 400,
                'msg' => '请选择要上传的图片'
            ]);
        }
        
        try {
            // 严格的文件安全验证
            $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
            if (!$securityCheck['success']) {
                return json(['code' => 400, 'msg' => $securityCheck['message']]);
            }
            
            // 创建保存目录
            $uploadPath = 'tupian/activity/' . date('Y-m-d') . '/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }
            
            // 根据文件内容生成安全的文件名
            $safeExtension = ImageSecurityUtil::getSafeExtension($securityCheck['mime_type']);
            $saveName = ImageSecurityUtil::generateSafeFileName('activity', $safeExtension);
            
            // 移动文件
            $info = $file->move($uploadPath, $saveName);
            if (!$info) {
                return json(['code' => 400, 'msg' => '文件上传失败']);
            }
            
            // 生成完整URL
            $url = '/tupian/activity/' . date('Y-m-d') . '/' . $saveName;
            $fullUrl = 'https://www.bjgaoxiaoshequ.store' . $url;
            
            return json([
                'code' => 0, // 前端期望的成功码
                'msg' => '上传成功',
                'data' => [
                    'imageUrl' => $fullUrl
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '上传失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取我发布的活动
     */
    public function getMyActivities()
    {
        try {
            $userId = Request::middleware('userId');
            
            $activities = ActivityModel::where('user_id', $userId)
                ->with(['organization'])
                ->order('create_time', 'desc')
                ->select();
                
            $result = [];
            foreach ($activities as $activity) {
                $result[] = $this->formatActivity($activity);
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '获取失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 保存活动订阅状态
     */
    public function saveSubscriptionStatus()
    {
        // 从请求参数获取用户ID
        $userId = Request::param('user_id', 0);
        
        // 如果请求参数中没有用户ID，则尝试从中间件获取
        if (!$userId) {
            $userId = Request::middleware('userId', 0);
        }
        
        // 如果仍然没有有效的用户ID，返回错误
        if (!$userId) {
            return json([
                'code' => 400,
                'msg' => '用户ID不能为空'
            ]);
        }
        
        $subscribed = Request::param('subscribed', '1');
        
        try {
            // 更新用户表中的订阅状态
            $result = User::where('id', $userId)->update([
                'activity_subscribed' => $subscribed == '1' ? 1 : 0
            ]);
            
            return json([
                'code' => 200,
                'msg' => '保存成功',
                'data' => [
                    'user_id' => $userId,
                    'subscribed' => $subscribed == '1' ? 1 : 0,
                    'updated' => $result
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '保存失败: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 保存最后查看活动时间
     */
    public function saveLastViewTime()
    {
        $userId = Request::param('user_id', 0); // 从请求参数获取用户ID
        
        if (!$userId) {
            return json([
                'code' => 400,
                'msg' => '用户ID不能为空'
            ]);
        }
        
        try {
            // 记录请求信息
            \think\facade\Log::info('saveLastViewTime - 请求参数', [
                'user_id' => $userId,
                'request_time' => date('Y-m-d H:i:s')
            ]);
            
            // 获取当前时间
            $now = date('Y-m-d H:i:s');
            
            // 先查询当前用户信息
            $userBefore = \app\model\User::where('id', $userId)->find();
            
            if (!$userBefore) {
                return json([
                    'code' => 404,
                    'msg' => '用户不存在'
                ]);
            }
            
            // 记录更新前的信息
            \think\facade\Log::info('saveLastViewTime - 更新前用户信息', [
                'user_id' => $userBefore->id,
                'last_view_time' => $userBefore->last_view_activity_time
            ]);
            
            // 使用模型更新
            $updateResult = \app\model\User::where('id', $userId)
                ->update(['last_view_activity_time' => $now]);
            
            // 更新后再次查询确认
            $userAfter = \app\model\User::where('id', $userId)->find();
            
            // 记录更新结果
            \think\facade\Log::info('saveLastViewTime - 更新结果', [
                'update_result' => $updateResult,
                'before_time' => $userBefore->last_view_activity_time,
                'after_time' => $userAfter->last_view_activity_time,
                'expected_time' => $now
            ]);
            
            return json([
                'code' => 200,
                'msg' => '保存成功',
                'data' => [
                    'user_id' => $userId,
                    'last_view_time' => $userAfter->last_view_activity_time,
                    'update_time' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('保存最后查看时间失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            return json([
                'code' => 500,
                'msg' => '保存失败: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取新活动数量
     */
    public function getNewActivityCount()
    {
        $userId = Request::post('user_id', 0); // 从POST参数获取用户ID
        
        if (!$userId) {
            return json([
                'code' => 400,
                'msg' => '用户ID不能为空',
                'data' => ['count' => 0]
            ]);
        }
        
        try {
            // 获取用户对象
            $user = \app\model\User::where('id', $userId)->find();
            
            if (!$user) {
                \think\facade\Log::error('getNewActivityCount - 用户不存在', ['user_id' => $userId]);
                
                return json([
                    'code' => 404,
                    'msg' => '用户不存在',
                    'data' => ['count' => 0]
                ]);
            }
            
            // 检查是否订阅了活动通知
            $subscribed = Request::post('subscribed', '1');
            
            // 如果用户未订阅活动通知，返回0
            if ($subscribed === '0') {
                return json([
                    'code' => 200,
                    'msg' => '获取成功（未订阅）',
                    'data' => [
                        'count' => 0,
                        'reason' => 'not_subscribed'
                    ]
                ]);
            }
            
            // 获取用户最后查看活动时间
            $lastViewTime = $user->last_view_activity_time;
            
            // 如果用户没有最后查看时间，则将所有活动都作为新活动
            if (empty($lastViewTime)) {
                $count = \app\model\Activity::where('status', 1)->count();
                $count = min($count, 99); // 限制最大显示数量
                
                return json([
                    'code' => 200,
                    'msg' => '获取成功',
                    'data' => [
                        'count' => $count,
                        'last_view_time' => '',
                        'reason' => 'no_last_view_time'
                    ]
                ]);
            }
            
            // 获取最近几条活动记录（用于调试）
            $recentActivities = \app\model\Activity::where('status', 1)
                ->field('id, title, create_time')
                ->order('create_time', 'desc')
                ->limit(5)
                ->select()
                ->toArray();
            
            // 计算新活动数量
            $count = \app\model\Activity::where('status', 1)
                ->where('create_time', '>', $lastViewTime)
                ->count();
                
            // 安全地记录查询信息
            \think\facade\Log::info('getNewActivityCount - 查询结果', [
                'count' => $count,
                'last_view_time' => $lastViewTime,
                'last_view_time_type' => gettype($lastViewTime)
            ]);
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => [
                    'count' => $count,
                    'last_view_time' => $lastViewTime,
                    'debug' => [
                        'server_time' => date('Y-m-d H:i:s'),
                        'user' => [
                            'id' => $userId,
                            'last_view_time_type' => gettype($lastViewTime),
                            'last_view_time' => $lastViewTime
                        ],
                        'recent_activities' => !empty($recentActivities) ? [
                            'count' => count($recentActivities),
                            'newest' => isset($recentActivities[0]) ? $recentActivities[0]['create_time'] : null
                        ] : []
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('获取活动数量失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            return json([
                'code' => 500,
                'msg' => '获取活动数量失败: ' . $e->getMessage(),
                'data' => [
                    'count' => 0,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            ]);
        }
    }
    
    /**
     * 格式化活动数据
     * @param ActivityModel|array $activity 活动模型或数组
     * @param bool $detail 是否详情
     * @return array
     */
    private function formatActivity($activity, $detail = false)
    {
        // 如果是对象，转为数组处理
        $isObject = is_object($activity);
        
        $data = [
            'id' => $isObject ? $activity->id : $activity['id'],
            'title' => $isObject ? $activity->title : $activity['title'],
            'description' => $isObject ? $activity->description : $activity['description'],
            'organizer' => $isObject ? $activity->organization->name : $activity['organization']['name'],
            'hasReward' => $isObject ? ($activity->has_reward == 1) : ($activity['has_reward'] == 1),
            'isOnline' => $isObject ? ($activity->is_online == 1) : ($activity['is_online'] == 1),
            'image' => $isObject ? $activity->cover_image : $activity['cover_image'],
            'article_url' => $isObject ? $activity->article_url : $activity['article_url'],
            'timeAgo' => $isObject ? $this->getTimeAgo($activity->create_time) : $this->getTimeAgo($activity['create_time']),
            'create_time' => $isObject ? $activity->create_time : $activity['create_time'],
            'qrcode' => $isObject 
                ? ($activity->organization->qrcode ?: 'https://www.bjgaoxiaoshequ.store/tupian/公众号/default.jpg') 
                : ($activity['organization']['qrcode'] ?: 'https://www.bjgaoxiaoshequ.store/tupian/公众号/default.jpg')
        ];
        
        // 详情页额外信息
        if ($detail) {
            $data['publisher'] = [
                'id' => $isObject ? $activity->user->id : $activity['user']['id'],
                'nickname' => $isObject ? $activity->user->nickname : $activity['user']['nickname']
            ];
            
            $data['organization'] = [
                'id' => $isObject ? $activity->organization->id : $activity['organization']['id'],
                'name' => $isObject ? $activity->organization->name : $activity['organization']['name'],
                'description' => $isObject ? $activity->organization->description : $activity['organization']['description'],
                'logo' => $isObject ? $activity->organization->logo : $activity['organization']['logo']
            ];
        }
        
        return $data;
    }
    
    /**
     * 获取时间ago
     * @param string $time 时间
     * @return string
     */
    private function getTimeAgo($time)
    {
        $time = strtotime($time);
        $now = time();
        $diff = $now - $time;
        
        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } elseif ($diff < 604800) {
            return floor($diff / 86400) . '天前';
        } else {
            return date('m-d', $time);
        }
    }
    
    /**
     * 保存Base64图片
     * @param string $base64Data
     * @param string $dir 目录名
     * @return string|false 保存路径或false
     */
    private function saveBase64Image($base64Data, $dir = 'activity')
    {
        try {
            // 获取图片类型和数据
            if (preg_match('/^data:image\/(.*?);base64,(.*)$/', $base64Data, $matches)) {
                $type = $matches[1];
                $data = base64_decode($matches[2]);
                
                // 生成随机文件名
                $filename = date('YmdHis') . rand(10000, 99999) . '.' . $type;
                
                // 保存目录
                $uploadDir = './uploads/' . $dir . '/';
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                
                // 文件完整路径
                $filePath = $uploadDir . $filename;
                
                // 保存文件
                if (file_put_contents($filePath, $data)) {
                    return '/' . $dir . '/' . $filename;
                }
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('保存Base64图片失败: ' . $e->getMessage());
        }
        
        return false;
    }

    /**
     * 格式化组织信息
     * @param array &$organization 组织数据
     * @return void
     */
    private function formatOrganization(&$organization)
    {
        // 处理logo路径
        if (!empty($organization['logo']) && is_string($organization['logo']) && strpos($organization['logo'], 'http') !== 0) {
            $organization['logo'] = request()->domain() . $organization['logo'];
        }
        
        // 处理二维码路径
        if (!empty($organization['qrcode'])) {
            if (is_string($organization['qrcode']) && strpos($organization['qrcode'], 'http') !== 0) {
                $organization['qrcode_url'] = 'https://www.bjgaoxiaoshequ.store/tupian/公众号/' . $organization['qrcode'];
            } else {
                $organization['qrcode_url'] = $organization['qrcode'];
            }
        } else {
            $organization['qrcode_url'] = '';
        }
        
        // 获取所属分类名称
        if (isset($organization['category_id']) && $organization['category_id'] > 0) {
            $category = OrganizationCategory::where('id', $organization['category_id'])->find();
            $organization['category_name'] = $category ? $category['name'] : '';
        } else {
            $organization['category_name'] = '';
        }
    }

    /**
     * 临时调试接口 - 检查token和用户
     */
    public function debugToken()
    {
        try {
            // 从请求头中获取token
            $token = Request::header('Authorization', '');
            if (empty($token)) {
                $token = Request::header('token', '');
            }
            
            // 如果没有在header中找到token，则尝试从GET或POST参数中获取
            if (empty($token)) {
                $token = Request::param('token', '');
                $token = Request::param('access_token', $token);
            }
            
            $result = [
                'has_token' => !empty($token),
            ];
            
            // 如果有token，尝试验证
            if (!empty($token) && is_string($token)) {
                // 处理带有Bearer前缀的token
                if (strpos($token, 'Bearer ') === 0) {
                    $token = substr($token, 7);
                }
                
                // 验证token
                $userData = \app\util\JwtUtil::validateToken($token);
                $result['token_valid'] = !empty($userData);
                $result['token_data'] = $userData;
                
                // 如果token有效，尝试查找用户
                if (!empty($userData) && isset($userData['user_id'])) {
                    $userId = $userData['user_id'];
                    $result['user_id'] = $userId;
                    
                    $user = \app\model\User::find($userId);
                    $result['user_exists'] = !empty($user);
                    
                    if (!empty($user)) {
                        $result['user_info'] = [
                            'id' => $user->id,
                            'username' => $user->username,
                            'status' => $user->status
                        ];
                    }
                }
            }
            
            return json([
                'code' => 200,
                'msg' => 'Token调试信息',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '调试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取用户上次选择的主办方
     */
    public function getUserOrganizer()
    {
        // 获取当前登录用户ID，支持多种token传递方式
        $user_id = null;
        
        // 尝试从header或参数获取token
        $token = Request::header('Authorization', '');
        if (empty($token)) {
            $token = Request::header('token', '');
        }
        
        // 如果没有在header中找到token，则尝试从GET或POST参数中获取
        if (empty($token)) {
            $token = Request::param('token', '');
            $token = Request::param('access_token', $token);
        }
        
        // 记录token信息用于调试
        \think\facade\Log::info('getUserOrganizer - token信息: ' . ($token ? substr($token, 0, 10) . '...' : 'null'));
        
        // 处理带有Bearer前缀的token
        if (is_string($token) && strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }
        
        // 如果有token，尝试验证并获取用户ID
        if (!empty($token)) {
            try {
                $userData = \app\util\JwtUtil::validateToken($token);
                if ($userData && isset($userData['user_id'])) {
                    $user_id = intval($userData['user_id']);
                }
            } catch (\Exception $e) {
                \think\facade\Log::error('getUserOrganizer - 验证token异常: ' . $e->getMessage());
            }
        }
        
        // 如果通过token获取失败，尝试从参数直接获取
        if (!$user_id) {
            $user_id = Request::param('user_id', 0);
        }
        
        \think\facade\Log::info('getUserOrganizer - 最终用户ID: ' . $user_id);
        
        if (!$user_id) {
            return json([
                'code' => 401,
                'msg' => '请先登录'
            ]);
        }
        
        try {
            // 使用数据库连接查询用户的主办方选择
            $db = \think\facade\Db::connect('mysql');
            
            // 查询用户主办方设置
            $result = $db->query(
                "SELECT 
                    o.id, o.name, o.description, o.logo, o.qrcode, 
                    c.id as category_id, c.name as category_name 
                FROM user_organizer_preference uop
                LEFT JOIN organizations o ON uop.organization_id = o.id
                LEFT JOIN organization_categories c ON o.category_id = c.id
                WHERE uop.user_id = ? AND o.status = 1
                LIMIT 1",
                [$user_id]
            );
            
            if (empty($result)) {
                return json([
                    'code' => 200,
                    'msg' => '暂无主办方选择',
                    'data' => null
                ]);
            }
            
            // 处理组织数据，保持与前端组织选择器一致的格式
            $organizer = $result[0];
            
            // 如果有logo，添加完整路径
            if (!empty($organizer['logo']) && strpos($organizer['logo'], 'http') !== 0) {
                $organizer['logo'] = 'https://www.bjgaoxiaoshequ.store' . $organizer['logo'];
            }
            
            // 如果有二维码，添加完整路径
            if (!empty($organizer['qrcode']) && strpos($organizer['qrcode'], 'http') !== 0) {
                $organizer['qrcode'] = 'https://www.bjgaoxiaoshequ.store' . $organizer['qrcode'];
            }
            
            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $organizer
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('获取用户主办方选择失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            return json([
                'code' => 500,
                'msg' => '获取用户主办方选择失败: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
} 