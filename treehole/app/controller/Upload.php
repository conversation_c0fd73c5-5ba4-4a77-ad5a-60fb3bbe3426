<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\Request;
use app\util\ImageSecurityUtil;
use app\util\CosUtil;

class Upload extends BaseController
{
    /**
     * 获取基础URL
     */
    private function getBaseUrl()
    {
        // 根据环境变量或配置决定使用哪个域名
        $isDev = env('APP_DEBUG', false);

        if ($isDev) {
            // 开发环境
            return 'http://localhost:80';
        } else {
            // 生产环境
            return 'https://www.bjgaoxiaoshequ.store';
        }
    }

    /**
     * 上传文件到COS
     */
    public function uploadToCos(Request $request)
    {
        // 获取token
        $token = $request->header('token');
        if (!$token) {
            return json(['code' => 401, 'msg' => '请先登录']);
        }

        // 验证JWT token
        try {
            $payload = \app\util\JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }
        } catch (\Exception $e) {
            return json(['code' => 401, 'msg' => 'token验证失败']);
        }

        // 获取上传的文件
        $file = $request->file('file');
        if (!$file) {
            return json(['code' => 400, 'msg' => '请选择要上传的图片']);
        }

        // 获取上传类型
        $type = $request->param('type', 'common');

        try {
            // 使用安全验证
            $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
            if (!$securityCheck['success']) {
                return json(['code' => 400, 'msg' => $securityCheck['message']]);
            }

            // 生成安全文件名
            $safeExtension = ImageSecurityUtil::getSafeExtension($securityCheck['mime_type']);
            $fileName = ImageSecurityUtil::generateSafeFileName($type, $safeExtension);

            // 记录上传开始
            \think\facade\Log::info('开始上传文件到COS', [
                'type' => $type,
                'fileName' => $fileName,
                'fileSize' => $file->getSize(),
                'filePath' => $file->getRealPath()
            ]);

            // 上传到COS
            $result = CosUtil::uploadFile($type, $file->getRealPath(), $fileName);

            // 记录上传结果
            \think\facade\Log::info('COS上传结果', $result);

            if ($result['success']) {
                // 生成完整URL供前端显示
                $fullUrl = CosUtil::generateUrl($result['key'], $result['bucket_type']);

                \think\facade\Log::info('上传成功，返回数据', [
                    'key' => $result['key'],
                    'url' => $fullUrl,
                    'storage_type' => $result['storage_type']
                ]);

                return json([
                    'code' => 200,
                    'msg' => '上传成功',
                    'data' => [
                        'key' => $result['key'],        // 相对路径，用于数据库存储
                        'url' => $fullUrl,              // 完整URL，用于前端显示
                        'storage_type' => $result['storage_type']
                    ]
                ]);
            } else {
                \think\facade\Log::error('COS上传失败', $result);
                return json(['code' => 500, 'msg' => '上传失败：' . $result['message']]);
            }

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '上传失败：' . $e->getMessage()]);
        }
    }
}