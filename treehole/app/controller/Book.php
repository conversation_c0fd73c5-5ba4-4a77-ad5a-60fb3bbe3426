<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;

class Book extends BaseController
{
    private $host = "https://jisuisbn.market.alicloudapi.com";
    private $path = "/isbn/query";
    private $appcode = "eb02e4424d53491785e2e8491b4cf3a0"; // 建议放到配置文件中
    private $bookPicPath = '../public/bookpic/'; // 修改为相对于应用根目录的路径
    private $domain = 'https://www.bjgaoxiaoshequ.store/'; // 域名

    public function getBookInfo()
    {
        // 接收前端传来的ISBN
        $isbn = $this->request->param('isbn');
        if (empty($isbn)) {
            return json(['status' => 0, 'msg' => 'ISBN不能为空']);
        }

        // 调用API获取图书信息
        $bookInfo = $this->queryIsbnApi($isbn);
        if (empty($bookInfo)) {
            return json(['status' => 0, 'msg' => '获取图书信息失败']);
        }

        // 处理API返回的数据
        $result = json_decode($bookInfo, true);
        if ($result['status'] !== 0 || empty($result['result'])) {
            return json(['status' => 0, 'msg' => $result['msg'] ?? '获取图书信息失败']);
        }

        // 构造数据库存储的数据
        $data = [
            'isbn' => $result['result']['isbn'],
            'isbn10' => $result['result']['isbn10'],
            'title' => $result['result']['title'],
            'subtitle' => $result['result']['subtitle'],
            'author' => $result['result']['author'],
            'publisher' => $result['result']['publisher'],
            'pubplace' => $result['result']['pubplace'],
            'pubdate' => $result['result']['pubdate'],
            'price' => isset($result['result']['price']) ? str_replace('元', '', $result['result']['price']) : '',
            'binding' => $result['result']['binding'],
            'page' => $result['result']['page'],
            'summary' => $result['result']['summary'],
            'pic' => $result['result']['pic'],
            'keyword' => $result['result']['keyword'],
            'cip' => $result['result']['cip'],
            'edition' => $result['result']['edition'],
            'impression' => $result['result']['impression'],
            'language' => $result['result']['language'],
            'format' => $result['result']['format'],
            'class' => $result['result']['class']
        ];

        try {
            // 查询是否已存在该图书
            $existBook = Db::name('books')->where('isbn', $isbn)->find();
            
            if ($existBook) {
                // 更新已存在的图书信息
                Db::name('books')->where('isbn', $isbn)->update($data);
                $bookId = $existBook['id'];
                $msg = '图书信息更新成功';
            } else {
                // 插入新的图书信息
                $bookId = Db::name('books')->insertGetId($data);
                $msg = '图书信息保存成功';
            }

            // 处理图片
            if (!empty($data['pic'])) {
                // 下载远程图片
                $picName = $bookId . '_' . $isbn . $this->getImageExtension($data['pic']);
                $localPath = $this->bookPicPath . $picName;
                
                if ($this->downloadImage($data['pic'], $localPath)) {
                    // 更新数据库中的图片路径
                    $newPicUrl = $this->domain . 'bookpic/' . $picName;
                    Db::name('books')->where('id', $bookId)->update(['pic' => $newPicUrl]);
                    $data['pic'] = $newPicUrl;
                }
                return json(['status' => 1, 'msg' => $msg, 'data' => $data]);
            } else {
                // 如果没有图片，提示前端上传
                return json(['status' => 2, 'msg' => '请上传图书封面照片', 'data' => $data, 'book_id' => $bookId]);
            }
        } catch (\Exception $e) {
            return json(['status' => 0, 'msg' => '数据库操作失败：' . $e->getMessage()]);
        }
    }
// 更新图书价格和库存
    public function updatePriceAndStock()
    {
        $bookId = $this->request->param('book_id');
        $salePrice = $this->request->param('sale_price');
        $stock = $this->request->param('stock');

        if (empty($bookId)) {
            return json(['status' => 0, 'msg' => '图书ID不能为空']);
        }

        if (!is_numeric($salePrice) || $salePrice < 0) {
            return json(['status' => 0, 'msg' => '请输入正确的价格']);
        }

        if (!is_numeric($stock) || $stock < 0) {
            return json(['status' => 0, 'msg' => '请输入正确的库存数量']);
        }

        try {
            $data = [
                'sale_price' => round((float)$salePrice, 1),  // 保留一位小数
                'stock' => (int)$stock
            ];

            Db::name('books')->where('id', $bookId)->update($data);
            return json(['status' => 1, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            return json(['status' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }
    // 处理前端上传的图片
    public function uploadBookPic()
    {
        $file = $this->request->file('image');
        $bookId = $this->request->param('book_id');
        $isbn = $this->request->param('isbn');

        if (!$file || !$bookId || !$isbn) {
            return json(['status' => 0, 'msg' => '参数错误']);
        }

        try {
            // 检查目录是否存在，不存在则创建
            if (!is_dir($this->bookPicPath)) {
                mkdir($this->bookPicPath, 0777, true);
            }

            // 生成文件名
            $picName = $bookId . '_' . $isbn . '.' . $file->getOriginalExtension();
            
            // 移动文件
            $file->move($this->bookPicPath, $picName);
            
            // 更新数据库中的图片路径
            $newPicUrl = $this->domain . 'bookpic/' . $picName;
            Db::name('books')->where('id', $bookId)->update(['pic' => $newPicUrl]);

            return json(['status' => 1, 'msg' => '图片上传成功', 'pic_url' => $newPicUrl]);
        } catch (\Exception $e) {
            return json(['status' => 0, 'msg' => '图片上传失败：' . $e->getMessage()]);
        }
    }

    private function queryIsbnApi($isbn)
    {
        $headers = [
            "Authorization:APPCODE " . $this->appcode,
            "Content-Type:application/json; charset=UTF-8"
        ];

        $querys = "isbn=" . $isbn;
        $url = $this->host . $this->path . "?" . $querys;

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($curl);
        curl_close($curl);

        return $response;
    }

    // 下载远程图片
    private function downloadImage($url, $savePath)
    {
        try {
            // 检查目录是否存在，不存在则创建
            $dir = dirname($savePath);
            if (!is_dir($dir)) {
                mkdir($dir, 0777, true);
            }

            // 下载图片
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            $file = curl_exec($ch);
            curl_close($ch);

            if ($file) {
                return file_put_contents($savePath, $file);
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    // 获取图片扩展名
    private function getImageExtension($url)
    {
        $info = pathinfo($url);
        return isset($info['extension']) ? '.' . strtolower($info['extension']) : '.jpg';
    }
    // 根据院系ID查询图书列表
    public function getBooksByDepartment()
    {
        $departmentId = $this->request->param('department_id');
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);

        if (empty($departmentId)) {
            return json(['status' => 0, 'msg' => '院系ID不能为空']);
        }

        try {
            // 查询总数
            $total = Db::name('books')
                ->where('department_id', $departmentId)
                ->count();

            // 查询数据
            $books = Db::name('books')
                ->field('category, department_id, sale_price, stock, pic, title')
                ->where('department_id', $departmentId)
                ->page($page, $limit)
                ->select()
                ->toArray();

            return json([
                'status' => 1,
                'msg' => '查询成功',
                'data' => [
                    'total' => $total,
                    'list' => $books,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);
        } catch (\Exception $e) {
            return json(['status' => 0, 'msg' => '查询失败：' . $e->getMessage()]);
        }
    }
}