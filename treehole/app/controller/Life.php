<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\View;
use think\Request;
use think\response\Json;
use app\util\JwtUtil;
use app\util\ImageSecurityUtil;

class Life extends BaseController
{
    public function getServiceById(Request $request)
    {
        // 获取前端传递的 id 值
        $id = $request->param('id');

        // 检查 id 是否为空
        if (empty($id)) {
            return json([
                'code' => 400,
                'message' => 'id参数不能为空',
                'data' => []
            ], 400);
        }

        try {
            // 查询 life_service 表中对应的记录
            $service = Db::name('life_service')
                ->where('id', $id)
                ->field('id, title, category, name, intro, main_image, detail_image, qq, vx, phone, address, type, create_time, create_user_id, status')
                ->find();

            // 如果没有找到记录，返回错误信息
            if (!$service) {
                return json([
                    'code' => 404,
                    'message' => '未找到对应的服务信息',
                    'data' => []
                ], 404);
            }

            // 格式化创建时间
            if (isset($service['create_time'])) {
                $service['create_time_formatted'] = date('Y-m-d H:i:s', strtotime($service['create_time']));
            }

            // 获取创建者信息
            if ($service['create_user_id']) {
                $creator = Db::name('user')
                    ->where('id', $service['create_user_id'])
                    ->field('id, username, phone')
                    ->find();
                
                $service['creator'] = $creator ?: ['id' => 0, 'username' => '未知用户', 'phone' => ''];
            }

            // 返回查询到的数据
            return json([
                'code' => 200,
                'message' => '成功',
                'data' => $service
            ], 200);

        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('获取服务详情失败: ' . $e->getMessage());
            
            return json([
                'code' => 500,
                'message' => '服务器内部错误',
                'data' => []
            ], 500);
        }
    }

    public function getServicesByCategory(Request $request)
    {
        // 获取 'category' 参数并转换为整数
        $category = $request->param('category', 0, 'intval');

        if ($category < 0) {
            return json([
                'code' => 400,
                'message' => '类别ID必须为大于0的整数',
                'data' => []
            ], 400);
        }

        try {
            // 根据category的值决定查询条件
            if ($category == 6) {
                // 如果category=6，获取所有未审核的服务
                $services = Db::name('life_service')
                    ->where('status', 'unverified')
                    ->field('id, title, category, name, intro, main_image, detail_image, create_time, create_user_id, status, qq, vx, phone, address, type')
                    ->select()
                    ->toArray();

                // 为每个服务添加创建者信息
                foreach ($services as &$service) {
                    $creator = Db::name('user')
                        ->where('id', $service['create_user_id'])
                        ->field('id, username, phone')
                        ->find();
                    
                    $service['creator'] = $creator ?: ['id' => 0, 'username' => '未知用户', 'phone' => ''];
                    
                    // 格式化创建时间
                    if (isset($service['create_time'])) {
                        $service['create_time_formatted'] = date('Y-m-d H:i:s', strtotime($service['create_time']));
                    }
                }
            } else {
                // 其他category值，获取已审核的服务
                $services = Db::name('life_service')
                    ->where('category', $category)
                    ->where('status', 'verified')
                    ->field('id, title, name, intro, main_image, address, qq, vx, phone')
                    ->select()
                    ->toArray();
            }

            return json([
                'code' => 200,
                'message' => '成功',
                'data' => $services
            ], 200);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('查询服务失败: ' . $e->getMessage());

            return json([
                'code' => 500,
                'message' => '服务器内部错误',
                'data' => []
            ], 500);
        }
    }

    public function uploadImage(Request $request): Json
    {
        if ($file = $request->file('file')) {
            try {
                // 严格的文件安全验证
                $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
                if (!$securityCheck['success']) {
                    return json(['error_code' => 1, 'msg' => $securityCheck['message']]);
                }

                // 获取上传类型（主图或详情图）
                $type = $request->param('type', 'main'); // main 或 detail

                // 使用相对路径，指向public/tupian/life目录
                $uploadPath = 'tupian/life/' . ($type === 'main' ? '主图' : '详情图') . '/';

                // 按日期创建子目录
                $currentDate = date('Y-m-d');
                $uploadPath .= $currentDate . '/';

                // 确保目录存在
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }

                // 根据文件内容生成安全的文件名
                $safeExtension = ImageSecurityUtil::getSafeExtension($securityCheck['mime_type']);
                $saveName = ImageSecurityUtil::generateSafeFileName('service', $safeExtension);
                
                // 移动文件
                $info = $file->move($uploadPath, $saveName);
                if (!$info) {
                    return json(['error_code' => 1, 'msg' => '文件上传失败']);
                }

                // 生成相对路径
                $relativePath = '/tupian/life/' . ($type === 'main' ? '主图' : '详情图') . '/' . $currentDate . '/' . $saveName;
                
                // 使用固定的域名
                $baseUrl = 'https://www.bjgaoxiaoshequ.store';
                $fullUrl = $baseUrl . $relativePath;

                return json([
                    'error_code' => 0, 
                    'msg' => '上传成功',
                    'data' => [
                        'image_url' => $fullUrl
                    ]
                ]);
                
            } catch (\Exception $e) {
                return json(['error_code' => 1, 'msg' => '上传失败：' . $e->getMessage()]);
            }
        }
        return json(['error_code' => 1, 'msg' => '没有上传文件']);
    }

    public function addService(Request $request): Json
    {
        // 获取token
        $token = $request->header('token');
        
        // 验证token
        if (empty($token)) {
            return json(['code' => 401, 'message' => '未授权，请先登录']);
        }

        // 验证token有效性
        $tokenData = JwtUtil::validateToken($token);
        if (!$tokenData) {
            return json(['code' => 401, 'message' => 'token无效或已过期']);
        }

        // 获取user_id
        $user_id = $tokenData['user_id'] ?? null;
        if (!$user_id) {
            return json(['code' => 401, 'message' => 'token信息不完整']);
        }

        // 验证用户状态是否为verified或管理员
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return json(['code' => 404, 'message' => '用户不存在']);
        }
        
        if ($user['status'] !== 'verified' && $user['status'] !== '管理员' && $user['status'] !== 'temporary_verified') {
            return json(['code' => 403, 'message' => '请先完成认证后再添加服务']);
        }

        // 获取请求参数
        $category = $request->param('category', 0, 'intval');
        $title = $request->param('title', '');
        $name = $request->param('name', '');
        $intro = $request->param('intro', '');
        $qq = $request->param('qq', '');
        $vx = $request->param('vx', '');
        $phone = $request->param('phone', '');
        $address = $request->param('address', 0, 'intval');
        $type = $request->param('type', 1, 'intval');
        $has_detail_image = $request->param('has_detail_image', 'false');

        // 验证必填字段
        if (empty($title)) {
            return json(['code' => 400, 'message' => '服务标题不能为空']);
        }

        if (empty($name)) {
            return json(['code' => 400, 'message' => '商家名称不能为空']);
        }

        // 验证category
        if ($category < 0) {
            return json(['code' => 400, 'message' => '服务类别必须为非负整数']);
        }

        // 验证address
        if ($address < 0 || $address > 255) {
            return json(['code' => 400, 'message' => '地址编号必须在0-255之间']);
        }

        // 验证联系方式
        if (empty($qq) && empty($vx) && empty($phone)) {
            return json(['code' => 400, 'message' => '至少需要提供一种联系方式']);
        }

        try {
            // 先验证所有必要信息
            if (!$request->file('main_image')) {
                return json(['code' => 400, 'message' => '请上传服务主图']);
            }

            // 开启事务
            Db::startTrans();
            try {
                // 处理主图上传
                $mainImage = '';
                $mainImageFile = $request->file('main_image');
                
                // 严格的文件安全验证
                $securityCheck = ImageSecurityUtil::validateThinkPHPFile($mainImageFile);
                if (!$securityCheck['success']) {
                    return json(['code' => 400, 'message' => $securityCheck['message']]);
                }

                // 上传主图
                $uploadPath = 'tupian/life/主图/' . date('Y-m-d') . '/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }

                $timestamp = date('YmdHis');
                $safeName = preg_replace('/[\\\\\/\:\*\?\"\<\>\|]/', '', $name);
                $saveName = $safeName . '_' . $timestamp . '.' . $mainImageFile->extension();
                
                $info = $mainImageFile->move($uploadPath, $saveName);
                if (!$info) {
                    return json(['code' => 400, 'message' => '主图上传失败']);
                }
                
                $mainImage = 'https://www.bjgaoxiaoshequ.store/tupian/life/主图/' . date('Y-m-d') . '/' . $saveName;

                // 构建数据
                $data = [
                    'category' => $category,
                    'title' => $title,
                    'name' => $name,
                    'intro' => $intro,
                    'qq' => $qq,
                    'vx' => $vx,
                    'phone' => $phone,
                    'address' => $address,
                    'main_image' => $mainImage,
                    'detail_image' => '',  // 详情图稍后通过专门的接口上传
                    'create_time' => date('Y-m-d H:i:s'),
                    'create_user_id' => $user_id,
                    'status' => 'unverified', // 新添加的服务默认为未审核状态
                    'type' => $type
                ];

                // 插入数据
                $serviceId = Db::name('life_service')->insertGetId($data);

                if ($serviceId) {
                    // 提交事务
                    Db::commit();
                    return json([
                        'code' => 200,
                        'message' => '服务添加成功' . ($has_detail_image === 'true' ? '，请继续上传详情图' : '，等待管理员审核'),
                        'data' => [
                            'id' => $serviceId,
                            'main_image' => $mainImage
                        ]
                    ]);
                } else {
                    // 回滚事务
                    Db::rollback();
                    // 删除已上传的图片
                    @unlink('./public' . parse_url($mainImage, PHP_URL_PATH));
                    return json(['code' => 500, 'message' => '服务添加失败']);
                }
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 删除已上传的图片
                if (isset($mainImage) && $mainImage) {
                    @unlink('./public' . parse_url($mainImage, PHP_URL_PATH));
                }
                throw $e;
            }
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器内部错误: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function uploadDetailImage(Request $request): Json
    {
        try {
            // 获取服务ID和文件
            $serviceId = $request->param('service_id');
            $file = $request->file('detail_image');

            if (!$serviceId || !$file) {
                return json(['code' => 400, 'message' => '参数错误']);
            }

            // 验证服务是否存在
            $service = Db::name('life_service')->where('id', $serviceId)->find();
            if (!$service) {
                return json(['code' => 404, 'message' => '服务不存在']);
            }

            // 严格的文件安全验证
            $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
            if (!$securityCheck['success']) {
                return json(['code' => 400, 'message' => $securityCheck['message']]);
            }

            // 上传文件
            $uploadPath = 'tupian/life/详情图/' . date('Y-m-d') . '/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            // 根据文件内容生成安全的文件名
            $safeExtension = ImageSecurityUtil::getSafeExtension($securityCheck['mime_type']);
            $timestamp = date('YmdHis');
            $safeName = preg_replace('/[\\\\\/\:\*\?\"\<\>\|]/', '', $service['name']);
            $saveName = $safeName . '_' . $timestamp . '.' . $safeExtension;
            
            $info = $file->move($uploadPath, $saveName);
            if (!$info) {
                return json(['code' => 400, 'message' => '详情图上传失败']);
            }

            $imageUrl = 'https://www.bjgaoxiaoshequ.store/tupian/life/详情图/' . date('Y-m-d') . '/' . $saveName;

            // 更新数据库
            $result = Db::name('life_service')
                ->where('id', $serviceId)
                ->update(['detail_image' => $imageUrl]);

            if ($result) {
                return json([
                    'code' => 200,
                    'message' => '上传成功',
                    'data' => ['url' => $imageUrl]
                ]);
            } else {
                // 删除已上传的文件
                @unlink(str_replace('https://www.bjgaoxiaoshequ.store', 'C:/daima/treehole/public', parse_url($imageUrl, PHP_URL_PATH)));
                return json(['code' => 500, 'message' => '更新数据库失败']);
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '上传失败：' . $e->getMessage()]);
        }
    }

    public function getPendingServices(Request $request): Json
    {
        try {
            // 查询所有未审核的服务
            $pendingServices = Db::name('life_service')
                ->where('status', 'unverified')
                ->order('create_time', 'desc')
                ->select()
                ->toArray();

            // 为每个服务添加创建者信息
            foreach ($pendingServices as &$service) {
                $creator = Db::name('user')
                    ->where('id', $service['create_user_id'])
                    ->field('id, username, phone')
                    ->find();
                
                $service['creator'] = $creator ?: ['id' => 0, 'username' => '未知用户', 'phone' => ''];
                
                // 格式化创建时间
                if (isset($service['create_time'])) {
                    $service['create_time_formatted'] = date('Y-m-d H:i:s', strtotime($service['create_time']));
                }
            }

            return json([
                'code' => 200,
                'message' => '成功',
                'data' => $pendingServices
            ]);
            
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('获取待审核服务失败: ' . $e->getMessage());
            
            return json([
                'code' => 500,
                'message' => '服务器内部错误',
                'data' => []
            ]);
        }
    }
    public function getServicesByLocation(Request $request): Json
    {
        // 获取 'address' 参数并转换为整数
        $address = $request->param('address', 0, 'intval');

        if ($address < 0 || $address > 255) {
            return json([
                'code' => 400,
                'message' => '地址编号必须在0-255之间',
                'data' => []
            ], 400);
        }

        try {
            // 获取指定地址的已审核服务
            $services = Db::name('life_service')
                ->where('address', $address)
                ->where('status', 'verified')
                ->field('id, title, name, intro, main_image, detail_image, qq, vx, phone, type, create_time')
                ->order('create_time', 'desc')
                ->select()
                ->toArray();

            // 格式化创建时间
            foreach ($services as &$service) {
                if (isset($service['create_time'])) {
                    $service['create_time_formatted'] = date('Y-m-d H:i:s', strtotime($service['create_time']));
                }
            }

            return json([
                'code' => 200,
                'message' => '成功',
                'data' => $services
            ], 200);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('查询服务失败: ' . $e->getMessage());

            return json([
                'code' => 500,
                'message' => '服务器内部错误',
                'data' => []
            ], 500);
        }
    }
} 