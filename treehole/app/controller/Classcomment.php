<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\Db;

class Classcomment extends BaseController
{
    /**
     * 获取课程评论列表
     */
    public function list()
    {
        $params = input();
        if (empty($params['course_id'])) {
            return json(['code' => 1, 'msg' => '课程ID不能为空']);
        }
        if (empty($params['sort_type']) || !in_array($params['sort_type'], ['likes', 'time'])) {
            return json(['code' => 1, 'msg' => '排序类型错误']);
        }
        if (empty($params['page']) || empty($params['page_size'])) {
            return json(['code' => 1, 'msg' => '分页参数不能为空']);
        }

        try {
            $page = max(1, intval($params['page']));
            $pageSize = max(1, min(50, intval($params['page_size'])));

            $query = Db::name('course_comments')
                ->where('course_id', $params['course_id']);

            // 排序
            if ($params['sort_type'] == 'likes') {
                $query->order('likes', 'desc')
                    ->order('created_at', 'desc');
            } else {
                $query->order('created_at', 'desc');
            }

            $total = $query->count();
            $list = $query->field([
                'id',
                'content',
                'likes',
                'created_at'
            ])
            ->page($page, $pageSize)
            ->select();

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'total' => $total,
                    'page_size' => $pageSize,
                    'current_page' => $page,
                    'list' => $list
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 2, 'msg' => '获取评论列表失败：' . $e->getMessage()]);
        }
    }

    /**
     * 发表课程评论
     */
    public function add()
    {
        $params = input();
        if (empty($params['course_id'])) {
            return json(['code' => 1, 'msg' => '课程ID不能为空']);
        }
        if (empty($params['content'])) {
            return json(['code' => 1, 'msg' => '评论内容不能为空']);
        }
        if (empty($params['user_id']) || empty($params['openid'])) {
            return json(['code' => 1, 'msg' => '用户信息不完整']);
        }

        try {
            // 验证用户信息
            $userId = $params['user_id'] ?? $params['userId'] ?? '';
            $openId = $params['open_id'] ?? $params['openid'] ?? '';
            
            if (empty($userId) || empty($openId)) {
                return json(['code' => 1, 'msg' => '用户信息不完整']);
            }

            // 验证用户信息
            $user = Db::name('user')
                ->where([
                    'id' => $userId,
                    'openid' => $openId
                ])->find();
            if (!$user) {
                return json(['code' => 1, 'msg' => '用户信息验证失败']);
            }

            // 验证课程是否存在
            $course = Db::name('courses')->where('id', $params['course_id'])->find();
            if (!$course) {
                return json(['code' => 1, 'msg' => '课程不存在']);
            }

            // 添加评论
            $data = [
                'user_id' => $params['user_id'],
                'course_id' => $params['course_id'],
                'content' => $params['content'],
                'likes' => 0,
                'created_at' => date('Y-m-d H:i:s')
            ];
            $commentId = Db::name('course_comments')->insertGetId($data);

            return json([
                'code' => 0,
                'msg' => '评论成功',
                'data' => [
                    'id' => $commentId,
                    'content' => $params['content'],
                    'created_at' => $data['created_at']
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 2, 'msg' => '发表评论失败：' . $e->getMessage()]);
        }
    }

    /**
     * 评论点赞
     */
    public function like()
    {
        $params = input();
        if (empty($params['comment_id'])) {
            return json(['code' => 1, 'msg' => '评论ID不能为空']);
        }

        // 兼容两种参数名
        $userId = $params['user_id'] ?? $params['userId'] ?? '';
        $openId = $params['open_id'] ?? $params['openid'] ?? '';
        
        if (empty($userId) || empty($openId)) {
            return json(['code' => 1, 'msg' => '用户信息不完整']);
        }

        try {
            // 验证用户信息
            $user = Db::name('user')
                ->where([
                    'id' => $userId,
                    'openid' => $openId
                ])->find();
            if (!$user) {
                return json(['code' => 1, 'msg' => '用户信息验证失败']);
            }

            // 验证评论是否存在
            $comment = Db::name('course_comments')->where('id', $params['comment_id'])->find();
            if (!$comment) {
                return json(['code' => 1, 'msg' => '评论不存在']);
            }

            // 检查是否已经点赞
            $exists = Db::name('comment_likes')
                ->where([
                    'user_id' => $params['user_id'],
                    'comment_id' => $params['comment_id']
                ])->find();

            if ($exists) {
                return json(['code' => 1, 'msg' => '已经点赞过该评论']);
            }

            // 开启事务
            Db::startTrans();
            try {
                // 添加点赞记录
                Db::name('comment_likes')->insert([
                    'user_id' => $params['user_id'],
                    'comment_id' => $params['comment_id'],
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                // 更新评论点赞数
                Db::name('course_comments')
                    ->where('id', $params['comment_id'])
                    ->inc('likes')
                    ->update();

                Db::commit();

                // 获取最新点赞数
                $likes = Db::name('course_comments')
                    ->where('id', $params['comment_id'])
                    ->value('likes');

                return json([
                    'code' => 0,
                    'msg' => '点赞成功',
                    'data' => [
                        'likes' => $likes
                    ]
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return json(['code' => 2, 'msg' => '点赞失败：' . $e->getMessage()]);
        }
    }
} 