<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;

class Book extends BaseController
{
    private $host = "https://jisuisbn.market.alicloudapi.com";
    private $path = "/isbn/query";
    private $appcode = "eb02e4424d53491785e2e8491b4cf3a0"; // 建议放到配置文件中

    public function getBookInfo()
    {
        // 接收前端传来的ISBN
        $isbn = $this->request->param('isbn');
        if (empty($isbn)) {
            return json(['status' => 0, 'msg' => 'ISBN不能为空']);
        }

        // 调用API获取图书信息
        $bookInfo = $this->queryIsbnApi($isbn);
        if (empty($bookInfo)) {
            return json(['status' => 0, 'msg' => '获取图书信息失败']);
        }

        // 处理API返回的数据
        $result = json_decode($bookInfo, true);
        if ($result['status'] !== 0 || empty($result['result'])) {
            return json(['status' => 0, 'msg' => $result['msg'] ?? '获取图书信息失败']);
        }

        // 构造数据库存储的数据
        $data = [
            'isbn' => $result['result']['isbn'],
            'isbn10' => $result['result']['isbn10'],
            'title' => $result['result']['title'],
            'subtitle' => $result['result']['subtitle'],
            'author' => $result['result']['author'],
            'publisher' => $result['result']['publisher'],
            'pubplace' => $result['result']['pubplace'],
            'pubdate' => $result['result']['pubdate'],
            'price' => str_replace('元', '', $result['result']['price']),
            'binding' => $result['result']['binding'],
            'page' => $result['result']['page'],
            'summary' => $result['result']['summary'],
            'pic' => $result['result']['pic'],
            'keyword' => $result['result']['keyword'],
            'cip' => $result['result']['cip'],
            'edition' => $result['result']['edition'],
            'impression' => $result['result']['impression'],
            'language' => $result['result']['language'],
            'format' => $result['result']['format'],
            'class' => $result['result']['class']
        ];

        try {
            // 查询是否已存在该图书
            $existBook = Db::name('books')->where('isbn', $isbn)->find();
            
            if ($existBook) {
                // 更新已存在的图书信息
                Db::name('books')->where('isbn', $isbn)->update($data);
                $msg = '图书信息更新成功';
            } else {
                // 插入新的图书信息
                Db::name('books')->insert($data);
                $msg = '图书信息保存成功';
            }

            return json(['status' => 1, 'msg' => $msg, 'data' => $data]);
        } catch (\Exception $e) {
            return json(['status' => 0, 'msg' => '数据库操作失败：' . $e->getMessage()]);
        }
    }

    private function queryIsbnApi($isbn)
    {
        $headers = [
            "Authorization:APPCODE " . $this->appcode,
            "Content-Type:application/json; charset=UTF-8"
        ];

        $querys = "isbn=" . $isbn;
        $url = $this->host . $this->path . "?" . $querys;

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($curl);
        curl_close($curl);

        return $response;
    }
}