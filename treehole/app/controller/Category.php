<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\facade\Db;

class Category extends BaseController
{
    /**
     * 获取课程分类列表
     */
    public function list()
    {
        try {
            // 获取所有分类
            $categories = Db::name('categories')
                ->where('parent_id', 0)
                ->field(['id', 'name'])
                ->select()
                ->toArray();

            // 获取子分类
            foreach ($categories as &$category) {
                $children = Db::name('categories')
                    ->where('parent_id', $category['id'])
                    ->field(['id', 'name'])
                    ->select()
                    ->toArray();
                $category['children'] = $children;
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => $categories
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 2,
                'msg' => '获取分类列表失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 添加课程分类
     */
    public function add()
    {
        $params = input();
        if (empty($params['name'])) {
            return json(['code' => 1, 'msg' => '分类名称不能为空']);
        }

        try {
            $data = [
                'name' => $params['name'],
                'parent_id' => intval($params['parent_id'] ?? 0),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 如果是子分类，验证父分类是否存在
            if ($data['parent_id'] > 0) {
                $parent = Db::name('categories')->where('id', $data['parent_id'])->find();
                if (!$parent) {
                    return json(['code' => 1, 'msg' => '父分类不存在']);
                }
                // 验证父分类是否为一级分类
                if ($parent['parent_id'] != 0) {
                    return json(['code' => 1, 'msg' => '只支持二级分类']);
                }
            }

            $id = Db::name('categories')->insertGetId($data);

            return json([
                'code' => 0,
                'msg' => '添加成功',
                'data' => [
                    'id' => $id,
                    'name' => $data['name'],
                    'parent_id' => $data['parent_id']
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 2, 'msg' => '添加分类失败：' . $e->getMessage()]);
        }
    }

    /**
     * 修改课程分类
     */
    public function update()
    {
        $params = input();
        if (empty($params['id'])) {
            return json(['code' => 1, 'msg' => '分类ID不能为空']);
        }
        if (empty($params['name'])) {
            return json(['code' => 1, 'msg' => '分类名称不能为空']);
        }

        try {
            $category = Db::name('categories')->where('id', $params['id'])->find();
            if (!$category) {
                return json(['code' => 1, 'msg' => '分类不存在']);
            }

            Db::name('categories')
                ->where('id', $params['id'])
                ->update([
                    'name' => $params['name'],
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            return json([
                'code' => 0,
                'msg' => '修改成功',
                'data' => [
                    'id' => $params['id'],
                    'name' => $params['name']
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 2, 'msg' => '修改分类失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除课程分类
     */
    public function delete()
    {
        $id = input('id');
        if (empty($id)) {
            return json(['code' => 1, 'msg' => '分类ID不能为空']);
        }

        try {
            // 检查分类是否存在
            $category = Db::name('categories')->where('id', $id)->find();
            if (!$category) {
                return json(['code' => 1, 'msg' => '分类不存在']);
            }

            // 检查是否有子分类
            $hasChildren = Db::name('categories')->where('parent_id', $id)->count();
            if ($hasChildren > 0) {
                return json(['code' => 1, 'msg' => '请先删除子分类']);
            }

            // 检查是否有关联的课程
            $hasCourses = Db::name('courses')->where('category_id', $id)->count();
            if ($hasCourses > 0) {
                return json(['code' => 1, 'msg' => '该分类下有关联的课程，无法删除']);
            }

            Db::name('categories')->where('id', $id)->delete();

            return json([
                'code' => 0,
                'msg' => '删除成功'
            ]);

        } catch (\Exception $e) {
            return json(['code' => 2, 'msg' => '删除分类失败：' . $e->getMessage()]);
        }
    }
} 