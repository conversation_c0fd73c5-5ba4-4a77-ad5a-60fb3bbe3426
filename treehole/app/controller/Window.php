<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\Request;
use think\facade\Db;
use app\util\JwtUtil;

class Window extends BaseController
{
    /**
     * 获取窗口列表（用于抽取今日美食）
     */
    public function getList()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::verifyToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $canteenIds = request()->post('canteen_ids', []); // 食堂ID数组
            $minRating = request()->post('min_rating', 0); // 最低评分

            // 构建查询条件
            $query = Db::name('windows')
                ->alias('w')
                ->leftJoin('canteens c', 'w.canteen_id = c.id')
                ->where('w.status', 1)
                ->where('c.status', 1);

            // 如果指定了食堂ID，则筛选
            if (!empty($canteenIds)) {
                $query->whereIn('w.canteen_id', $canteenIds);
            }

            // 如果指定了最低评分，则筛选
            if ($minRating > 0) {
                $query->where('w.avg_rating', '>=', $minRating);
            }

            $windows = $query->field('w.id, w.name, w.image_url, w.avg_rating, w.canteen_id, w.floor, c.name as canteen_name')
                ->order('w.avg_rating desc, w.total_ratings desc')
                ->select();

            $result = [];
            foreach ($windows as $window) {
                $result[] = [
                    'id' => $window['id'],
                    'name' => $window['name'],
                    'avatar' => $window['image_url'] ?: '/images/shitang.png',
                    'rating' => (float)$window['avg_rating'],
                    'canteen_id' => $window['canteen_id'],
                    'canteen_name' => $window['canteen_name'],
                    'floor' => $window['floor']
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取窗口详情
     */
    public function getDetail()
    {
        try {
            $windowId = request()->post('id');
            if (!$windowId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 获取当前用户ID（如果已登录）
            $currentUserId = null;
            $token = request()->header('token');
            if ($token) {
                $userData = JwtUtil::validateToken($token);
                if ($userData) {
                    $currentUserId = $userData['user_id'];
                }
            }

            // 获取窗口信息，包含创建者信息
            $window = Db::name('windows')
                ->alias('w')
                ->leftJoin('user u', 'w.created_by = u.id')
                ->where('w.id', $windowId)
                ->where('w.status', 1)
                ->field('w.*, u.username as creator_name')
                ->find();

            if (!$window) {
                return json(['code' => 404, 'msg' => '窗口不存在']);
            }

            // 获取窗口评论列表（不包含评分）
            $comments = Db::name('window_comments')
                ->alias('wc')
                ->leftJoin('user u', 'wc.user_id = u.id')
                ->where('wc.window_id', $windowId)
                ->where('wc.status', 1)
                ->whereNull('wc.parent_id') // 只获取顶级评论
                ->field('wc.id, wc.window_id, wc.user_id, wc.content, wc.images, wc.like_count, wc.created_at, u.username, u.face_url') // 明确指定字段，添加images
                ->order('wc.like_count desc, wc.created_at desc') // 按点赞数和时间排序
                ->select();

            $commentList = [];
            foreach ($comments as $comment) {
                // 检查当前用户是否点赞了这条评论
                $isLiked = false;
                if ($currentUserId) {
                    $likeRecord = Db::name('window_comment_likes')
                        ->where('comment_id', $comment['id'])
                        ->where('user_id', $currentUserId)
                        ->find();
                    $isLiked = !empty($likeRecord);
                }

                // 获取回复
                $replies = Db::name('window_comments')
                    ->alias('wc')
                    ->leftJoin('user u', 'wc.user_id = u.id')
                    ->leftJoin('user ru', 'wc.reply_to_user_id = ru.id')
                    ->where('wc.parent_id', $comment['id'])
                    ->where('wc.status', 1)
                    ->field('wc.id, wc.user_id, wc.content, wc.images, wc.like_count, wc.reply_to_user_id, wc.created_at, u.username, u.face_url, ru.username as reply_to_username') // 明确指定字段，添加images
                    ->order('wc.created_at asc')
                    ->select();

                $replyList = [];
                foreach ($replies as $reply) {
                    // 检查当前用户是否点赞了这条回复
                    $replyIsLiked = false;
                    if ($currentUserId) {
                        $replyLikeRecord = Db::name('window_comment_likes')
                            ->where('comment_id', $reply['id'])
                            ->where('user_id', $currentUserId)
                            ->find();
                        $replyIsLiked = !empty($replyLikeRecord);
                    }

                    // 处理回复内容格式
                    // 只有对回复的回复才显示"回复xxx"，对评论的直接回复不显示
                    $replyContent = $reply['content'];
                    $replyToUsername = '';
                    $showReplyTo = false;

                    // 只有当reply_to_user_id存在且不为空时，才显示"回复xxx"
                    if ($reply['reply_to_user_id'] && $reply['reply_to_username']) {
                        $replyToUsername = $reply['reply_to_username'];
                        $showReplyTo = true;
                    }

                    // 处理回复图片
                    $replyImages = [];
                    if ($reply['images']) {
                        $imageUrls = json_decode($reply['images'], true);
                        if (is_array($imageUrls)) {
                            $replyImages = $imageUrls;
                        }
                    }

                    $replyList[] = [
                        'id' => $reply['id'],
                        'user' => [
                            'id' => $reply['user_id'], // 添加用户ID
                            'name' => $reply['username'] ?: '匿名用户',
                            'avatar' => $reply['face_url'] ?: '/images/default_avatar.png'
                        ],
                        'content' => $reply['content'], // 直接使用原始内容
                        'images' => $replyImages, // 添加图片数组
                        'reply_to_username' => $showReplyTo ? $replyToUsername : '', // 只有对回复的回复才显示
                        'time' => $reply['created_at'],
                        'likes' => intval($reply['like_count'] ?: 0),
                        'is_liked' => $replyIsLiked
                    ];
                }

                // 处理评论图片
                $commentImages = [];
                if ($comment['images']) {
                    $imageUrls = json_decode($comment['images'], true);
                    if (is_array($imageUrls)) {
                        $commentImages = $imageUrls;
                    }
                }

                $commentList[] = [
                    'id' => $comment['id'],
                    'user' => [
                        'id' => $comment['user_id'], // 添加用户ID
                        'name' => $comment['username'] ?: '匿名用户',
                        'avatar' => $comment['face_url'] ?: '/images/default_avatar.png'
                    ],
                    'content' => $comment['content'],
                    'images' => $commentImages, // 添加图片数组
                    'time' => $comment['created_at'],
                    'likes' => intval($comment['like_count'] ?: 0), // 确保是整数
                    'is_liked' => $isLiked, // 添加点赞状态
                    'replies' => $replyList
                ];
            }

            // 获取当前用户的评分状态
            $userRating = null;
            if ($currentUserId) {
                $userRatingRecord = Db::name('window_ratings')
                    ->where('window_id', $windowId)
                    ->where('user_id', $currentUserId)
                    ->where('status', 1)
                    ->find();
                if ($userRatingRecord) {
                    $userRating = intval($userRatingRecord['rating']);
                }
            }

            $result = [
                'id' => $window['id'],
                'name' => $window['name'],
                'avatar' => $window['image_url'] ?: '/images/shitang.png',
                'tag' => $window['creator_name'] ?: '匿名用户', // 使用创建者用户名作为tag
                'tagColor' => '#5ec6fa',
                'rating' => (float)$window['avg_rating'],
                'ratingCount' => $window['total_ratings'],
                'description' => $window['description'] ?: '',
                'floor' => $window['floor'],
                'canteen_id' => $window['canteen_id'],
                'userRating' => $userRating, // 用户已评分状态
                'comments' => $commentList
            ];

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 添加窗口
     */
    public function add()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $userData = JwtUtil::validateToken($token);
            if (!$userData) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            // 验证管理员身份
            $userId = $userData['user_id'];
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user || $user['status'] !== '管理员') {
                return json(['code' => 403, 'msg' => '只有管理员可以添加窗口']);
            }

            $name = request()->post('name');
            $floor = request()->post('floor');
            $canteenId = request()->post('canteen_id');
            $avatar = request()->post('avatar');
            $description = request()->post('description', '');
            $creatorName = request()->post('creator_name', ''); // 获取创建者用户名

            if (!$name || !$floor || !$canteenId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 检查食堂是否存在
            $canteen = Db::name('canteens')
                ->where('id', $canteenId)
                ->where('status', 1)
                ->find();

            if (!$canteen) {
                return json(['code' => 404, 'msg' => '食堂不存在']);
            }

            // 检查窗口名称是否已存在（同一食堂同一楼层）
            $existWindow = Db::name('windows')
                ->where('name', $name)
                ->where('floor', $floor)
                ->where('canteen_id', $canteenId)
                ->where('status', 1)
                ->find();

            if ($existWindow) {
                return json(['code' => 400, 'msg' => '该楼层已存在同名窗口']);
            }

            // 添加窗口
            $windowData = [
                'name' => $name,
                'floor' => $floor,
                'canteen_id' => $canteenId,
                'image_url' => $avatar ?: '/images/shitang.png',
                'description' => $description,
                'specialty' => $creatorName ?: '匿名用户', // 将创建者用户名保存到specialty字段
                'avg_rating' => 0,
                'total_ratings' => 0,
                'hot_comment' => '',
                'created_by' => $userData['user_id'], // 添加创建者用户ID
                'status' => 1,
                'sort_order' => 0
            ];

            $windowId = Db::name('windows')->insertGetId($windowData);

            return json([
                'code' => 200,
                'msg' => '添加成功',
                'data' => ['id' => $windowId]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 添加窗口评分（仅评分，不包含评论）
     */
    public function addRating()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $userData = JwtUtil::validateToken($token);
            if (!$userData) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $userData['user_id'];
            $windowId = request()->post('window_id');
            $rating = request()->post('rating');

            if (!$windowId || ($rating === null || $rating === '')) {
                return json([
                    'code' => 400,
                    'msg' => '参数错误'
                ]);
            }

            // 确保rating是数字
            $rating = intval($rating);

            if ($rating < -10 || $rating > 10) {
                return json([
                    'code' => 400,
                    'msg' => '评分范围为-10到10分'
                ]);
            }

            // 检查是否已经评分过
            $existRating = Db::name('window_ratings')
                ->where('window_id', $windowId)
                ->where('user_id', $userId)
                ->where('status', 1)
                ->find();

            if ($existRating) {
                // 更新评分
                Db::name('window_ratings')
                    ->where('window_id', $windowId)
                    ->where('user_id', $userId)
                    ->update(['rating' => $rating]);
            } else {
                // 添加新评分
                $ratingData = [
                    'window_id' => $windowId,
                    'user_id' => $userId,
                    'rating' => $rating
                ];
                Db::name('window_ratings')->insert($ratingData);
            }

            // 更新窗口平均评分
            $this->updateWindowRating($windowId);

            return json([
                'code' => 200,
                'msg' => '评分成功'
            ]);

        } catch (\Exception $e) {
            // 详细错误信息用于调试
            $errorInfo = [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];

            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage(),
                'debug' => $errorInfo
            ]);
        }
    }

    /**
     * 添加窗口评论
     */
    public function addComment()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $userData = JwtUtil::validateToken($token);
            if (!$userData) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $userData['user_id'];
            $windowId = request()->post('window_id');
            $content = request()->post('content');
            $parentId = request()->post('parent_id', null);
            $replyToUserId = request()->post('reply_to_user_id', null);
            $images = request()->post('images', null); // 获取图片参数

            // 处理参数，确保数据类型正确
            $parentId = $parentId && $parentId !== 'null' && $parentId !== 'undefined' ? intval($parentId) : null;
            $replyToUserId = $replyToUserId && $replyToUserId !== 'null' && $replyToUserId !== 'undefined' ? intval($replyToUserId) : null;

            // 验证参数：内容或图片至少有一个
            if (!$windowId || (!$content && !$images)) {
                return json(['code' => 400, 'msg' => '请输入评论内容或选择图片']);
            }

            // 检查窗口是否存在
            $window = Db::name('windows')
                ->where('id', $windowId)
                ->where('status', 1)
                ->find();

            if (!$window) {
                return json(['code' => 404, 'msg' => '窗口不存在']);
            }

            Db::startTrans();
            try {
                // 添加评论
                $commentData = [
                    'window_id' => $windowId,
                    'user_id' => $userId,
                    'content' => $content ?: '', // 如果没有内容，设为空字符串
                    'parent_id' => $parentId,
                    'reply_to_user_id' => $replyToUserId,
                    'images' => $images // 添加图片字段
                ];

                $commentId = Db::name('window_comments')->insertGetId($commentData);

                // 如果是回复，更新父评论的回复数
                if ($parentId) {
                    Db::name('window_comments')
                        ->where('id', $parentId)
                        ->inc('reply_count', 1);
                }

                // 处理推送通知
                $this->handleCommentNotification($userId, $windowId, $parentId, $replyToUserId, $content, $images);

                Db::commit();

                return json([
                    'code' => 200,
                    'msg' => '评论成功',
                    'data' => ['id' => $commentId]
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            // 详细错误信息用于调试
            $errorInfo = [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];

            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage(),
                'debug' => $errorInfo
            ]);
        }
    }

    /**
     * 点赞评论
     */
    public function likeComment()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $userData = JwtUtil::validateToken($token);
            if (!$userData) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $userData['user_id'];
            $commentId = request()->post('comment_id');

            if (!$commentId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 检查是否已经点赞过
            $existLike = Db::name('window_comment_likes')
                ->where('comment_id', $commentId)
                ->where('user_id', $userId)
                ->find();

            // 开启事务
            Db::startTrans();
            try {
                if ($existLike) {
                    // 取消点赞
                    Db::name('window_comment_likes')
                        ->where('comment_id', $commentId)
                        ->where('user_id', $userId)
                        ->delete();
                } else {
                    // 添加点赞
                    Db::name('window_comment_likes')->insert([
                        'comment_id' => $commentId,
                        'user_id' => $userId,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);

                    // 处理点赞通知（只在点赞时发送通知，取消点赞不发送）
                    $this->handleLikeNotification($userId, $commentId);
                }

                // 重新计算点赞数（基于点赞表的实际数量）
                $actualLikes = Db::name('window_comment_likes')
                    ->where('comment_id', $commentId)
                    ->count();

                // 更新评论表的点赞数
                Db::name('window_comments')
                    ->where('id', $commentId)
                    ->update(['like_count' => $actualLikes]);

                Db::commit();

                return json([
                    'code' => 200,
                    'msg' => $existLike ? '取消点赞成功' : '点赞成功',
                    'data' => [
                        'is_liked' => !$existLike,
                        'total_likes' => $actualLikes
                    ]
                ]);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            // 详细错误信息用于调试
            $errorInfo = [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];

            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage(),
                'debug' => $errorInfo
            ]);
        }
    }

    /**
     * 更新窗口平均评分
     */
    private function updateWindowRating($windowId)
    {
        $ratings = Db::name('window_ratings')
            ->where('window_id', $windowId)
            ->where('status', 1)
            ->select()
            ->toArray(); // 转换为数组

        $totalRatings = count($ratings);
        $avgRating = 0;

        if ($totalRatings > 0) {
            $totalScore = array_sum(array_column($ratings, 'rating'));
            $avgRating = round($totalScore / $totalRatings, 1); // 保留1位小数
            // 确保评分在有效范围内
            $avgRating = max(-10.0, min(10.0, $avgRating));
        }

        // 更新热门评论（从评论表获取）
        $hotComment = '';
        $topComment = Db::name('window_comments')
            ->where('window_id', $windowId)
            ->where('status', 1)
            ->whereNull('parent_id')
            ->order('like_count desc, created_at desc')
            ->find();

        if ($topComment) {
            $hotComment = $topComment['content'];
        }

        Db::name('windows')->where('id', $windowId)->update([
            'avg_rating' => $avgRating,
            'total_ratings' => $totalRatings,
            'hot_comment' => $hotComment
        ]);
    }

    /**
     * 获取窗口推荐菜品列表
     */
    public function getDishRecommendations()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $userData = JwtUtil::validateToken($token);
            if (!$userData) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $windowId = request()->post('window_id');
            if (!$windowId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 获取推荐菜品统计
            $recommendations = Db::name('window_dish_recommendations')
                ->where('window_id', $windowId)
                ->where('status', 1)
                ->field('dish_name, COUNT(*) as count')
                ->group('dish_name')
                ->order('count DESC, dish_name ASC')
                ->select();

            // 获取当前用户的推荐
            $userRecommendations = Db::name('window_dish_recommendations')
                ->where('window_id', $windowId)
                ->where('user_id', $userData['user_id'])
                ->where('status', 1)
                ->column('dish_name');

            $result = [];
            foreach ($recommendations as $item) {
                $result[] = [
                    'dish_name' => $item['dish_name'],
                    'count' => (int)$item['count'],
                    'isRecommended' => in_array($item['dish_name'], $userRecommendations)
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 添加/取消推荐菜品
     */
    public function toggleDishRecommendation()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $userData = JwtUtil::validateToken($token);
            if (!$userData) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $windowId = request()->post('window_id');
            $dishName = request()->post('dish_name');
            $action = request()->post('action'); // 'add' 或 'remove'

            if (!$windowId || !$dishName || !$action) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 验证菜品名称长度
            if (mb_strlen($dishName) > 10) {
                return json(['code' => 400, 'msg' => '菜品名称不能超过10个字符']);
            }

            // 检查窗口是否存在
            $window = Db::name('windows')
                ->where('id', $windowId)
                ->where('status', 1)
                ->find();

            if (!$window) {
                return json(['code' => 404, 'msg' => '窗口不存在']);
            }

            $userId = $userData['user_id'];

            if ($action === 'add') {
                // 检查是否存在该用户对该菜品的记录（不管状态）
                $existRecommendation = Db::name('window_dish_recommendations')
                    ->where('window_id', $windowId)
                    ->where('user_id', $userId)
                    ->where('dish_name', $dishName)
                    ->find();

                if ($existRecommendation) {
                    // 如果记录已存在
                    if ($existRecommendation['status'] == 1) {
                        return json(['code' => 400, 'msg' => '您已经推荐过这道菜了']);
                    } else {
                        // 如果状态为0，重新激活
                        Db::name('window_dish_recommendations')
                            ->where('id', $existRecommendation['id'])
                            ->update(['status' => 1]);
                    }
                } else {
                    // 创建新记录
                    Db::name('window_dish_recommendations')->insert([
                        'window_id' => $windowId,
                        'user_id' => $userId,
                        'dish_name' => $dishName,
                        'status' => 1
                    ]);
                }

                return json([
                    'code' => 200,
                    'msg' => '推荐成功'
                ]);

            } elseif ($action === 'remove') {
                // 取消推荐 - 将状态设为0，不删除记录
                $result = Db::name('window_dish_recommendations')
                    ->where('window_id', $windowId)
                    ->where('user_id', $userId)
                    ->where('dish_name', $dishName)
                    ->where('status', 1)
                    ->update(['status' => 0]);

                if ($result) {
                    return json([
                        'code' => 200,
                        'msg' => '取消推荐成功'
                    ]);
                } else {
                    return json(['code' => 400, 'msg' => '您还没有推荐过这道菜']);
                }
            } else {
                return json(['code' => 400, 'msg' => '操作类型错误']);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 处理评论通知
     */
    private function handleCommentNotification($userId, $windowId, $parentId, $replyToUserId, $content, $images)
    {
        try {
            // 获取评论者信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) return;

            // 获取第一张图片URL（如果有的话）
            $firstImage = '';
            if (!empty($images) && $images !== '[]') {
                $imagesArray = json_decode($images, true);
                $firstImage = is_array($imagesArray) && !empty($imagesArray) && isset($imagesArray[0]) ? $imagesArray[0] : '';
            }

            if ($parentId) {
                // 这是回复评论
                $parentComment = Db::name('window_comments')->where('id', $parentId)->find();
                if ($parentComment && $parentComment['user_id'] != $userId) {
                    // 通知原评论作者
                    Db::name('notification')->insert([
                        'user_id' => $parentComment['user_id'],
                        'from_user_id' => $userId,
                        'type' => 'reply',
                        'target_type' => 'window_comment',
                        'target_id' => $parentId,
                        'message_id' => $windowId,
                        'content' => $user['username'] . '回复了你的食堂评论',
                        'target_content' => mb_substr($content, 0, 50),
                        'content_image' => $firstImage,
                        'created_at' => date('Y-m-d H:i:s'),
                        'is_read' => 0
                    ]);

                    // 更新用户未读消息数
                    Db::name('user')->where('id', $parentComment['user_id'])->inc('unread', 1)->update();
                }

                // 如果是回复特定用户，也要通知被回复的用户
                if ($replyToUserId && $replyToUserId != $userId && $replyToUserId != $parentComment['user_id']) {
                    Db::name('notification')->insert([
                        'user_id' => $replyToUserId,
                        'from_user_id' => $userId,
                        'type' => 'reply',
                        'target_type' => 'window_reply',
                        'target_id' => $parentId,
                        'message_id' => $windowId,
                        'content' => $user['username'] . '回复了你的食堂回复',
                        'target_content' => mb_substr($content, 0, 50),
                        'content_image' => $firstImage,
                        'created_at' => date('Y-m-d H:i:s'),
                        'is_read' => 0
                    ]);

                    // 更新用户未读消息数
                    Db::name('user')->where('id', $replyToUserId)->inc('unread', 1)->update();
                }
            } else {
                // 这是评论窗口，通知窗口创建者
                $window = Db::name('windows')->where('id', $windowId)->find();
                if ($window && $window['created_by'] != $userId) {
                    Db::name('notification')->insert([
                        'user_id' => $window['created_by'],
                        'from_user_id' => $userId,
                        'type' => 'comment',
                        'target_type' => 'window',
                        'target_id' => $windowId,
                        'message_id' => $windowId,
                        'content' => $user['username'] . '评论了你的食堂窗口',
                        'target_content' => mb_substr($content, 0, 50),
                        'content_image' => $firstImage,
                        'created_at' => date('Y-m-d H:i:s'),
                        'is_read' => 0
                    ]);

                    // 更新用户未读消息数
                    Db::name('user')->where('id', $window['created_by'])->inc('unread', 1)->update();
                }
            }
        } catch (\Exception $e) {
            // 通知发送失败不影响评论功能
            error_log("食堂评论通知发送失败: " . $e->getMessage());
        }
    }

    /**
     * 处理点赞通知
     */
    private function handleLikeNotification($userId, $commentId)
    {
        try {
            // 获取点赞者信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) return;

            // 获取评论信息
            $comment = Db::name('window_comments')->where('id', $commentId)->find();
            if (!$comment) return;

            // 只有当点赞者不是评论作者本人时才发送通知
            if ($comment['user_id'] != $userId) {
                $targetType = $comment['parent_id'] ? 'window_reply' : 'window_comment';
                $contentText = $comment['parent_id'] ? '点赞了你的食堂回复' : '点赞了你的食堂评论';

                Db::name('notification')->insert([
                    'user_id' => $comment['user_id'],
                    'from_user_id' => $userId,
                    'type' => 'like',
                    'target_type' => $targetType,
                    'target_id' => $commentId,
                    'message_id' => $comment['window_id'],
                    'content' => $user['username'] . $contentText,
                    'target_content' => $comment['content'],
                    'content_image' => '',
                    'created_at' => date('Y-m-d H:i:s'),
                    'is_read' => 0
                ]);

                // 更新用户未读消息数
                Db::name('user')->where('id', $comment['user_id'])->inc('unread', 1)->update();
            }
        } catch (\Exception $e) {
            // 通知发送失败不影响点赞功能
            error_log("食堂点赞通知发送失败: " . $e->getMessage());
        }
    }

}
