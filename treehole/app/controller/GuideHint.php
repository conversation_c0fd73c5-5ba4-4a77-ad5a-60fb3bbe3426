<?php
declare (strict_types = 1);

namespace app\controller;

use think\facade\Db;
use think\Request;
use think\Response;

class GuideHint
{
    // 定义页面提示的当前版本号
    private $pageVersions = [
        'home_page' => [
            'steps' => [
                'guide' => [        // 新手引导
                    'version' => 2,  // 新手引导保持版本1
                    'order' => 1    // 优先显示
                ],
                'notice' => [       // 更新公告引导
                    'version' => 11,  // 更新公告使用版本2
                    'order' => 2    // 第二个显示
                ]
            ],
            'total_steps' => 2
        ],
        'profile_page' => [
            'steps' => [
                'avatar' => 1,       // 头像设置引导
                'settings' => 1,     // 设置按钮引导
            ],
            'total_steps' => 2
        ],
        'post_page' => [
            'steps' => [
                'image_upload' => 1, // 图片上传引导
                'tag_select' => 1,   // 标签选择引导
            ],
            'total_steps' => 2
        ],
                'message_page' => [
            'steps' => [
                'guide' => [
                    'version' => 1,
                    'order' => 1
                ]
            ],
            'total_steps' => 1
        ],
        'me_page' => [
            'steps' => [
                'guide' => [
                    'version' => 1,
                    'order' => 1
                ]
            ],
            'total_steps' => 1
        ],
        'search_page' => [
            'steps' => [
                'guide' => [
                    'version' => 1,
                    'order' => 1
                ]
            ],
            'total_steps' => 1
        ],
        'settings_page' => [
            'steps' => [
                'guide' => [
                    'version' => 1,
                    'order' => 1
                ]
            ],
            'total_steps' => 1
        ]
        // 可以添加更多页面的版本号
    ];

    /**
     * 获取页面提示状态
     * @param Request $request
     * @return Response
     */
    public function getHintStatus(Request $request): Response
    {
        $user_id = $request->param('user_id');
        $page_key = $request->param('page_key');
        $step_key = $request->param('step_key');
        
        if (empty($user_id) || empty($page_key)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        if (!isset($this->pageVersions[$page_key])) {
            return json(['code' => 0, 'msg' => '无效的页面标识']);
        }

        // 如果没有指定step_key，查找第一个未显示的引导
        if (empty($step_key)) {
            $steps = $this->pageVersions[$page_key]['steps'];
            // 按order排序
            uasort($steps, function($a, $b) {
                return $a['order'] - $b['order'];
            });

            foreach ($steps as $key => $step) {
                $hint_status = Db::name('user_guide_hints')
                    ->where('user_id', $user_id)
                    ->where('page_key', $page_key)
                    ->where('step_key', $key)
                    ->where('version', $step['version'])
                    ->find();
                
                if (empty($hint_status)) {
                    $step_key = $key;
                    break;
                }
            }

            // 如果所有引导都已显示
            if (empty($step_key)) {
                return json([
                    'code' => 1,
                    'data' => [
                        'should_show_hint' => false,
                        'current_version' => 0,
                        'total_steps' => $this->pageVersions[$page_key]['total_steps']
                    ],
                    'msg' => 'success'
                ]);
            }
        }

        if (!isset($this->pageVersions[$page_key]['steps'][$step_key])) {
            return json(['code' => 0, 'msg' => '无效的步骤标识']);
        }

        // 从数据库中获取提示状态
        $hint_status = Db::name('user_guide_hints')
            ->where('user_id', $user_id)
            ->where('page_key', $page_key)
            ->where('step_key', $step_key)
            ->where('version', $this->pageVersions[$page_key]['steps'][$step_key]['version'])
            ->find();

        return json([
            'code' => 1,
            'data' => [
                'should_show_hint' => empty($hint_status),
                'current_version' => $this->pageVersions[$page_key]['steps'][$step_key]['version'],
                'total_steps' => $this->pageVersions[$page_key]['total_steps'],
                'step_key' => $step_key
            ],
            'msg' => 'success'
        ]);
    }

    /**
     * 更新页面提示状态（标记为已查看）
     * @param Request $request
     * @return Response
     */
    public function updateHintStatus(Request $request): Response
    {
        $user_id = $request->param('user_id');
        $page_key = $request->param('page_key');
        $step_key = $request->param('step_key');
        
        if (empty($user_id) || empty($page_key) || empty($step_key)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        if (!isset($this->pageVersions[$page_key]['steps'][$step_key])) {
            return json(['code' => 0, 'msg' => '无效的步骤标识']);
        }

        // 检查记录是否已存在
        $exists = Db::name('user_guide_hints')
            ->where('user_id', $user_id)
            ->where('page_key', $page_key)
            ->where('step_key', $step_key)
            ->where('version', $this->pageVersions[$page_key]['steps'][$step_key]['version'])
            ->find();

        if (empty($exists)) {
            // 插入新记录
            Db::name('user_guide_hints')->insert([
                'user_id' => $user_id,
                'page_key' => $page_key,
                'step_key' => $step_key,
                'version' => $this->pageVersions[$page_key]['steps'][$step_key]['version'],
                'create_time' => time(),
                'update_time' => time()
            ]);
        }

        return json(['code' => 1, 'msg' => 'success']);
    }

    /**
     * 清除指定页面的所有提示记录（强制所有用户重新查看提示）
     * @param Request $request
     * @return Response
     */
    public function clearPageHints(Request $request): Response
    {
        $page_key = $request->param('page_key');
        
        if (empty($page_key)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        if (!isset($this->pageVersions[$page_key])) {
            return json(['code' => 0, 'msg' => '无效的页面标识']);
        }

        // 删除指定页面的所有提示记录
        Db::name('user_guide_hints')
            ->where('page_key', $page_key)
            ->delete();

        return json(['code' => 1, 'msg' => 'success']);
    }
} 