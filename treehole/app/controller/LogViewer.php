<?php
namespace app\controller;

use app\BaseController;
use app\util\SecretUtil;
use think\Request;
use think\response\Json;
use think\facade\Config;
use think\facade\Session;

/**
 * 日志查看器控制器
 */
class LogViewer extends BaseController
{
    /**
     * 验证访问权限
     */
    private function checkAuth(): bool
    {
        // 本地环境不需要验证
        if (SecretUtil::isLocal()) {
            return true;
        }

        $secretsConfig = Config::get('secrets.log_viewer');
        $logViewerConfig = Config::get('log_viewer');

        // 如果未启用认证，直接通过
        if (!$secretsConfig['enable_auth']) {
            return true;
        }

        // 检查IP白名单
        if (!empty($logViewerConfig['allowed_ips'])) {
            $clientIp = request()->ip();
            if (!in_array($clientIp, $logViewerConfig['allowed_ips'])) {
                return false;
            }
        }

        // 检查session中的认证状态
        return Session::get('log_viewer_auth', false);
    }

    /**
     * 密码验证
     */
    public function auth(Request $request): Json
    {
        $password = $request->param('password', '');
        $secretsConfig = Config::get('secrets.log_viewer');

        if (empty($password)) {
            return json(['code' => 400, 'msg' => '请输入密码']);
        }

        if ($password === $secretsConfig['password']) {
            Session::set('log_viewer_auth', true);
            return json(['code' => 200, 'msg' => '验证成功']);
        }

        return json(['code' => 401, 'msg' => '密码错误']);
    }
    /**
     * 获取日志文件列表
     */
    public function getLogFiles(): Json
    {
        if (!$this->checkAuth()) {
            return json(['code' => 401, 'msg' => '未授权访问']);
        }

        try {
            $logPath = runtime_path() . 'log/';
            
            if (!is_dir($logPath)) {
                return json(['code' => 404, 'msg' => '日志目录不存在']);
            }

            $files = [];
            $handle = opendir($logPath);
            
            while (($file = readdir($handle)) !== false) {
                if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) == 'log') {
                    $filePath = $logPath . $file;
                    $files[] = [
                        'name' => $file,
                        'size' => $this->formatFileSize(filesize($filePath)),
                        'modified' => date('Y-m-d H:i:s', filemtime($filePath)),
                        'type' => $this->getLogType($file)
                    ];
                }
            }
            closedir($handle);

            // 按修改时间倒序排列
            usort($files, function($a, $b) {
                return strtotime($b['modified']) - strtotime($a['modified']);
            });

            return json(['code' => 200, 'data' => $files]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取日志文件失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 读取日志内容
     */
    public function readLog(Request $request): Json
    {
        if (!$this->checkAuth()) {
            return json(['code' => 401, 'msg' => '未授权访问']);
        }

        try {
            $fileName = $request->param('file', '');
            $lines = $request->param('lines', 100, 'intval'); // 默认读取最后100行
            $search = $request->param('search', ''); // 搜索关键词
            
            if (empty($fileName)) {
                return json(['code' => 400, 'msg' => '请指定日志文件名']);
            }

            $logPath = runtime_path() . 'log/' . $fileName;
            
            if (!file_exists($logPath)) {
                return json(['code' => 404, 'msg' => '日志文件不存在']);
            }

            // 读取文件内容
            $content = $this->readLastLines($logPath, $lines);
            
            // 如果有搜索关键词，进行过滤
            if (!empty($search)) {
                $content = $this->filterLogContent($content, $search);
            }

            // 解析日志内容
            $logs = $this->parseLogContent($content);

            return json([
                'code' => 200,
                'data' => [
                    'file' => $fileName,
                    'total_lines' => count($logs),
                    'logs' => $logs
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '读取日志失败: ' . $e->getMessage()]);
        }
    }



    /**
     * 获取日志统计信息
     */
    public function getLogStats(): Json
    {
        if (!$this->checkAuth()) {
            return json(['code' => 401, 'msg' => '未授权访问']);
        }

        try {
            $logPath = runtime_path() . 'log/';
            $stats = [
                'total_files' => 0,
                'total_size' => 0,
                'by_type' => [],
                'recent_errors' => []
            ];

            $handle = opendir($logPath);
            while (($file = readdir($handle)) !== false) {
                if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) == 'log') {
                    $filePath = $logPath . $file;
                    $fileSize = filesize($filePath);
                    $logType = $this->getLogType($file);
                    
                    $stats['total_files']++;
                    $stats['total_size'] += $fileSize;
                    
                    if (!isset($stats['by_type'][$logType])) {
                        $stats['by_type'][$logType] = ['count' => 0, 'size' => 0];
                    }
                    $stats['by_type'][$logType]['count']++;
                    $stats['by_type'][$logType]['size'] += $fileSize;
                }
            }
            closedir($handle);

            // 格式化大小
            $stats['total_size_formatted'] = $this->formatFileSize($stats['total_size']);
            foreach ($stats['by_type'] as &$typeStats) {
                $typeStats['size_formatted'] = $this->formatFileSize($typeStats['size']);
            }

            return json(['code' => 200, 'data' => $stats]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '获取统计信息失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 读取文件的最后N行
     */
    private function readLastLines(string $filePath, int $lines): string
    {
        $file = file($filePath);
        return implode('', array_slice($file, -$lines));
    }

    /**
     * 过滤日志内容
     */
    private function filterLogContent(string $content, string $search): string
    {
        $lines = explode("\n", $content);
        $filtered = array_filter($lines, function($line) use ($search) {
            return stripos($line, $search) !== false;
        });
        return implode("\n", $filtered);
    }

    /**
     * 解析日志内容
     */
    private function parseLogContent(string $content): array
    {
        $lines = explode("\n", $content);
        $logs = [];
        
        foreach ($lines as $line) {
            if (empty(trim($line))) continue;
            
            // 解析日志格式：[时间][级别] 消息
            if (preg_match('/^\[([^\]]+)\]\[([^\]]+)\]\s(.+)$/', $line, $matches)) {
                $logs[] = [
                    'timestamp' => $matches[1],
                    'level' => $matches[2],
                    'message' => $matches[3],
                    'raw' => $line
                ];
            } else {
                // 如果不匹配标准格式，作为原始行处理
                $logs[] = [
                    'timestamp' => '',
                    'level' => 'unknown',
                    'message' => $line,
                    'raw' => $line
                ];
            }
        }
        
        return $logs;
    }

    /**
     * 根据文件名判断日志类型
     */
    private function getLogType(string $fileName): string
    {
        if (strpos($fileName, 'error') !== false) return 'error';
        if (strpos($fileName, 'warning') !== false) return 'warning';
        if (strpos($fileName, 'info') !== false) return 'info';
        if (strpos($fileName, 'debug') !== false) return 'debug';
        if (strpos($fileName, 'api') !== false) return 'api';
        if (strpos($fileName, 'business') !== false) return 'business';
        return 'general';
    }

    /**
     * 格式化文件大小
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
