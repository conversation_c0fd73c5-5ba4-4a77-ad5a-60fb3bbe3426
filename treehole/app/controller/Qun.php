<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\View;
use think\Request;
use think\response\Json;
use app\util\JwtUtil;
use app\util\ImageSecurityUtil;

class Qun extends BaseController
{
    public function getQunById(Request $request)
    {
        // 获取前端传递的 id 值
        $id = $request->param('id');

        // 检查 id 是否为空
        if (empty($id)) {
            return json(['status' => 'error', 'message' => 'id参数不能为空'], 400);
        }

        // 查询 qun 表中对应的记录
        $qun = Db::name('qun')->where('id', $id)->find();

        // 如果没有找到记录，返回错误信息
        if (!$qun) {
            return json(['status' => 'error', 'message' => '未找到对应的群信息'], 404);
        }

        // 返回查询到的数据
        return json(['status' => 'success', 'data' => $qun], 200);
    }
    public function getGroupsByCategory(Request $request)
    {
        // 获取 'category' 参数并转换为整数
        $category = $request->param('category', 0, 'intval');

        if ($category < 0) {
            return json([
                'code' => 400,
                'message' => '类别ID必须为大于0的整数',
                'data' => []
            ], 400);
        }

        try {
            // 根据category的值决定查询条件
            if ($category == 99) {
                // 如果category=6，获取未审核且category为1-5的群聊
                $groups = Db::name('qun')
                    ->where('status', 'unverified')
                    ->whereIn('category', [1, 2, 3, 4, 5])
                    ->field('id, qunname, category, qunnumber, quntitle, qunintro, qunimage, introimage, create_time, create_user_id, status')
                    ->select()
                    ->toArray();

                // 为每个群聊添加创建者信息
                foreach ($groups as &$group) {
                    $creator = Db::name('user')
                        ->where('id', $group['create_user_id'])
                        ->field('id, username, phone')
                        ->find();
                    
                    $group['creator'] = $creator ?: ['id' => 0, 'username' => '未知用户', 'phone' => ''];
                    
                    // 格式化创建时间
                    if (isset($group['create_time'])) {
                        $group['create_time_formatted'] = date('Y-m-d H:i:s', strtotime($group['create_time']));
                    }
                }
            } else {
                // 其他category值，获取已审核的群聊
                $groups = Db::name('qun')
                    ->where('category', $category)
                    ->where('status', 'verified')
                    ->field('id, qunname')
                    ->select()
                    ->toArray();
            }

            return json([
                'code' => 200,
                'message' => '成功',
                'data' => $groups
            ], 200);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('查询群组失败: ' . $e->getMessage());

            return json([
                'code' => 500,
                'message' => '服务器内部错误',
                'data' => []
            ], 500);
        }
    }

    public function uploadImage(Request $request): Json
    {
        if ($file = $request->file('file')) {
            try {
                // 严格的文件安全验证
                $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
                if (!$securityCheck['success']) {
                    return json(['error_code' => 1, 'msg' => $securityCheck['message']]);
                }

                // 获取上传类型（群二维码或群介绍）
                $type = $request->param('type', 'qrcode'); // qrcode 或 intro

                // 使用相对路径，指向public/tupian目录
                $uploadPath = 'tupian/' . ($type === 'qrcode' ? '群二维码' : '群介绍') . '/';

                // 按日期创建子目录
                $currentDate = date('Y-m-d');
                $uploadPath .= $currentDate . '/';

                // 确保目录存在
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }

                // 根据文件内容生成安全的文件名
                $safeExtension = ImageSecurityUtil::getSafeExtension($securityCheck['mime_type']);
                $saveName = ImageSecurityUtil::generateSafeFileName('qun', $safeExtension);
                
                // 移动文件
                $info = $file->move($uploadPath, $saveName);
                if (!$info) {
                    return json(['error_code' => 1, 'msg' => '文件上传失败']);
                }

                // 生成相对路径
                $relativePath = '/tupian/' . ($type === 'qrcode' ? '群二维码' : '群介绍') . '/' . $currentDate . '/' . $saveName;
                
                // 使用固定的域名
                $baseUrl = 'https://www.bjgaoxiaoshequ.store';
                $fullUrl = $baseUrl . $relativePath;

                return json([
                    'error_code' => 0, 
                    'msg' => '上传成功',
                    'data' => [
                        'image_url' => $fullUrl  // 返回完整的HTTPS URL
                    ]
                ]);
                
            } catch (\Exception $e) {
                return json(['error_code' => 1, 'msg' => '上传失败：' . $e->getMessage()]);
            }
        }
        return json(['error_code' => 1, 'msg' => '没有上传文件']);
    }

    /**
     * 添加群聊信息
     * @param Request $request
     * @return Json
     */
    public function addQun(Request $request): Json
    {
        // 获取token
        $token = $request->header('token');
        
        // 验证token
        if (empty($token)) {
            return json(['code' => 401, 'message' => '未授权，请先登录']);
        }

        // 验证token有效性
        $tokenData = JwtUtil::validateToken($token);
        if (!$tokenData) {
            return json(['code' => 401, 'message' => 'token无效或已过期']);
        }

        // 获取user_id
        $user_id = $tokenData['user_id'] ?? null;
        if (!$user_id) {
            return json(['code' => 401, 'message' => 'token信息不完整']);
        }

        // 验证用户状态是否为verified或管理员
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return json(['code' => 404, 'message' => '用户不存在']);
        }
        
        if ($user['status'] !== 'verified' && $user['status'] !== '管理员' && $user['status'] !== 'temporary_verified'&& $user['status'] !== '社团管理员') {
            return json(['code' => 403, 'message' => '请先完成认证后再添加群聊']);
        }

        // 获取请求参数
        $category = $request->param('category', 0, 'intval');
        $qunnumber = $request->param('qunnumber', '', 'trim');
        $quntitle = $request->param('quntitle', '');
        $qunname = $request->param('qunname', '');
        $qunintro = $request->param('qunintro', '');
        $has_intro_image = $request->param('has_intro_image', 'false');

        // 验证必填字段
        if (empty($qunname)) {
            return json(['code' => 400, 'message' => '群名称不能为空']);
        }

        // 验证群号格式
        if (empty($qunnumber) || !preg_match('/^\d{1,12}$/', $qunnumber)) {
            return json(['code' => 400, 'message' => '群号必须是1-12位数字']);
        }

        // 如果群标题和群介绍都为空，将群标题设置为群名
        if (empty($quntitle) && empty($qunintro)) {
            $quntitle = $qunname;
        }

        // 判断群号是否已存在（只检查已审核通过的群聊）
        $existQun = Db::name('qun')
            ->where('qunnumber', $qunnumber)
            ->where('status', 'verified')
            ->find();
        if ($existQun) {
            return json(['code' => 400, 'message' => '该群号已存在']);
        }

        try {
            // 先验证所有必要信息
            if (!$request->file('qrcode')) {
                return json(['code' => 400, 'message' => '请上传群二维码']);
            }

            // 开启事务
            Db::startTrans();
            try {
                // 处理群二维码图片上传
                $qunimage = '';
                $qrcodeFile = $request->file('qrcode');
                
                // 严格的文件安全验证
                $securityCheck = ImageSecurityUtil::validateThinkPHPFile($qrcodeFile);
                if (!$securityCheck['success']) {
                    return json(['code' => 400, 'message' => $securityCheck['message']]);
                }

                // 上传群二维码
                $uploadPath = 'tupian/群二维码/' . date('Y-m-d') . '/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }

                $timestamp = date('YmdHis');
                $safeQunname = preg_replace('/[\\\\\/\:\*\?\"\<\>\|]/', '', $qunname);
                $saveName = $safeQunname . '_' . $timestamp . '.' . $qrcodeFile->extension();
                
                $info = $qrcodeFile->move($uploadPath, $saveName);
                if (!$info) {
                    return json(['code' => 400, 'message' => '群二维码上传失败']);
                }
                
                $qunimage = 'https://www.bjgaoxiaoshequ.store/tupian/群二维码/' . date('Y-m-d') . '/' . $saveName;

                // 构建数据
                $data = [
                    'category' => $category,
                    'qunnumber' => $qunnumber,
                    'quntitle' => $quntitle,
                    'qunname' => $qunname,
                    'qunintro' => $qunintro,
                    'qunimage' => $qunimage,
                    'introimage' => '',  // 介绍图片稍后通过专门的接口上传
                    'create_time' => date('Y-m-d H:i:s'),
                    'create_user_id' => $user_id,
                    'status' => 'unverified' // 新添加的群聊默认为未审核状态
                ];

                // 插入数据
                $qunId = Db::name('qun')->insertGetId($data);

                if ($qunId) {
                    // 提交事务
                    Db::commit();
                    return json([
                        'code' => 200,
                        'message' => '群聊添加成功' . ($has_intro_image === 'true' ? '，请继续上传群介绍图片' : '，等待管理员审核'),
                        'data' => [
                            'id' => $qunId,
                            'qunimage' => $qunimage
                        ]
                    ]);
                } else {
                    // 回滚事务
                    Db::rollback();
                    // 删除已上传的图片
                    @unlink('./public' . parse_url($qunimage, PHP_URL_PATH));
                    return json(['code' => 500, 'message' => '群聊添加失败']);
                }
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 删除已上传的图片
                if (isset($qunimage) && $qunimage) {
                    @unlink('./public' . parse_url($qunimage, PHP_URL_PATH));
                }
                throw $e;
            }
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器内部错误: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 上传群介绍图片
     * @param Request $request
     * @return Json
     */
    public function uploadIntroImage(Request $request): Json
    {
        try {
            // 获取群ID和文件
            $qunId = $request->param('qun_id');
            $file = $request->file('intro_image');

            if (!$qunId || !$file) {
                return json(['code' => 400, 'message' => '参数错误']);
            }

            // 验证群是否存在
            $qun = Db::name('qun')->where('id', $qunId)->find();
            if (!$qun) {
                return json(['code' => 404, 'message' => '群不存在']);
            }

            // 严格的文件安全验证
            $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
            if (!$securityCheck['success']) {
                return json(['code' => 400, 'message' => $securityCheck['message']]);
            }

            // 上传文件
            $uploadPath = 'tupian/群介绍/' . date('Y-m-d') . '/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            $timestamp = date('YmdHis');
            $safeQunname = preg_replace('/[\\\\\/\:\*\?\"\<\>\|]/', '', $qun['qunname']);
            $saveName = $safeQunname . '_' . $timestamp . '.' . $file->extension();
            
            $info = $file->move($uploadPath, $saveName);
            if (!$info) {
                return json(['code' => 400, 'message' => '群介绍图片上传失败']);
            }

            $imageUrl = 'https://www.bjgaoxiaoshequ.store/tupian/群介绍/' . date('Y-m-d') . '/' . $saveName;

            // 更新数据库
            $result = Db::name('qun')
                ->where('id', $qunId)
                ->update(['introimage' => $imageUrl]);

            if ($result) {
                return json([
                    'code' => 200,
                    'message' => '上传成功',
                    'data' => ['url' => $imageUrl]
                ]);
            } else {
                // 删除已上传的文件
                @unlink(str_replace('https://www.bjgaoxiaoshequ.store', 'C:/daima/treehole/public', parse_url($imageUrl, PHP_URL_PATH)));
                return json(['code' => 500, 'message' => '更新数据库失败']);
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '上传失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取所有待审核的群聊
     * @param Request $request
     * @return Json
     */
    public function getPendingGroups(Request $request): Json
    {
        try {
            // 查询所有未审核的群聊，且category不等于1,2,3,4,5
            $pendingGroups = Db::name('qun')
                ->where('status', 'unverified')
                ->whereNotIn('category', [1, 2, 3, 4, 5])
                ->order('create_time', 'desc')
                ->select()
                ->toArray();

            // 为每个群聊添加创建者信息
            foreach ($pendingGroups as &$group) {
                $creator = Db::name('user')
                    ->where('id', $group['create_user_id'])
                    ->field('id, username, phone')
                    ->find();
                
                $group['creator'] = $creator ?: ['id' => 0, 'username' => '未知用户', 'phone' => ''];
                
                // 格式化创建时间
                if (isset($group['create_time'])) {
                    $group['create_time_formatted'] = date('Y-m-d H:i:s', strtotime($group['create_time']));
                }
            }

            return json([
                'code' => 200,
                'message' => '成功',
                'data' => $pendingGroups
            ]);
            
        } catch (\Exception $e) {
            // 记录错误日志
            \think\facade\Log::error('获取待审核群聊失败: ' . $e->getMessage());
            
            return json([
                'code' => 500,
                'message' => '服务器内部错误',
                'data' => []
            ]);
        }
    }

    /**
     * 更新群介绍图片
     * @param Request $request
     * @return Json
     */
    public function updateIntroImage(Request $request): Json
    {
        if ($file = $request->file('intro')) {
            try {
                // 严格的文件安全验证
                $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
                if (!$securityCheck['success']) {
                    return json(['error_code' => 1, 'msg' => $securityCheck['message']]);
                }

                // 使用相对路径，指向public/tupian目录
                $uploadPath = 'tupian/群介绍/';

                // 按日期创建子目录
                $currentDate = date('Y-m-d');
                $uploadPath .= $currentDate . '/';

                // 确保目录存在
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }

                // 获取群名称（如果有）
                $qunname = '';
                if ($id = $request->param('id')) {
                    $qun = Db::name('qun')->where('id', $id)->find();
                    if ($qun) {
                        $qunname = $qun['qunname'];
                    }
                }

                // 根据文件内容生成安全的文件名
                $safeExtension = ImageSecurityUtil::getSafeExtension($securityCheck['mime_type']);
                $timestamp = date('YmdHis');
                $safeQunname = $qunname ? preg_replace('/[\\\\\/\:\*\?\"\<\>\|]/', '', $qunname) : 'qun';
                $saveName = $safeQunname . '_' . $timestamp . '.' . $safeExtension;
                
                // 移动文件
                $info = $file->move($uploadPath, $saveName);
                if (!$info) {
                    return json(['error_code' => 1, 'msg' => '文件上传失败']);
                }

                // 生成相对路径
                $relativePath = '/tupian/群介绍/' . $currentDate . '/' . $saveName;
                
                // 使用固定的域名
                $baseUrl = 'https://www.bjgaoxiaoshequ.store';
                $fullUrl = $baseUrl . $relativePath;

                // 如果有群ID，更新数据库
                if (!empty($id)) {
                    Db::name('qun')->where('id', $id)->update(['introimage' => $fullUrl]);
                }

                return json([
                    'error_code' => 0, 
                    'msg' => '上传成功',
                    'data' => [
                        'image_url' => $fullUrl
                    ]
                ]);
                
            } catch (\Exception $e) {
                return json(['error_code' => 1, 'msg' => '上传失败：' . $e->getMessage()]);
            }
        }
        return json(['error_code' => 1, 'msg' => '没有上传文件']);
    }

    /**
     * 添加社团群聊信息
     * @param Request $request
     * @return Json
     */
    public function addShetuanQun(Request $request): Json
    {
        // 获取token
        $token = $request->header('token');
        
        // 验证token
        if (empty($token)) {
            return json(['code' => 401, 'message' => '未授权，请先登录']);
        }

        // 验证token有效性
        $tokenData = JwtUtil::validateToken($token);
        if (!$tokenData) {
            return json(['code' => 401, 'message' => 'token无效或已过期']);
        }

        // 获取user_id
        $user_id = $tokenData['user_id'] ?? null;
        if (!$user_id) {
            return json(['code' => 401, 'message' => 'token信息不完整']);
        }

        // 验证用户状态是否为verified或管理员
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return json(['code' => 404, 'message' => '用户不存在']);
        }
        
        if ($user['status'] !== 'verified' && $user['status'] !== '管理员' && $user['status'] !== 'temporary_verified'&& $user['status'] !== '社团管理员') {
            return json(['code' => 403, 'message' => '请先完成认证后再添加群聊']);
        }

        // 获取请求参数
        $category = $request->param('category', 0, 'intval');
        $qunnumber = $request->param('qunnumber', '', 'trim');
        $quntitle = $request->param('quntitle', '');
        $qunname = $request->param('qunname', '');
        $qunintro = $request->param('qunintro', '');
        $has_intro_image = $request->param('has_intro_image', 'false');
        $type = $request->param('type', 1, 'intval');

        // 验证必填字段
        if (empty($qunname)) {
            return json(['code' => 400, 'message' => '群名称不能为空']);
        }
        // 验证category是否在1-5之间
        if ($category < 1 || $category > 5) {
            return json(['code' => 400, 'message' => '社团群类别必须在1-5之间']);
        }

        // 如果群标题和群介绍都为空，将群标题设置为群名
        if (empty($quntitle) && empty($qunintro)) {
            $quntitle = $qunname;
        }

        // 判断群号是否已存在（只检查已审核通过的群聊）
        $existQun = Db::name('qun')
            ->where('qunnumber', $qunnumber)
            ->where('status', 'verified')
            ->find();
        if ($existQun) {
            return json(['code' => 400, 'message' => '该群号已存在']);
        }

        try {
            // 先验证所有必要信息
            if (!$request->file('qrcode')) {
                return json(['code' => 400, 'message' => '请上传群二维码']);
            }

            // 开启事务
            Db::startTrans();
            try {
                // 处理群二维码图片上传
                $qunimage = '';
                $qrcodeFile = $request->file('qrcode');
                
                // 验证图片格式和大小
                $validate = validate([
                    'qrcode' => [
                        'fileSize' => 10485760, // 10MB
                        'fileExt' => 'jpg,jpeg,png,gif',
                        'fileMime' => 'image/jpeg,image/png,image/gif'
                    ]
                ]);

                if (!$validate->check(['qrcode' => $qrcodeFile])) {
                    return json(['code' => 400, 'message' => $validate->getError()]);
                }

                // 上传群二维码
                $uploadPath = 'tupian/群二维码/' . date('Y-m-d') . '/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }

                $timestamp = date('YmdHis');
                $safeQunname = preg_replace('/[\\\\\/\:\*\?\"\<\>\|]/', '', $qunname);
                $saveName = $safeQunname . '_' . $timestamp . '.' . $qrcodeFile->extension();
                
                $info = $qrcodeFile->move($uploadPath, $saveName);
                if (!$info) {
                    return json(['code' => 400, 'message' => '群二维码上传失败']);
                }
                
                $qunimage = 'https://www.bjgaoxiaoshequ.store/tupian/群二维码/' . date('Y-m-d') . '/' . $saveName;

                // 根据category设置默认的社团群介绍图片
                $categoryMap = [
                    1 => '人文类',
                    2 => '艺术类',
                    3 => '科技类',
                    4 => '实践类',
                    5 => '体育类'
                ];
                
                $defaultIntroImage = 'https://www.bjgaoxiaoshequ.store/tupian/' . 
                    ($categoryMap[$category] ?? '') . '/' . 
                    preg_replace('/[\\\\\/\:\*\?\"\<\>\|]/', '', $qunname) . '.jpg';

                // 构建数据
                $data = [
                    'category' => $category,
                    'qunnumber' => $qunnumber,
                    'quntitle' => $quntitle,
                    'qunname' => $qunname,
                    'qunintro' => $qunintro,
                    'qunimage' => $qunimage,
                    'introimage' => $defaultIntroImage,
                    'create_time' => date('Y-m-d H:i:s'),
                    'create_user_id' => $user_id,
                    'status' => 'unverified',
                    'type' => $type
                ];

                // 插入数据
                $qunId = Db::name('qun')->insertGetId($data);

                if ($qunId) {
                    // 提交事务
                    Db::commit();
                    return json([
                        'code' => 200,
                        'message' => '社团群添加成功' . ($has_intro_image === 'true' ? '，请继续上传群介绍图片' : '，等待管理员审核'),
                        'data' => [
                            'id' => $qunId,
                            'qunimage' => $qunimage
                        ]
                    ]);
                } else {
                    // 回滚事务
                    Db::rollback();
                    // 删除已上传的图片
                    @unlink('./public' . parse_url($qunimage, PHP_URL_PATH));
                    return json(['code' => 500, 'message' => '社团群添加失败']);
                }
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 删除已上传的图片
                if (isset($qunimage) && $qunimage) {
                    @unlink('./public' . parse_url($qunimage, PHP_URL_PATH));
                }
                throw $e;
            }
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '服务器内部错误: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 获取已验证的社团列表
     * @return Json
     */
    public function getVerifiedOrganizations(): Json
    {
        try {
            // 查询状态为已验证且分类在1-5之间的社团
            $organizations = Db::name('qun')
                ->where('status', 'verified')
                ->where('category', 'in', [1, 2, 3, 4, 5])
                ->field('id, qunname, category')
                ->select()
                ->toArray();
            
            return json([
                'code' => 200,
                'message' => '获取社团列表成功',
                'data' => $organizations
            ]);
        } catch (\Exception $e) {
            \think\facade\Log::error('获取社团列表失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '服务器内部错误',
                'data' => []
            ]);
        }
    }
    
    /**
     * 更新社团公众号二维码
     * @param Request $request
     * @return Json
     */
    public function updateOrganizationQrcode(Request $request): Json
    {
        // 获取token
        $token = $request->header('token');
        
        // 验证token
        if (empty($token)) {
            return json(['code' => 401, 'message' => '未授权，请先登录']);
        }

        // 验证token有效性
        $tokenData = JwtUtil::validateToken($token);
        if (!$tokenData) {
            return json(['code' => 401, 'message' => 'token无效或已过期']);
        }

        // 获取user_id
        $user_id = $tokenData['user_id'] ?? null;
        if (!$user_id) {
            return json(['code' => 401, 'message' => 'token信息不完整']);
        }

        // 验证用户状态是否为verified或管理员
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            return json(['code' => 404, 'message' => '用户不存在']);
        }
        
        if ($user['status'] !== 'verified' && $user['status'] !== '管理员' && $user['status'] !== 'temporary_verified'&& $user['status'] !== '社团管理员') {
            return json(['code' => 403, 'message' => '请先完成认证后再操作']);
        }

        // 获取社团ID
        $organization_id = $request->param('organization_id', 0, 'intval');
        if (!$organization_id) {
            return json(['code' => 400, 'message' => '社团ID不能为空']);
        }

        // 验证社团是否存在
        $organization = Db::name('qun')->where('id', $organization_id)->find();
        if (!$organization) {
            return json(['code' => 404, 'message' => '社团不存在']);
        }

        try {
            // 先验证所有必要信息
            if (!$request->file('qrcode')) {
                return json(['code' => 400, 'message' => '请上传公众号二维码']);
            }

            // 处理公众号二维码图片上传
            $qrcodeFile = $request->file('qrcode');
            
            // 验证图片格式和大小
            $validate = validate([
                'qrcode' => [
                    'fileSize' => 10485760, // 10MB
                    'fileExt' => 'jpg,jpeg,png,gif',
                    'fileMime' => 'image/jpeg,image/png,image/gif'
                ]
            ]);

            if (!$validate->check(['qrcode' => $qrcodeFile])) {
                return json(['code' => 400, 'message' => $validate->getError()]);
            }

            // 上传公众号二维码
            $uploadPath = 'tupian/公众号/' . date('Y-m-d') . '/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            $timestamp = date('YmdHis');
            $safeQunname = preg_replace('/[\\\\\/\:\*\?\"\<\>\|]/', '', $organization['qunname']);
            $saveName = $safeQunname . '_' . $timestamp . '.' . $qrcodeFile->extension();
            
            $info = $qrcodeFile->move($uploadPath, $saveName);
            if (!$info) {
                return json(['code' => 400, 'message' => '二维码上传失败']);
            }
            
            $qrcodeUrl = 'https://bjgaoxiaoshequ.store/tupian/公众号/' . date('Y-m-d') . '/' . $saveName;

            // 更新社团的公众号二维码
            $result = Db::name('qun')->where('id', $organization_id)->update([
                'qrcode' => $qrcodeUrl,
                'update_time' => date('Y-m-d H:i:s'),
                'update_user_id' => $user_id
            ]);

            if ($result) {
                // 检查该社团是否已在organizations表中存在
                $existingOrg = Db::name('organizations')->where('name', $organization['qunname'])->find();
                
                // 确定组织分类ID
                $categoryId = 1; // 默认为官方组织
                switch ($organization['category']) {
                    case 1: $categoryId = 2; break; // 人文类
                    case 2: $categoryId = 3; break; // 艺术类
                    case 3: $categoryId = 4; break; // 科技类
                    case 4: $categoryId = 5; break; // 实践类
                    case 5: $categoryId = 6; break; // 体育类
                }
                
                // 组织表数据
                $orgData = [
                    'name' => $organization['qunname'],
                    'category_id' => $categoryId,
                    'description' => $organization['qunintro'] ?? '',
                    'qrcode' => $qrcodeUrl,
                    'logo' => $organization['qunimage'] ?? '',
                    'update_time' => date('Y-m-d H:i:s')
                ];
                
                if ($existingOrg) {
                    // 更新已存在的组织信息
                    Db::name('organizations')->where('id', $existingOrg['id'])->update($orgData);
                } else {
                    // 创建新组织记录
                    $orgData['create_time'] = date('Y-m-d H:i:s');
                    $orgData['status'] = 1;
                    $newOrgId = Db::name('organizations')->insertGetId($orgData);
                }
                
                return json([
                    'code' => 200,
                    'message' => '公众号二维码更新成功',
                    'data' => [
                        'qrcode_url' => $qrcodeUrl
                    ]
                ]);
            } else {
                // 删除已上传的图片
                @unlink($uploadPath . $saveName);
                return json(['code' => 500, 'message' => '公众号二维码更新失败']);
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('更新公众号二维码失败: ' . $e->getMessage());
            return json([
                'code' => 500,
                'message' => '服务器内部错误: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }
}