<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\Request;
use think\facade\Db;
use app\util\JwtUtil;

class Liaoran extends BaseController
{
    // 获取评分对象列表
    public function getList()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            // 检查表是否存在
            try {
                $tableExists = Db::query("SHOW TABLES LIKE 'liaoran_objects'");
                if (empty($tableExists)) {
                    return json(['code' => 500, 'msg' => '数据库表不存在，请先执行SQL建表语句']);
                }
            } catch (\Exception $e) {
                return json(['code' => 500, 'msg' => '数据库连接失败：' . $e->getMessage()]);
            }

            // 获取评分对象列表，按评分排序
            $objects = Db::name('liaoran_objects')
                ->where('status', 1)
                ->order('avg_rating desc, total_ratings desc, id desc')
                ->select();

            $result = [];
            foreach ($objects as $object) {
                // 获取评分统计
                $ratingStats = Db::name('liaoran_ratings')
                    ->where('object_id', $object['id'])
                    ->where('status', 1)
                    ->field('COUNT(*) as rating_count, AVG(rating) as avg_rating')
                    ->find();

                // 获取评论统计
                $commentStats = Db::name('liaoran_comments')
                    ->where('object_id', $object['id'])
                    ->where('status', 1)
                    ->field('COUNT(*) as comment_count')
                    ->find();

                // 获取热门评论（点赞数最多的评论）
                $hotComment = Db::name('liaoran_comments')
                    ->where('object_id', $object['id'])
                    ->where('status', 1)
                    ->where(function($query) {
                        $query->whereNull('parent_id')->whereOr('parent_id', 0);
                    }) // 只获取主评论
                    ->order('like_count desc, id desc')
                    ->value('content');

                $avgRating = $ratingStats['avg_rating'] ? round(floatval($ratingStats['avg_rating']), 1) : 0.0;
                $ratingCount = $ratingStats['rating_count'] ?: 0;
                $commentCount = $commentStats['comment_count'] ?: 0;
                $totalPeopleCount = $ratingCount + $commentCount;

                $result[] = [
                    'id' => $object['id'],
                    'name' => $object['name'],
                    'image_url' => $object['image_url'],
                    'avg_rating' => $avgRating,
                    'total_ratings' => $ratingCount,
                    'rating_count' => $ratingCount,
                    'comment_count' => $commentCount,
                    'total_people_count' => $totalPeopleCount,
                    'hot_comment' => $hotComment ?: '暂无评论',
                    'created_at' => $object['created_at']
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 获取评分对象详情
    public function getDetail()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $payload['user_id'];
            $objectId = request()->post('id');

            if (!$objectId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 获取评分对象信息
            $object = Db::name('liaoran_objects')
                ->where('id', $objectId)
                ->where('status', 1)
                ->find();

            if (!$object) {
                return json(['code' => 404, 'msg' => '评分对象不存在']);
            }

            // 获取创建者信息
            $creator = Db::name('user')
                ->where('id', $object['created_by'])
                ->field('username')
                ->find();

            // 获取评分统计
            $ratingStats = Db::name('liaoran_ratings')
                ->where('object_id', $objectId)
                ->where('status', 1)
                ->field('COUNT(*) as rating_count, AVG(rating) as avg_rating')
                ->find();

            // 获取用户评分
            $userRating = Db::name('liaoran_ratings')
                ->where('object_id', $objectId)
                ->where('user_id', $userId)
                ->where('status', 1)
                ->value('rating');

            // 获取评论列表
            $comments = $this->getCommentsList($objectId, $userId);

            $avgRating = $ratingStats['avg_rating'] ? round(floatval($ratingStats['avg_rating']), 1) : 0.0;
            $ratingCount = $ratingStats['rating_count'] ?: 0;

            // 转换用户评分为前端显示格式（-10到10转换为1到10）
            $displayUserRating = 0;
            if ($userRating !== null) {
                $displayUserRating = $userRating + 5.5;
                $displayUserRating = max(1, min(10, round($displayUserRating)));
            }

            $result = [
                'id' => $object['id'],
                'name' => $object['name'],
                'image_url' => $object['image_url'],
                'avg_rating' => $avgRating,
                'rating_count' => $ratingCount,
                'user_rating' => $displayUserRating,
                'creator_name' => $creator['username'] ?? '匿名用户',
                'comments' => $comments,
                'created_at' => $object['created_at']
            ];

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 添加评分对象
    public function add()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $payload['user_id'];

            // 检查用户状态
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user || $user['status'] === '禁言' || $user['status'] === 'unverified') {
                return json(['code' => 403, 'msg' => '您当前无法发布内容']);
            }

            $name = request()->post('name');
            $imageUrl = request()->post('image_url', '/images/liaoran.png');

            if (!$name) {
                return json(['code' => 400, 'msg' => '请输入对象名称']);
            }

            // 检查对象名称是否已存在
            $existObject = Db::name('liaoran_objects')
                ->where('name', $name)
                ->where('status', 1)
                ->find();

            if ($existObject) {
                return json(['code' => 400, 'msg' => '该评分对象已存在']);
            }

            // 添加评分对象
            $objectData = [
                'name' => trim($name),
                'image_url' => $imageUrl,
                'created_by' => $userId,
                'avg_rating' => 0,
                'total_ratings' => 0,
                'status' => 1
            ];

            $objectId = Db::name('liaoran_objects')->insertGetId($objectData);

            return json([
                'code' => 200,
                'msg' => '发布成功',
                'data' => ['id' => $objectId]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 添加评分
    public function addRating()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $payload['user_id'];

            // 检查用户状态
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user || $user['status'] === '禁言' || $user['status'] === 'unverified') {
                return json(['code' => 403, 'msg' => '您当前无法评分']);
            }

            $objectId = request()->post('object_id');
            $rating = request()->post('rating');

            if (!$objectId || ($rating === null || $rating === '')) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 确保rating是数字
            $rating = intval($rating);

            if ($rating < -10 || $rating > 10) {
                return json(['code' => 400, 'msg' => '评分范围为-10到10分']);
            }

            // 检查评分对象是否存在
            $object = Db::name('liaoran_objects')
                ->where('id', $objectId)
                ->where('status', 1)
                ->find();

            if (!$object) {
                return json(['code' => 404, 'msg' => '评分对象不存在']);
            }

            // 检查是否已经评分过
            $existRating = Db::name('liaoran_ratings')
                ->where('object_id', $objectId)
                ->where('user_id', $userId)
                ->where('status', 1)
                ->find();

            if ($existRating) {
                // 更新评分
                Db::name('liaoran_ratings')
                    ->where('object_id', $objectId)
                    ->where('user_id', $userId)
                    ->update(['rating' => $rating]);
            } else {
                // 添加新评分
                $ratingData = [
                    'object_id' => $objectId,
                    'user_id' => $userId,
                    'rating' => $rating
                ];
                Db::name('liaoran_ratings')->insert($ratingData);
            }

            // 更新对象平均评分
            $this->updateObjectRating($objectId);

            return json([
                'code' => 200,
                'msg' => '评分成功'
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 添加评论
    public function addComment()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $payload['user_id'];

            // 检查用户状态
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user || $user['status'] === '禁言' || $user['status'] === 'unverified') {
                return json(['code' => 403, 'msg' => '您当前无法评论']);
            }

            $objectId = request()->post('object_id');
            $content = request()->post('content', '');
            $images = request()->post('images', '');
            $parentId = request()->post('parent_id', null);
            $replyToUserId = request()->post('reply_to_user_id', null);

            // 处理参数，确保数据类型正确
            $parentId = $parentId && $parentId !== 'null' && $parentId !== 'undefined' && $parentId !== '0' ? intval($parentId) : null;
            $replyToUserId = $replyToUserId && $replyToUserId !== 'null' && $replyToUserId !== 'undefined' && $replyToUserId !== '0' ? intval($replyToUserId) : null;

            if (!$objectId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            if (!$content && !$images) {
                return json(['code' => 400, 'msg' => '请输入评论内容或选择图片']);
            }

            // 检查评分对象是否存在
            $object = Db::name('liaoran_objects')
                ->where('id', $objectId)
                ->where('status', 1)
                ->find();

            if (!$object) {
                return json(['code' => 404, 'msg' => '评分对象不存在']);
            }

            Db::startTrans();
            try {
                // 添加评论
                $commentData = [
                    'object_id' => $objectId,
                    'user_id' => $userId,
                    'content' => $content ?: '',
                    'images' => $images
                ];

                // 只有当parent_id不为null时才添加
                if ($parentId !== null) {
                    $commentData['parent_id'] = $parentId;
                }

                // 只有当reply_to_user_id不为null时才添加
                if ($replyToUserId !== null) {
                    $commentData['reply_to_user_id'] = $replyToUserId;
                }

                $commentId = Db::name('liaoran_comments')->insertGetId($commentData);

                // 如果是回复，更新父评论的回复数
                if ($parentId) {
                    Db::name('liaoran_comments')
                        ->where('id', $parentId)
                        ->inc('reply_count', 1);
                }

                // 处理推送通知
                $this->handleCommentNotification($userId, $objectId, $parentId, $replyToUserId, $content, $images, $commentId);

                Db::commit();

                return json([
                    'code' => 200,
                    'msg' => '评论成功',
                    'data' => ['id' => $commentId]
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 点赞评论
    public function likeComment()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $payload['user_id'];
            $commentId = request()->post('comment_id');

            if (!$commentId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 检查评论是否存在
            $comment = Db::name('liaoran_comments')
                ->where('id', $commentId)
                ->where('status', 1)
                ->find();

            if (!$comment) {
                return json(['code' => 404, 'msg' => '评论不存在']);
            }

            Db::startTrans();
            try {
                // 检查是否已经点赞
                $existLike = Db::name('unified_likes')
                    ->where('target_type', 'liaoran_comment')
                    ->where('target_id', $commentId)
                    ->where('user_id', $userId)
                    ->find();

                $isLiked = false;
                if (!$existLike) {
                    // 添加点赞记录
                    Db::name('unified_likes')->insert([
                        'user_id' => $userId,
                        'target_type' => 'liaoran_comment',
                        'target_id' => $commentId,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                    $isLiked = true;

                    // 处理点赞通知（只在点赞时发送通知，取消点赞不发送）
                    $this->handleLikeNotification($userId, $comment, 'comment');
                } else {
                    // 删除点赞记录
                    Db::name('unified_likes')
                        ->where('id', $existLike['id'])
                        ->delete();
                    $isLiked = false;
                }

                // 计算最新点赞数
                $totalLikes = Db::name('unified_likes')
                    ->where('target_type', 'liaoran_comment')
                    ->where('target_id', $commentId)
                    ->count();

                // 更新评论的点赞数
                Db::name('liaoran_comments')
                    ->where('id', $commentId)
                    ->update(['like_count' => $totalLikes]);

                Db::commit();

                return json([
                    'code' => 200,
                    'msg' => 'success',
                    'data' => [
                        'is_liked' => $isLiked,
                        'total_likes' => $totalLikes
                    ]
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 点赞回复
    public function likeReply()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $payload['user_id'];
            $replyId = request()->post('reply_id');

            if (!$replyId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 检查回复是否存在
            $reply = Db::name('liaoran_comments')
                ->where('id', $replyId)
                ->where('status', 1)
                ->where(function($query) {
                    $query->whereNotNull('parent_id')->where('parent_id', '>', 0);
                }) // 确保是回复
                ->find();

            if (!$reply) {
                return json(['code' => 404, 'msg' => '回复不存在']);
            }

            Db::startTrans();
            try {
                // 检查是否已经点赞
                $existLike = Db::name('unified_likes')
                    ->where('target_type', 'liaoran_comment')
                    ->where('target_id', $replyId)
                    ->where('user_id', $userId)
                    ->find();

                $isLiked = false;
                if (!$existLike) {
                    // 添加点赞记录
                    Db::name('unified_likes')->insert([
                        'user_id' => $userId,
                        'target_type' => 'liaoran_comment',
                        'target_id' => $replyId,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                    $isLiked = true;

                    // 处理点赞通知（只在点赞时发送通知，取消点赞不发送）
                    $this->handleLikeNotification($userId, $reply, 'reply');
                } else {
                    // 删除点赞记录
                    Db::name('unified_likes')
                        ->where('id', $existLike['id'])
                        ->delete();
                    $isLiked = false;
                }

                // 计算最新点赞数
                $totalLikes = Db::name('unified_likes')
                    ->where('target_type', 'liaoran_comment')
                    ->where('target_id', $replyId)
                    ->count();

                // 更新回复的点赞数
                Db::name('liaoran_comments')
                    ->where('id', $replyId)
                    ->update(['like_count' => $totalLikes]);

                Db::commit();

                return json([
                    'code' => 200,
                    'msg' => 'success',
                    'data' => [
                        'is_liked' => $isLiked,
                        'total_likes' => $totalLikes
                    ]
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 删除评论
    public function deleteComment()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $payload['user_id'];
            $commentId = request()->post('comment_id');

            if (!$commentId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 检查评论是否存在
            $comment = Db::name('liaoran_comments')
                ->where('id', $commentId)
                ->where('status', 1)
                ->find();

            if (!$comment) {
                return json(['code' => 404, 'msg' => '评论不存在']);
            }

            // 验证是否是评论作者
            if ($comment['user_id'] != $userId) {
                return json(['code' => 403, 'msg' => '无权限删除此评论']);
            }

            Db::startTrans();
            try {
                // 软删除评论（将status设为0）
                $result = Db::name('liaoran_comments')
                    ->where('id', $commentId)
                    ->update(['status' => 0]);

                if ($result) {
                    // 如果是回复，需要减少父评论的回复数
                    if ($comment['parent_id'] && $comment['parent_id'] > 0) {
                        Db::name('liaoran_comments')
                            ->where('id', $comment['parent_id'])
                            ->where('reply_count', '>', 0)
                            ->dec('reply_count', 1);
                    }

                    Db::commit();
                    return json(['code' => 200, 'msg' => '删除成功']);
                } else {
                    Db::rollback();
                    return json(['code' => 500, 'msg' => '删除失败']);
                }
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 获取评论列表（包含回复）
    private function getCommentsList($objectId, $userId)
    {
        // 获取主评论
        $comments = Db::name('liaoran_comments')
            ->alias('c')
            ->leftJoin('user u', 'c.user_id = u.id')
            ->where('c.object_id', $objectId)
            ->where(function($query) {
                $query->whereNull('c.parent_id')->whereOr('c.parent_id', 0);
            })
            ->where('c.status', 1)
            ->field('c.*, u.username, u.face_url')
            ->order('c.like_count desc, c.id desc')
            ->select();

        $result = [];
        foreach ($comments as $comment) {
            // 处理评论图片
            $images = [];
            if ($comment['images']) {
                $imageList = json_decode($comment['images'], true);
                if (is_array($imageList)) {
                    $images = $imageList;
                }
            }

            // 检查当前用户是否点赞了这条评论
            $isLiked = Db::name('unified_likes')
                ->where('target_type', 'liaoran_comment')
                ->where('target_id', $comment['id'])
                ->where('user_id', $userId)
                ->count() > 0;

            // 获取回复
            $replies = Db::name('liaoran_comments')
                ->alias('c')
                ->leftJoin('user u', 'c.user_id = u.id')
                ->leftJoin('user ru', 'c.reply_to_user_id = ru.id')
                ->where('c.parent_id', $comment['id'])
                ->where('c.status', 1)
                ->field('c.*, u.username, u.face_url, ru.username as reply_to_username')
                ->order('c.id asc')
                ->select();

            $replyList = [];
            foreach ($replies as $reply) {
                // 处理回复图片
                $replyImages = [];
                if ($reply['images']) {
                    $replyImageList = json_decode($reply['images'], true);
                    if (is_array($replyImageList)) {
                        $replyImages = $replyImageList;
                    }
                }

                // 检查当前用户是否点赞了这条回复
                $replyIsLiked = Db::name('liaoran_comment_likes')
                    ->where('comment_id', $reply['id'])
                    ->where('user_id', $userId)
                    ->count() > 0;

                $replyList[] = [
                    'id' => $reply['id'],
                    'content' => $reply['content'],
                    'images' => $replyImages,
                    'time' => $reply['created_at'],
                    'likes' => $reply['like_count'],
                    'is_liked' => $replyIsLiked,
                    'parent_id' => $reply['parent_id'], // 添加父评论ID
                    'user' => [
                        'id' => $reply['user_id'],
                        'name' => $reply['username'],
                        'avatar' => $reply['face_url'] ?: '/images/weixiao.png'
                    ],
                    'reply_to_user' => $reply['reply_to_user_id'] ? [
                        'id' => $reply['reply_to_user_id'],
                        'name' => $reply['reply_to_username']
                    ] : null,
                    'reply_to_username' => $reply['reply_to_username'] ?: '' // 添加reply_to_username字段以保持一致性
                ];
            }

            $result[] = [
                'id' => $comment['id'],
                'content' => $comment['content'],
                'images' => $images,
                'time' => $comment['created_at'],
                'likes' => $comment['like_count'],
                'is_liked' => $isLiked,
                'user' => [
                    'id' => $comment['user_id'],
                    'name' => $comment['username'],
                    'avatar' => $comment['face_url'] ?: '/images/weixiao.png'
                ],
                'replies' => $replyList
            ];
        }

        return $result;
    }

    // 更新对象平均评分
    private function updateObjectRating($objectId)
    {
        $stats = Db::name('liaoran_ratings')
            ->where('object_id', $objectId)
            ->where('status', 1)
            ->field('COUNT(*) as total_ratings, AVG(rating) as avg_rating')
            ->find();

        $avgRating = $stats['avg_rating'] ? round(floatval($stats['avg_rating']), 1) : 0.0;
        $totalRatings = $stats['total_ratings'] ?: 0;

        Db::name('liaoran_objects')
            ->where('id', $objectId)
            ->update([
                'avg_rating' => $avgRating,
                'total_ratings' => $totalRatings
            ]);
    }

    // ========== 三级结构新增接口 ==========

    // 获取分类列表
    public function getCategoryList()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            // 检查分类表是否存在
            try {
                $tableExists = Db::query("SHOW TABLES LIKE 'liaoran_categories'");
                if (empty($tableExists)) {
                    return json(['code' => 500, 'msg' => '分类表不存在，请先执行SQL建表语句']);
                }
            } catch (\Exception $e) {
                return json(['code' => 500, 'msg' => '数据库连接失败：' . $e->getMessage()]);
            }

            // 获取分类列表，按排序权重和评分排序
            $categories = Db::name('liaoran_categories')
                ->where('status', 1)
                ->order('sort_order asc, avg_rating desc, id desc')
                ->select();

            $result = [];
            foreach ($categories as $category) {
                // 获取该分类下的对象统计
                $objectStats = Db::name('liaoran_objects')
                    ->where('category_id', $category['id'])
                    ->where('status', 1)
                    ->field('COUNT(*) as object_count, AVG(avg_rating) as avg_rating, SUM(total_ratings) as total_ratings')
                    ->find();

                // 获取该分类下所有对象的评论总数
                $commentCount = Db::name('liaoran_comments')
                    ->alias('c')
                    ->leftJoin('liaoran_objects o', 'c.object_id = o.id')
                    ->where('o.category_id', $category['id'])
                    ->where('c.status', 1)
                    ->where('o.status', 1)
                    ->count();

                $avgRating = $objectStats['avg_rating'] ? round(floatval($objectStats['avg_rating']), 1) : 0.0;
                $totalRatings = $objectStats['total_ratings'] ?: 0;
                $totalPeopleCount = $totalRatings + $commentCount;

                $result[] = [
                    'id' => $category['id'],
                    'name' => $category['name'],
                    'description' => $category['description'],
                    'image_url' => $category['image_url'],
                    'avatar_text' => $category['avatar_text'],
                    'avatar_color' => $category['avatar_color'],
                    'avg_rating' => $avgRating,
                    'total_ratings' => $totalRatings,
                    'object_count' => $objectStats['object_count'] ?: 0,
                    'total_people_count' => $totalPeopleCount,
                    'sort_order' => $category['sort_order'],
                    'created_at' => $category['created_at']
                ];
            }

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 获取分类详情（包含该分类下的对象列表）
    public function getCategoryDetail()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $categoryId = request()->post('id');
            if (!$categoryId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 获取分类信息
            $category = Db::name('liaoran_categories')
                ->where('id', $categoryId)
                ->where('status', 1)
                ->find();

            if (!$category) {
                return json(['code' => 404, 'msg' => '分类不存在']);
            }

            // 获取该分类下的对象列表
            $objects = Db::name('liaoran_objects')
                ->where('category_id', $categoryId)
                ->where('status', 1)
                ->order('avg_rating desc, total_ratings desc, id desc')
                ->select();

            $objectList = [];
            foreach ($objects as $object) {
                // 获取评分统计
                $ratingStats = Db::name('liaoran_ratings')
                    ->where('object_id', $object['id'])
                    ->where('status', 1)
                    ->field('COUNT(*) as rating_count, AVG(rating) as avg_rating')
                    ->find();

                // 获取评论统计
                $commentStats = Db::name('liaoran_comments')
                    ->where('object_id', $object['id'])
                    ->where('status', 1)
                    ->field('COUNT(*) as comment_count')
                    ->find();

                // 获取热门评论（点赞数最多的评论）
                $hotComment = Db::name('liaoran_comments')
                    ->where('object_id', $object['id'])
                    ->where('status', 1)
                    ->where(function($query) {
                        $query->whereNull('parent_id')->whereOr('parent_id', 0);
                    }) // 只获取主评论
                    ->order('like_count desc, id desc')
                    ->value('content');

                $avgRating = $ratingStats['avg_rating'] ? round(floatval($ratingStats['avg_rating']), 1) : 0.0;
                $ratingCount = $ratingStats['rating_count'] ?: 0;
                $commentCount = $commentStats['comment_count'] ?: 0;
                $totalPeopleCount = $ratingCount + $commentCount;

                $objectList[] = [
                    'id' => $object['id'],
                    'name' => $object['name'],
                    'description' => $object['description'],
                    'image_url' => $object['image_url'],
                    'avatar_text' => $object['avatar_text'],
                    'avatar_color' => $object['avatar_color'],
                    'avg_rating' => $avgRating,
                    'total_ratings' => $ratingCount,
                    'rating_count' => $ratingCount,
                    'comment_count' => $commentCount,
                    'total_people_count' => $totalPeopleCount,
                    'hot_comment' => $hotComment ?: ($object['description'] ?: '暂无评论'),
                    'created_at' => $object['created_at']
                ];
            }

            $result = [
                'id' => $category['id'],
                'name' => $category['name'],
                'description' => $category['description'],
                'image_url' => $category['image_url'],
                'objects' => $objectList
            ];

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 添加分类
    public function addCategory()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $payload['user_id'];

            // 检查用户状态
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user || $user['status'] === '禁言' || $user['status'] === 'unverified') {
                return json(['code' => 403, 'msg' => '您当前无法发布内容']);
            }

            $name = request()->post('name');
            $description = request()->post('description', '');
            $imageUrl = request()->post('image_url', '');
            $avatarText = request()->post('avatar_text', '');
            $avatarColor = request()->post('avatar_color', '');

            if (!$name) {
                return json(['code' => 400, 'msg' => '请输入分类名称']);
            }

            // 检查分类名称是否已存在
            $existCategory = Db::name('liaoran_categories')
                ->where('name', $name)
                ->where('status', 1)
                ->find();

            if ($existCategory) {
                return json(['code' => 400, 'msg' => '该分类已存在']);
            }

            // 添加分类
            $categoryData = [
                'name' => trim($name),
                'description' => trim($description),
                'created_by' => $userId,
                'avg_rating' => 0,
                'total_ratings' => 0,
                'sort_order' => 0,
                'status' => 1
            ];

            // 如果有图片头像
            if ($imageUrl) {
                $categoryData['image_url'] = $imageUrl;
            }
            // 如果有文字头像
            else if ($avatarText) {
                $categoryData['avatar_text'] = $avatarText;
                $categoryData['avatar_color'] = $avatarColor;
            }
            // 都没有则使用默认文字头像
            else {
                $categoryData['avatar_text'] = '文字头像';
                $categoryData['avatar_color'] = '#A8E6CF';
            }

            $categoryId = Db::name('liaoran_categories')->insertGetId($categoryData);

            return json([
                'code' => 200,
                'msg' => '发布成功',
                'data' => ['id' => $categoryId]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 添加对象（在指定分类下）
    public function addObject()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $payload['user_id'];

            // 检查用户状态
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user || $user['status'] === '禁言' || $user['status'] === 'unverified') {
                return json(['code' => 403, 'msg' => '您当前无法发布内容']);
            }

            $name = request()->post('name');
            $description = request()->post('description', '');
            $categoryId = request()->post('category_id');
            $imageUrl = request()->post('image_url', '');
            $avatarText = request()->post('avatar_text', '');
            $avatarColor = request()->post('avatar_color', '');
            $creatorName = request()->post('creator_name', '匿名用户');

            if (!$name || !$categoryId) {
                return json(['code' => 400, 'msg' => '请输入对象名称和选择分类']);
            }

            // 检查分类是否存在
            $category = Db::name('liaoran_categories')
                ->where('id', $categoryId)
                ->where('status', 1)
                ->find();

            if (!$category) {
                return json(['code' => 404, 'msg' => '分类不存在']);
            }

            // 检查对象名称在该分类下是否已存在
            $existObject = Db::name('liaoran_objects')
                ->where('name', $name)
                ->where('category_id', $categoryId)
                ->where('status', 1)
                ->find();

            if ($existObject) {
                return json(['code' => 400, 'msg' => '该分类下已存在同名对象']);
            }

            // 添加对象
            $objectData = [
                'category_id' => $categoryId,
                'name' => trim($name),
                'description' => trim($description),
                'created_by' => $userId,
                'avg_rating' => 0,
                'total_ratings' => 0,
                'sort_order' => 0,
                'status' => 1
            ];

            // 如果有图片头像
            if ($imageUrl) {
                $objectData['image_url'] = $imageUrl;
            }
            // 如果有文字头像
            else if ($avatarText) {
                $objectData['avatar_text'] = $avatarText;
                $objectData['avatar_color'] = $avatarColor;
            }
            // 都没有则使用默认文字头像
            else {
                $objectData['avatar_text'] = '文字头像';
                $objectData['avatar_color'] = '#A8E6CF';
            }

            $objectId = Db::name('liaoran_objects')->insertGetId($objectData);

            return json([
                'code' => 200,
                'msg' => '发布成功',
                'data' => ['id' => $objectId]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    // 获取对象详情（新的三级结构版本）
    public function getObjectDetail()
    {
        try {
            $token = request()->header('token');
            if (!$token) {
                return json(['code' => 401, 'msg' => '请先登录']);
            }

            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }

            $userId = $payload['user_id'];
            $objectId = request()->post('id');

            if (!$objectId) {
                return json(['code' => 400, 'msg' => '参数错误']);
            }

            // 获取对象信息
            $object = Db::name('liaoran_objects')
                ->where('id', $objectId)
                ->where('status', 1)
                ->find();

            if (!$object) {
                return json(['code' => 404, 'msg' => '评分对象不存在']);
            }

            // 获取创建者信息
            $creator = Db::name('user')
                ->where('id', $object['created_by'])
                ->field('username')
                ->find();

            // 获取评分统计
            $ratingStats = Db::name('liaoran_ratings')
                ->where('object_id', $objectId)
                ->where('status', 1)
                ->field('COUNT(*) as rating_count, AVG(rating) as avg_rating')
                ->find();

            // 获取用户评分
            $userRating = Db::name('liaoran_ratings')
                ->where('object_id', $objectId)
                ->where('user_id', $userId)
                ->where('status', 1)
                ->value('rating');

            // 获取评论列表
            $comments = $this->getCommentsList($objectId, $userId);

            $avgRating = $ratingStats['avg_rating'] ? round(floatval($ratingStats['avg_rating']), 1) : 0.0;
            $ratingCount = $ratingStats['rating_count'] ?: 0;

            // 直接返回原始用户评分，让前端处理转换
            $displayUserRating = $userRating;

            $result = [
                'id' => $object['id'],
                'name' => $object['name'],
                'description' => $object['description'],
                'image_url' => $object['image_url'],
                'avatar_text' => $object['avatar_text'],
                'avatar_color' => $object['avatar_color'],
                'avg_rating' => $avgRating,
                'rating_count' => $ratingCount,
                'user_rating' => $displayUserRating,
                'creator_name' => $creator['username'] ?? '匿名用户',
                'comments' => $comments,
                'created_at' => $object['created_at']
            ];

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 处理评论通知
     */
    private function handleCommentNotification($userId, $objectId, $parentId, $replyToUserId, $content, $images, $commentId = null)
    {
        try {
            // 获取评论者信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) return;

            // 获取第一张图片URL（如果有的话）
            $firstImage = '';
            if (!empty($images) && $images !== '[]') {
                $imagesArray = json_decode($images, true);
                $firstImage = is_array($imagesArray) && !empty($imagesArray) && isset($imagesArray[0]) ? $imagesArray[0] : '';
            }

            if ($parentId) {
                // 这是回复评论
                $parentComment = Db::name('liaoran_comments')->where('id', $parentId)->find();
                if ($parentComment && $parentComment['user_id'] != $userId) {
                    // 通知原评论作者
                    Db::name('notification')->insert([
                        'user_id' => $parentComment['user_id'],
                        'from_user_id' => $userId,
                        'type' => 'reply',
                        'target_type' => 'liaoran_comment',
                        'target_id' => $parentId,
                        'message_id' => $objectId,
                        'content' => $user['username'] . '回复了你的了然几分评论',
                        'target_content' => mb_substr($content, 0, 50),
                        'content_image' => $firstImage,
                        'created_at' => date('Y-m-d H:i:s'),
                        'is_read' => 0
                    ]);

                    // 更新用户未读消息数
                    Db::name('user')->where('id', $parentComment['user_id'])->inc('unread', 1)->update();
                }

                // 如果是回复特定用户，也要通知被回复的用户
                if ($replyToUserId && $replyToUserId != $userId && $replyToUserId != $parentComment['user_id']) {
                    Db::name('notification')->insert([
                        'user_id' => $replyToUserId,
                        'from_user_id' => $userId,
                        'type' => 'reply',
                        'target_type' => 'liaoran_reply',
                        'target_id' => $commentId, // 使用新回复的ID，而不是父评论ID
                        'message_id' => $objectId,
                        'content' => $user['username'] . '回复了你的了然几分回复',
                        'target_content' => mb_substr($content, 0, 50),
                        'content_image' => $firstImage,
                        'created_at' => date('Y-m-d H:i:s'),
                        'is_read' => 0
                    ]);

                    // 更新用户未读消息数
                    Db::name('user')->where('id', $replyToUserId)->inc('unread', 1)->update();
                }
            } else {
                // 这是评论对象，通知对象创建者
                $object = Db::name('liaoran_objects')->where('id', $objectId)->find();
                if ($object && $object['created_by'] != $userId) {
                    Db::name('notification')->insert([
                        'user_id' => $object['created_by'],
                        'from_user_id' => $userId,
                        'type' => 'comment',
                        'target_type' => 'liaoran_object',
                        'target_id' => $objectId,
                        'message_id' => $objectId,
                        'content' => $user['username'] . '评论了你的了然几分对象',
                        'target_content' => mb_substr($content, 0, 50),
                        'content_image' => $firstImage,
                        'created_at' => date('Y-m-d H:i:s'),
                        'is_read' => 0
                    ]);

                    // 更新用户未读消息数
                    Db::name('user')->where('id', $object['created_by'])->inc('unread', 1)->update();
                }
            }
        } catch (\Exception $e) {
            // 通知发送失败不影响评论功能
            error_log("了然几分评论通知发送失败: " . $e->getMessage());
        }
    }

    /**
     * 处理点赞通知
     */
    private function handleLikeNotification($userId, $comment, $type)
    {
        try {
            // 获取点赞者信息
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) return;

            // 只有当点赞者不是评论作者本人时才发送通知
            if ($comment['user_id'] != $userId) {
                $targetType = $type === 'reply' ? 'liaoran_reply' : 'liaoran_comment';
                $contentText = $type === 'reply' ? '点赞了你的了然几分回复' : '点赞了你的了然几分评论';

                Db::name('notification')->insert([
                    'user_id' => $comment['user_id'],
                    'from_user_id' => $userId,
                    'type' => 'like',
                    'target_type' => $targetType,
                    'target_id' => $comment['id'],
                    'message_id' => $comment['object_id'],
                    'content' => $user['username'] . $contentText,
                    'target_content' => $comment['content'],
                    'content_image' => '',
                    'created_at' => date('Y-m-d H:i:s'),
                    'is_read' => 0
                ]);

                // 更新用户未读消息数
                Db::name('user')->where('id', $comment['user_id'])->inc('unread', 1)->update();
            }
        } catch (\Exception $e) {
            // 通知发送失败不影响点赞功能
            error_log("了然几分点赞通知发送失败: " . $e->getMessage());
        }
    }
}
