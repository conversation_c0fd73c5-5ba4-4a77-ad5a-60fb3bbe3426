<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Config;
use think\facade\View;
use think\Request;
use think\response\Json;
use think\App;

/**
 * 微信公众号文章发布控制器
 * 功能：将数据库中的消息以草稿的形式发布到微信公众号
 */
class WechatArticle extends BaseController
{
    // 微信公众号配置
    protected $appId = '';
    protected $appSecret = '';
    
    // 草稿配置
    protected $coverImagePath = '';
    protected $messageLimit = 10;
    protected $author = '';
    protected $titleTemplate = '';
    protected $digest = '';
    
    // 临时素材缓存
    protected $mediaCache = [];
    
    /**
     * 构造函数，加载配置
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        
        // 加载公众号配置
        $this->appId = Config::get('wechat.official_account.app_id', '');
        $this->appSecret = Config::get('wechat.official_account.secret', '');
        
        // 加载草稿配置
        $this->coverImagePath = root_path() . Config::get('wechat.draft.cover_image', 'public/uploads/cover.jpg');
        $this->messageLimit = Config::get('wechat.draft.message_limit', 10);
        $this->author = Config::get('wechat.draft.author', '树洞管理员');
        $this->titleTemplate = Config::get('wechat.draft.title_template', '树洞最新消息汇总 - {date}');
        $this->digest = Config::get('wechat.draft.digest', '今日树洞最新消息汇总');
        
        // 验证必要的配置是否存在
        if (empty($this->appId) || empty($this->appSecret)) {
            // error_log('微信公众号配置缺失：请在配置文件中设置app_id和secret');
        }
    }
    
    /**
     * 管理页面
     */
    public function index()
    {
        // 向视图传递配置信息
        View::assign([
            'appId' => $this->appId,
            'appSecret' => $this->appSecret,
            'coverImagePath' => $this->coverImagePath,
            'messageLimit' => $this->messageLimit,
            'author' => $this->author,
            'titleTemplate' => $this->titleTemplate,
            'digest' => $this->digest
        ]);
        
        // 返回视图
        return View::fetch('wechat_article/index');
    }
    
    /**
     * 创建草稿并发布
     */
    public function createDraft(): Json
    {
        try {
            // 记录开始创建草稿
            // error_log("[微信文章] 开始创建草稿");
            
            // 检查必要配置
            if (empty($this->appId) || empty($this->appSecret)) {
                // error_log("[微信文章] 微信公众号配置缺失");
                return json(['code' => 500, 'msg' => '微信公众号配置缺失：请在配置文件中设置app_id和secret']);
            }
            
            // 1. 获取AccessToken
            $accessToken = $this->getAccessToken();
            if (empty($accessToken)) {
                return json(['code' => 500, 'msg' => '获取access_token失败']);
            }
            
            // 2. 上传封面图片
            // error_log("[微信文章] 开始上传封面图片: " . $this->coverImagePath);
            if (empty($this->coverImagePath) || !file_exists($this->coverImagePath)) {
                // error_log("[微信文章] 封面图片不存在: " . $this->coverImagePath);
                return json(['code' => 500, 'msg' => '封面图片不存在: ' . $this->coverImagePath]);
            }
            
            $thumbMediaId = $this->uploadImage($this->coverImagePath, $accessToken, 'thumb');
            if (empty($thumbMediaId)) {
                return json(['code' => 500, 'msg' => '上传封面图片失败']);
            }
            
            // error_log("[微信文章] 上传封面图片成功，media_id: " . $thumbMediaId);
            
            // 3. 从数据库获取消息
            // error_log("[微信文章] 开始获取数据库消息");
            $messages = $this->getUnpublishedMessages();
            
            if (empty($messages)) {
                // error_log("[微信文章] 没有可发布的消息");
                return json(['code' => 404, 'msg' => '没有可发布的消息']);
            }
            
            // error_log("[微信文章] 获取到 " . count($messages) . " 条消息");
            
            // 4. 上传树洞消息图片到微信服务器并获取media_id
            // error_log("[微信文章] 开始上传消息中的图片");
            // 设置允许的最大处理时间
            set_time_limit(300); // 5分钟
            
            $messages = $this->uploadMessagesImages($messages, $accessToken);
            
            // 检查每条消息的图片上传情况
            $imageCount = 0;
            $successCount = 0;
            foreach ($messages as $message) {
                if (!empty($message['images'])) {
                    $images = json_decode($message['images'], true);
                    if (is_array($images)) {
                        $imageCount += count($images);
                    }
                }
                
                if (!empty($message['image_media_ids'])) {
                    $successCount += count($message['image_media_ids']);
                }
            }
            
            // error_log("[微信文章] 图片上传结果: 成功 " . $successCount . "/" . $imageCount);
            
            // 5. 上传小程序码到微信服务器
            // error_log("[微信文章] 开始上传小程序码");
            $qrCodeMediaId = $this->uploadQRCodeImage($accessToken);
            
            if (!empty($qrCodeMediaId)) {
                // error_log("[微信文章] 上传小程序码成功，media_id: " . $qrCodeMediaId);
            } else {
                // error_log("[微信文章] 上传小程序码失败");
            }
            
            // 6. 生成文章内容
            // error_log("[微信文章] 开始生成文章内容");
            $content = $this->generateContentWithWechatImages($messages, $qrCodeMediaId);
            // error_log("[微信文章] 生成文章内容完成，内容长度: " . strlen($content));
            
            // 7. 创建草稿
            $title = str_replace('{date}', date('Y-m-d'), $this->titleTemplate);
            // error_log("[微信文章] 开始创建草稿，标题: " . $title);
            
            $draftResult = $this->createWechatDraft($title, $content, $thumbMediaId, $this->author, $this->digest, $accessToken);
            
            if (empty($draftResult) || !isset($draftResult['media_id'])) {
                $errorMsg = !empty($draftResult['errmsg']) ? $draftResult['errmsg'] : '未知错误';
                // error_log("[微信文章] 创建草稿失败: " . $errorMsg);
                return json(['code' => 500, 'msg' => '创建草稿失败: ' . $errorMsg, 'data' => $draftResult]);
            }
            
            // error_log("[微信文章] 创建草稿成功，media_id: " . $draftResult['media_id']);
            
            return json([
                'code' => 200, 
                'msg' => '草稿创建成功', 
                'data' => [
                    'draft_id' => $draftResult['media_id'],
                    'message_count' => count($messages),
                    'image_success' => $successCount,
                    'image_total' => $imageCount
                ]
            ]);
        } catch (\Exception $e) {
            // error_log("[微信文章] 创建草稿异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 500, 'msg' => '发生错误: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 发布草稿
     */
    public function publishDraft(Request $request): Json
    {
        try {
            // 检查必要配置
            if (empty($this->appId) || empty($this->appSecret)) {
                return json(['code' => 500, 'msg' => '微信公众号配置缺失：请在配置文件中设置app_id和secret']);
            }
            
            $mediaId = $request->param('media_id', '', 'trim');
            
            if (empty($mediaId)) {
                return json(['code' => 400, 'msg' => '缺少草稿ID']);
            }
            
            // 获取AccessToken
            $accessToken = $this->getAccessToken();
            if (empty($accessToken)) {
                return json(['code' => 500, 'msg' => '获取access_token失败']);
            }
            
            // 发布草稿
            $result = $this->publishWechatDraft($mediaId, $accessToken);
            
            if (!isset($result['publish_id'])) {
                $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : '未知错误';
                return json(['code' => 500, 'msg' => '发布草稿失败: ' . $errorMsg, 'data' => $result]);
            }
            
            return json([
                'code' => 200, 
                'msg' => '发布成功', 
                'data' => [
                    'publish_id' => $result['publish_id'],
                    'msg_data_id' => $result['msg_data_id'] ?? ''
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '发生错误: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 一键创建并发布
     */
    public function createAndPublish(): Json
    {
        try {
            // 创建草稿
            $createResult = $this->createDraft();
            $createData = $createResult->getData();
            
            if ($createData['code'] != 200) {
                return $createResult;
            }
            
            // 发布草稿
            $draftId = $createData['data']['draft_id'];
            $request = new Request();
            $request->withParam('media_id', $draftId);
            
            $publishResult = $this->publishDraft($request);
            
            return $publishResult;
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '一键发布失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取AccessToken
     * @return string 获取到的access_token或空字符串
     */
    private function getAccessToken(): string
    {
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appId}&secret={$this->appSecret}";
        // error_log("[微信文章] 开始获取AccessToken");
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error || $httpCode != 200) {
            // error_log("[微信文章] 获取AccessToken失败: " . ($error ?: "HTTP状态码: " . $httpCode));
            return '';
        }
        
        $result = json_decode($response, true);
        if (!isset($result['access_token'])) {
            $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : '未知错误';
            // error_log("[微信文章] AccessToken获取失败: " . $errorMsg);
            return '';
        }
        
        // error_log("[微信文章] AccessToken获取成功");
        return $result['access_token'];
    }
    
    /**
     * 上传图片到微信服务器临时素材库
     * @param string $imagePath 本地图片路径
     * @param string $accessToken 访问令牌
     * @param string $type 媒体类型(image/thumb)
     * @return string 媒体ID或空字符串
     */
    private function uploadImage(string $imagePath, string $accessToken, string $type = 'image'): string
    {
        if (empty($imagePath) || !file_exists($imagePath)) {
            // error_log("[微信文章] 图片不存在: " . $imagePath);
            return '';
        }
        
        // 验证文件是否为图片及大小是否符合要求
        $fileinfo = finfo_open(FILEINFO_MIME_TYPE);
        $filetype = finfo_file($fileinfo, $imagePath);
        finfo_close($fileinfo);
        
        // 检查文件类型
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
        if (!in_array($filetype, $allowedTypes)) {
            // error_log("[微信文章] 不支持的图片类型: " . $filetype);
            return '';
        }
        
        // 检查文件大小
        $filesize = filesize($imagePath);
        $maxSize = $type === 'thumb' ? 65536 : 10485760; // 缩略图 64KB，普通图片 10MB
        if ($filesize > $maxSize) {
            // error_log("[微信文章] 图片大小超过限制(" . ($maxSize / 1024 / 1024) . "MB): " . ($filesize / 1024 / 1024) . "MB");
            
            // 对于缩略图，如果超过大小限制，可以考虑压缩
            if ($type === 'thumb') {
                // 简单的压缩逻辑，实际项目中可能需要更复杂的处理
                $tempFile = tempnam(sys_get_temp_dir(), 'wx_thumb_');
                $this->compressImage($imagePath, $tempFile, 65536);
                if (file_exists($tempFile) && filesize($tempFile) <= 65536) {
                    $imagePath = $tempFile;
                } else {
                    // error_log("[微信文章] 缩略图压缩失败");
                    @unlink($tempFile);
                    return '';
                }
            } else {
                return '';
            }
        }
        
        // 临时素材上传接口地址
        $url = "https://api.weixin.qq.com/cgi-bin/media/upload?access_token={$accessToken}&type={$type}";
        
        // 重试机制
        $maxRetries = 3;
        $retry = 0;
        
        while ($retry < $maxRetries) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            
            // 准备上传数据，使用CURLFile
            $data = [
                'media' => new \CURLFile(realpath($imagePath), $filetype, basename($imagePath))
            ];
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            // 如果是缩略图的临时文件，删除
            if ($type === 'thumb' && isset($tempFile) && file_exists($tempFile)) {
                @unlink($tempFile);
            }
            
            if (!$error && $httpCode == 200) {
                $result = json_decode($response, true);
                
                if (isset($result['media_id'])) {
                    // 临时素材成功上传，返回media_id
                    // error_log("[微信文章] 上传图片成功, media_id: " . $result['media_id'] . ", 有效期3天");
                    return $result['media_id'];
                } elseif (isset($result['thumb_media_id']) && $type === 'thumb') {
                    // 对于缩略图，返回thumb_media_id
                    // error_log("[微信文章] 上传缩略图成功, thumb_media_id: " . $result['thumb_media_id'] . ", 有效期3天");
                    return $result['thumb_media_id'];
                } elseif (isset($result['errcode'])) {
                    // API返回错误，记录错误信息
                    // error_log("[微信文章] 上传图片API错误: code=" . $result['errcode'] . ", msg=" . ($result['errmsg'] ?? '未知错误'));
                    
                    // 对于token过期错误，不再重试
                    if (in_array($result['errcode'], [40001, 42001])) {
                        return '';
                    }
                    
                    $retry++;
                    if ($retry < $maxRetries) {
                        sleep(2); // 重试前等待
                    }
                    continue;
                }
            }
            
            // 网络错误或其他错误
            // error_log("[微信文章] 上传图片请求失败: " . ($error ?: "HTTP状态码: " . $httpCode));
            $retry++;
            if ($retry < $maxRetries) {
                sleep(2); // 重试前等待
            }
        }
        
        return '';
    }
    
    /**
     * 简单图片压缩函数 (仅用于缩略图)
     * @param string $source 源图片路径
     * @param string $destination 目标图片路径
     * @param int $maxSize 最大允许大小(字节)
     * @return bool 成功与否
     */
    private function compressImage(string $source, string $destination, int $maxSize): bool
    {
        // 获取图片信息
        $info = getimagesize($source);
        if (!$info) {
            return false;
        }
        
        // 根据图片类型创建图像资源
        switch ($info[2]) {
            case IMAGETYPE_JPEG:
                $image = imagecreatefromjpeg($source);
                break;
            case IMAGETYPE_PNG:
                $image = imagecreatefrompng($source);
                break;
            case IMAGETYPE_GIF:
                $image = imagecreatefromgif($source);
                break;
            default:
                return false;
        }
        
        if (!$image) {
            return false;
        }
        
        // 计算新尺寸，确保缩略图不超过微信规定的大小
        $width = $info[0];
        $height = $info[1];
        
        // 缩小到合适尺寸
        $newWidth = min($width, 300); // 缩略图通常不需要太大
        $newHeight = ($height * $newWidth) / $width;
        
        // 创建缩略图
        $thumb = imagecreatetruecolor($newWidth, $newHeight);
        imagecopyresampled($thumb, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
        
        // 保存为JPEG (JPG是微信缩略图唯一支持的格式)
        $quality = 90; // 起始质量
        
        do {
            // 保存图片
            imagejpeg($thumb, $destination, $quality);
            
            // 检查大小
            $size = filesize($destination);
            
            // 如果还是太大，继续降低质量
            if ($size > $maxSize && $quality > 10) {
                $quality -= 10;
            } else {
                break;
            }
        } while (true);
        
        // 释放资源
        imagedestroy($image);
        imagedestroy($thumb);
        
        return filesize($destination) <= $maxSize;
    }
    
    /**
     * 上传网络图片到微信服务器 (临时素材)
     * @param string $imageUrl 图片URL
     * @param string $accessToken 访问令牌
     * @return string 媒体ID或空字符串
     */
    private function uploadImageByUrl(string $imageUrl, string $accessToken): string
    {
        // 实现图片URL的MD5缓存键
        $cacheKey = md5($imageUrl);
        
        // 检查媒体缓存
        if (isset($this->mediaCache[$cacheKey])) {
            // 缓存的media_id可能已过期(3天有效期)，这里为简化处理先尝试使用缓存值
            // error_log("[微信文章] 使用缓存的图片media_id: " . $this->mediaCache[$cacheKey]);
            return $this->mediaCache[$cacheKey];
        }
        
        try {
            // 处理图片URL，确保完整URL
            if (strpos($imageUrl, 'http') !== 0) {
                // 添加域名前缀
                $imageUrl = 'https://www.bjgaoxiaoshequ.store' . (strpos($imageUrl, '/') === 0 ? '' : '/') . $imageUrl;
            }
            
            // error_log("[微信文章] 下载网络图片: " . $imageUrl);
            
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'wx_img_');
            if (!$tempFile) {
                // error_log("[微信文章] 无法创建临时文件");
                return '';
            }
            
            // 下载图片，使用重试机制
            $maxRetries = 3;
            $retry = 0;
            $imageContent = null;
            
            while ($retry < $maxRetries && $imageContent === null) {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $imageUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_HEADER, 0);
                // 添加User-Agent
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
                // 添加Referer
                curl_setopt($ch, CURLOPT_REFERER, 'https://www.bjgaoxiaoshequ.store/');
                
                $imageContent = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                if ($error || $httpCode != 200 || empty($imageContent)) {
                    // error_log("[微信文章] 下载网络图片失败(尝试{$retry}): " . ($error ?: "HTTP状态码: " . $httpCode));
                    $imageContent = null;
                    $retry++;
                    if ($retry < $maxRetries) {
                        sleep(2); // 重试前等待
                    }
                }
            }
            
            if (empty($imageContent)) {
                @unlink($tempFile); // 删除临时文件
                return '';
            }
            
            // 保存图片到临时文件
            file_put_contents($tempFile, $imageContent);
            
            // 验证文件是否为图片
            $fileinfo = finfo_open(FILEINFO_MIME_TYPE);
            $filetype = finfo_file($fileinfo, $tempFile);
            finfo_close($fileinfo);
            
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg', 'image/webp'];
            if (!in_array($filetype, $allowedTypes)) {
                // error_log("[微信文章] 文件不是有效的图片类型: " . $filetype);
                @unlink($tempFile);
                return '';
            }
            
            // 检查图片大小是否符合微信要求(最大10MB)
            $filesize = filesize($tempFile);
            if ($filesize > 10485760) { // 10MB
                // error_log("[微信文章] 图片大小超过限制(10MB): " . ($filesize / 1024 / 1024) . "MB");
                @unlink($tempFile);
                return '';
            }
            
            // 上传到微信服务器临时素材库
            $mediaId = $this->uploadImage($tempFile, $accessToken);
            
            // 删除临时文件
            @unlink($tempFile);
            
            // 缓存媒体ID (3天有效期，但缓存会在程序重启后丢失)
            if (!empty($mediaId)) {
                $this->mediaCache[$cacheKey] = $mediaId;
            }
            
            return $mediaId;
        } catch (\Exception $e) {
            // error_log("[微信文章] 上传网络图片异常: " . $e->getMessage());
            if (isset($tempFile) && file_exists($tempFile)) {
                @unlink($tempFile);
            }
            return '';
        }
    }
    
    /**
     * 创建微信公众号草稿
     * @param string $title 标题
     * @param string $content 内容
     * @param string $thumbMediaId 缩略图media_id
     * @param string $author 作者
     * @param string $digest 摘要
     * @param string $accessToken 访问令牌
     * @return array 响应结果
     */
    private function createWechatDraft(string $title, string $content, string $thumbMediaId, string $author, string $digest, string $accessToken): array
    {
        $url = "https://api.weixin.qq.com/cgi-bin/draft/add?access_token={$accessToken}";
        
        $data = [
            'articles' => [
                [
                    'title' => $title,
                    'author' => $author,
                    'digest' => $digest,
                    'content' => $content,
                    'content_source_url' => '',
                    'thumb_media_id' => $thumbMediaId,
                    'need_open_comment' => 0,
                    'only_fans_can_comment' => 0
                ]
            ]
        ];
        
        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE);
        
        // error_log("[微信文章] 创建草稿请求长度: " . strlen($jsonData));
        
        // 尝试多次提交
        $maxRetries = 3;
        $lastRawResponse = '';
        $lastError = '';
        $lastHttpCode = 0;
        
        for ($retry = 0; $retry < $maxRetries; $retry++) {
            // error_log("[微信文章] 创建草稿请求尝试 #" . ($retry + 1));
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 120); // 长超时时间
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json; charset=UTF-8',
                'Content-Length: ' . strlen($jsonData)
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            // 保存最后一次的原始响应和错误信息，用于调试
            $lastRawResponse = $response;
            $lastError = $error;
            $lastHttpCode = $httpCode;
            
            if (!$error && $httpCode == 200) {
                $result = json_decode($response, true);
                
                // JSON解析错误处理
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $jsonError = json_last_error_msg();
                    // error_log("[微信文章] JSON解析失败: " . $jsonError);
                    
                    // 尝试返回部分原始响应用于调试
                    $responseExcerpt = (strlen($response) > 500) ? 
                        substr($response, 0, 200) . '...' . substr($response, -200) : 
                        $response;
                    
                    continue; // 尝试重试
                }
                
                // 检查是否包含media_id
                if (isset($result['media_id'])) {
                    // error_log("[微信文章] 创建草稿成功: " . $result['media_id']);
                    return $result;
                }
                
                // 如果是API错误，检查是否需要重试
                if (isset($result['errcode']) && $result['errcode'] != 0) {
                    $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : '未知错误';
                    // error_log("[微信文章] 创建草稿API错误: " . $errorMsg);
                    
                    // 如果错误是超时或服务器错误，则重试
                    if ($result['errcode'] >= 500 || in_array($result['errcode'], [40001, 42001])) {
                        // error_log("[微信文章] 准备重试...");
                        sleep(3);
                        continue;
                    }
                    
                    // 其他错误不重试
                    return $result;
                }
            } else {
                // error_log("[微信文章] 创建草稿请求失败: " . ($error ?: "HTTP状态码: " . $httpCode));
                sleep(3);
            }
        }
        
        // error_log("[微信文章] 创建草稿失败，已达到最大重试次数");
        
        // 返回更详细的错误信息
        return [
            'errcode' => -1, 
            'errmsg' => '请求失败，已达到最大重试次数',
            'debug_info' => [
                'http_code' => $lastHttpCode,
                'curl_error' => $lastError,
                'raw_response_excerpt' => (strlen($lastRawResponse) > 1000) ? 
                    substr($lastRawResponse, 0, 300) . '...' . substr($lastRawResponse, -300) : 
                    $lastRawResponse,
                'response_length' => strlen($lastRawResponse)
            ]
        ];
    }
    
    /**
     * 发布微信公众号草稿
     * @param string $mediaId 草稿media_id
     * @param string $accessToken 访问令牌
     * @return array 响应结果
     */
    private function publishWechatDraft(string $mediaId, string $accessToken): array
    {
        $url = "https://api.weixin.qq.com/cgi-bin/freepublish/submit?access_token={$accessToken}";
        
        $data = [
            'media_id' => $mediaId
        ];
        
        $jsonData = json_encode($data);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json; charset=UTF-8',
            'Content-Length: ' . strlen($jsonData)
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error || $httpCode != 200) {
            // error_log("[微信文章] 发布草稿请求失败: " . ($error ?: "HTTP状态码: " . $httpCode));
            return ['errcode' => -1, 'errmsg' => '请求失败: ' . ($error ?: "HTTP状态码: " . $httpCode)];
        }
        
        $result = json_decode($response, true);
        
        if (!isset($result['publish_id'])) {
            $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : '未知错误';
            // error_log("[微信文章] 发布草稿API错误: " . $errorMsg);
            return $result;
        }
        
        // error_log("[微信文章] 发布草稿成功: " . $result['publish_id']);
        return $result;
    }
    
    /**
     * 从数据库获取消息
     * @return array 消息数组
     */
    private function getUnpublishedMessages(): array
    {
        // 获取最新的10条消息，按发送时间降序排序
        $messages = Db::table('message')
            ->where('choose', '<', 100) // 只显示普通消息
            ->order('send_timestamp', 'desc') // 按发送时间降序排列
            ->limit($this->messageLimit)
            ->field('id, user_id, username, content, total_likes, send_timestamp, images, titlename')
            ->select()
            ->toArray();
            
        // 为每条消息计算评论数
        foreach ($messages as &$message) {
            // 从comment表查询评论数
            $commentCount = Db::table('comment')
                ->where('message_id', $message['id'])
                ->count();
                
            // 从post表查询互动数
            $postCount = Db::table('post')
                ->where('message_id', $message['id'])
                ->count();
                
            // 设置评论总数
            $message['total_pinglun'] = $commentCount + $postCount;
        }
        
        return $messages;
    }
    
    /**
     * 上传消息图片到微信服务器
     * @param array $messages 消息数组
     * @param string $accessToken 访问令牌
     * @return array 处理后的消息数组（添加了media_id）
     */
    private function uploadMessagesImages(array $messages, string $accessToken): array
    {
        foreach ($messages as &$message) {
            if (!empty($message['images'])) {
                $images = json_decode($message['images'], true);
                if (is_array($images) && !empty($images)) {
                    $mediaIds = [];
                    
                    foreach ($images as $imageUrl) {
                        // error_log("[微信文章] 处理消息图片: " . $imageUrl);
                        
                        $mediaId = $this->uploadImageByUrl($imageUrl, $accessToken);
                        if (!empty($mediaId)) {
                            $mediaIds[] = $mediaId;
                            // error_log("[微信文章] 图片上传成功，media_id: " . $mediaId);
                        } else {
                            // error_log("[微信文章] 图片上传失败: " . $imageUrl);
                        }
                    }
                    
                    // 将上传成功的media_id添加到消息中
                    $message['image_media_ids'] = $mediaIds;
                    // error_log("[微信文章] 消息ID: " . $message['id'] . " 上传图片结果: " . count($mediaIds) . "/" . count($images) . " 成功");
                }
            }
        }
        
        return $messages;
    }
    
    /**
     * 上传小程序码到微信服务器
     * @param string $accessToken 访问令牌
     * @return string 上传成功返回media_id，失败返回空字符串
     */
    private function uploadQRCodeImage(string $accessToken): string
    {
        // 小程序码图片URL
        $qrcodeUrl = 'https://www.bjgaoxiaoshequ.store/images/xiaochengxu_code.jpg';
        return $this->uploadImageByUrl($qrcodeUrl, $accessToken);
    }
    
    /**
     * 使用微信上传的图片生成文章内容
     * @param array $messages 消息数组
     * @param string $qrCodeMediaId 小程序码media_id
     * @return string 生成的HTML内容
     */
    private function generateContentWithWechatImages(array $messages, string $qrCodeMediaId = ''): string
    {
        // 使用微信公众号支持的简单HTML结构
        $html = '';
        
        // 设置整体背景色 - 使用浅棕色背景
        $html .= '<section style="background-color: #f8f5f2; padding: 1px 0 10px;">';
        
        // 标题部分 - 更美观的渐变背景
        $html .= '<section style="text-align: center; margin: 20px 0;">';
        $html .= '<section style="display: inline-block; background: linear-gradient(to right, #7D5A4F, #A67F6D); padding: 12px 24px; color: white; border-radius: 10px; box-shadow: 0 2px 6px rgba(0,0,0,0.1);">';
        $html .= '<p style="margin: 0; font-size: 18px; font-weight: bold;">树洞最新消息</p>';
        $html .= '<p style="margin: 4px 0 0; font-size: 12px;">发布日期: ' . date('Y-m-d') . '</p>';
        $html .= '</section>';
        $html .= '</section>';
        
        // 介绍说明 - 改进卡片样式
        $html .= '<section style="margin: 0 15px 20px; background: #fff; padding: 15px; border-radius: 10px; color: #5D4037; font-size: 14px; box-shadow: 0 1px 4px rgba(0,0,0,0.05);">';
        $html .= '<p style="margin: 0;">以下是最新的树洞消息，点击每条消息下方的"查看详情"按钮可直接跳转到小程序查看完整内容。</p>';
        $html .= '</section>';
        
        // 消息列表
        foreach ($messages as $index => $message) {
            $html .= $this->formatMessageItemWithWechatImages($message, $index + 1);
        }
        
        // 底部引导区域 - 使用上传到微信的小程序码
        $html .= '<section style="margin: 20px 15px; text-align: center; background: #fff; padding: 20px; border-radius: 10px; box-shadow: 0 1px 4px rgba(0,0,0,0.05);">';
        $html .= '<p style="font-size: 15px; color: #5D4037; margin-bottom: 12px; font-weight: bold;">扫码进入树洞小程序</p>';
        $html .= '<p><br/></p>';
        
        // 使用微信服务器上的小程序码图片
        if (!empty($qrCodeMediaId)) {
            $html .= '<p style="text-align:center">';
            $html .= '<img src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==" data-cropselx1="0" data-cropselx2="140" data-cropsely1="0" data-cropsely2="140" style="width: 140px !important; height: 140px !important; visibility: visible !important;" data-media-id="' . $qrCodeMediaId . '">';
            $html .= '</p>';
        } else {
            // 备用方案，使用原始URL
            $html .= '<p style="text-align:center"><img src="https://www.bjgaoxiaoshequ.store/images/xiaochengxu_code.jpg" style="width:140px;" /></p>';
        }
        
        $html .= '<p style="font-size: 13px; color: #8D6E63; margin-top: 12px;">浏览更多内容，参与校园互动</p>';
        $html .= '</section>';
        
        // 页脚
        $html .= '<section style="margin: 0 15px 15px; background: #fff; border-radius: 10px; padding: 12px; text-align: center; color: #8D6E63; font-size: 12px; box-shadow: 0 1px 4px rgba(0,0,0,0.05);">';
        $html .= '<p>本文由系统自动生成 · 树洞团队</p>';
        $html .= '</section>';
        
        // 结束整体背景区域
        $html .= '</section>';
        
        return $html;
    }
    
    /**
     * 格式化单个消息项（使用微信图片）
     * @param array $message 消息数据
     * @param int $index 序号
     * @return string 格式化后的HTML
     */
    private function formatMessageItemWithWechatImages(array $message, int $index): string
    {
        // 格式化发布时间 - 月日时分
        $time = date('m月d日 H:i', $message['send_timestamp']);
        
        // 处理内容，防止XSS
        $content = htmlspecialchars($message['content']);
        
        // 获取小程序AppID和页面路径
        $wechatConfig = \app\util\SecretUtil::getWechatMiniprogram('main');
        $appId = $wechatConfig['appid'];
        $path = 'packageEmoji/pages/messageDetail/messageDetail?id=' . $message['id'];
        
        // 用户名和标题，如果有头衔则显示
        $titleName = isset($message['titlename']) && !empty($message['titlename']) ? $message['titlename'] : '';
        
        // 评论数
        $commentCount = isset($message['total_pinglun']) ? $message['total_pinglun'] : 0;
        
        // 点赞数
        $likeCount = isset($message['total_likes']) ? $message['total_likes'] : 0;
        
        // 使用微信公众号支持的简单格式，两侧露出背景
        $html = '<section style="margin: 0 15px 16px; background: #fff; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 6px rgba(0,0,0,0.08);">';
        
        // 用户信息区域 - 美化设计
        $html .= '<section style="padding: 12px 15px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center;">';
        // 用户头像占位
        $html .= '<span style="width: 26px; height: 26px; background-color: #A67F6D; border-radius: 50%; display: inline-block; color: white; text-align: center; line-height: 26px; margin-right: 8px; font-size: 14px;">' . mb_substr($message['username'], 0, 1) . '</span>';
        
        // 用户名和头衔
        $html .= '<div style="flex: 1;">';
        $html .= '<strong style="font-size: 15px;">' . $message['username'] . '</strong>';
        
        // 如果有头衔，放在用户名旁边
        if (!empty($titleName)) {
            $html .= ' <span style="color: #ff9800; font-size: 12px; margin-left: 5px; border: 1px solid #ff9800; border-radius: 10px; padding: 0 6px;">' . $titleName . '</span>';
        }
        $html .= '</div>';
        
        // 消息序号
        $html .= '<span style="color: #A67F6D; font-size: 13px;">#' . $index . '</span>';
        
        $html .= '</section>';
        
        // 内容区域 - 优化布局
        $html .= '<section style="padding: 15px; word-break: break-all;">';
        $html .= '<p style="margin: 0; font-size: 15px; line-height: 1.6; color: #333;">' . $content . '</p>';
        
        // 处理图片 - 优先使用微信media_id
        // 处理图片 - 尝试多种方式显示图片
        // 1. 优先使用微信media_id
        if (!empty($message['image_media_ids']) && is_array($message['image_media_ids']) && count($message['image_media_ids']) > 0) {
            $mediaIds = $message['image_media_ids'];
            $html .= '<div style="margin-top: 12px;">';
            
            if (count($mediaIds) == 1) {
                // 单张图片 - 使用微信media_id
                $html .= '<p><br/></p>';
                $html .= '<p style="text-align:center">';
                $html .= '<img src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==" style="max-width:100%; border-radius: 6px; visibility:visible !important;" data-media-id="' . $mediaIds[0] . '" />';
                $html .= '</p>';
            } else {
                // 对于多张图片，使用表格布局
                $imageCount = min(count($mediaIds), 3);
                $html .= '<p><br/></p>';
                
                // 使用表格来排列图片以确保在公众号中按预期显示
                $html .= '<table style="width:100%;">';
                $html .= '<tbody><tr>';
                
                for ($i = 0; $i < $imageCount; $i++) {
                    $html .= '<td style="text-align: center; width:' . (100 / $imageCount) . '%;">';
                    $html .= '<img src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==" style="width:95%; border-radius: 6px; visibility:visible !important;" data-media-id="' . $mediaIds[$i] . '" />';
                    $html .= '</td>';
                }
                
                $html .= '</tr></tbody></table>';
                
                if (count($mediaIds) > 3) {
                    $html .= '<p style="text-align: center; color: #999; font-size: 12px; margin-top: 5px;">共' . count($mediaIds) . '张图片</p>';
                }
            }
            
            $html .= '</div>';
        } 
        // 2. 如果没有media_id但有原始图片URL，在预览模式下直接显示原始图片
        else if (!empty($message['images'])) {
            $images = json_decode($message['images'], true);
            if (is_array($images) && !empty($images)) {
                // 检查当前是否为预览模式
                $isPreview = strpos($_SERVER['REQUEST_URI'], '/preview') !== false;
                
                if ($isPreview) {
                    // 预览模式：直接使用原始图片URL
                    $html .= '<div style="margin-top: 12px;">';
                    
                    if (count($images) == 1) {
                        $html .= '<p><br/></p>';
                        $html .= '<p style="text-align:center">';
                        $html .= '<img src="' . $images[0] . '" style="max-width:100%; border-radius: 6px;" />';
                        $html .= '</p>';
                    } else {
                        $imageCount = min(count($images), 3);
                        $html .= '<p><br/></p>';
                        
                        $html .= '<table style="width:100%;">';
                        $html .= '<tbody><tr>';
                        
                        for ($i = 0; $i < $imageCount; $i++) {
                            $html .= '<td style="text-align: center; width:' . (100 / $imageCount) . '%;">';
                            $html .= '<img src="' . $images[$i] . '" style="width:95%; border-radius: 6px;" />';
                            $html .= '</td>';
                        }
                        
                        $html .= '</tr></tbody></table>';
                        
                        if (count($images) > 3) {
                            $html .= '<p style="text-align: center; color: #999; font-size: 12px; margin-top: 5px;">共' . count($images) . '张图片</p>';
                        }
                    }
                    
                    $html .= '</div>';
                } else {
                    // 非预览模式：提示图片未上传成功
                    $html .= '<div style="margin-top: 12px; border: 1px dashed #ccc; padding: 10px; text-align: center; color: #999; border-radius: 6px;">';
                    $html .= '该消息包含 ' . count($images) . ' 张图片，但未能上传至微信服务器';
                    $html .= '</div>';
                }
            }
        }
        
        $html .= '</section>';
        
        // 底部信息栏 - 使用更美观的布局
        $html .= '<section style="padding: 12px 15px; background: #f9f7f6; border-top: 1px solid #eee; font-size: 13px; color: #888; display: flex; justify-content: space-between; align-items: center;">';
        
        // 左侧基本信息
        $html .= '<div style="flex: 1;">';
        $html .= '<span style="margin-right: 10px;"><i style="font-size: 12px;">💬</i> ' . $commentCount . '</span>';
        $html .= '<span style="margin-right: 10px;"><i style="font-size: 12px;">👍</i> ' . $likeCount . '</span>';
        $html .= '<span><i style="font-size: 12px;">🕒</i> ' . $time . '</span>';
        $html .= '</div>';
        
        // 右侧按钮
        $html .= '<div>';
        $html .= '<a class="weapp_text_link" data-miniprogram-appid="' . $appId . '" ';
        $html .= 'data-miniprogram-path="' . $path . '" ';
        $html .= 'style="display: inline-block; color: #fff; background: linear-gradient(to right, #7D5A4F, #A67F6D); padding: 4px 12px; border-radius: 15px; font-size: 12px; text-decoration: none;">';
        $html .= '查看详情</a>';
        $html .= '</div>';
        
        $html .= '</section>';
        $html .= '</section>';
        
        return $html;
    }
    
    /**
     * API调试工具
     */
    public function debug(Request $request): Json
    {
        try {
            $action = $request->param('action', '', 'trim');
            $params = $request->param('params', '', 'trim');
            
            // 获取AccessToken
            $accessToken = $this->getAccessToken();
            if (empty($accessToken)) {
                return json(['code' => 500, 'msg' => '获取access_token失败']);
            }
            
            switch ($action) {
                case 'upload_image':
                    // 上传图片
                    if (empty($this->coverImagePath) || !file_exists($this->coverImagePath)) {
                        return json(['code' => 500, 'msg' => '封面图片不存在: ' . $this->coverImagePath]);
                    }
                    
                    $mediaId = $this->uploadImage($this->coverImagePath, $accessToken, 'thumb');
                    
                    return json(['code' => 200, 'msg' => '上传图片测试结果', 'data' => [
                        'media_id' => $mediaId,
                        'image_path' => $this->coverImagePath
                    ]]);
                    
                case 'create_draft':
                    // 创建草稿
                    $mediaId = $params;
                    if (empty($mediaId)) {
                        return json(['code' => 400, 'msg' => '请提供媒体ID']);
                    }
                    
                    $messages = $this->getUnpublishedMessages();
                    if (empty($messages)) {
                        return json(['code' => 404, 'msg' => '没有可发布的消息']);
                    }
                    
                    $content = $this->generateContentWithWechatImages($messages);
                    $title = str_replace('{date}', date('Y-m-d'), $this->titleTemplate);
                    
                    $result = $this->createWechatDraft($title, $content, $mediaId, $this->author, $this->digest, $accessToken);
                    
                    return json(['code' => 200, 'msg' => '创建草稿测试结果', 'data' => $result]);
                    
                case 'publish_draft':
                    // 发布草稿
                    $mediaId = $params;
                    if (empty($mediaId)) {
                        return json(['code' => 400, 'msg' => '请提供草稿ID']);
                    }
                    
                    $result = $this->publishWechatDraft($mediaId, $accessToken);
                    
                    return json(['code' => 200, 'msg' => '发布草稿测试结果', 'data' => $result]);
                    
                default:
                    return json(['code' => 400, 'msg' => '未知的操作类型']);
            }
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '调试过程中发生错误: ' . $e->getMessage(), 'trace' => $e->getTraceAsString()]);
        }
    }
    
    /**
     * 预览优化后的排版效果
     */
    public function preview(): \think\Response
    {
        // 从数据库获取消息
        $messages = $this->getUnpublishedMessages();
        
        if (empty($messages)) {
            return response('没有可显示的消息', 404);
        }
        
        // 生成文章内容（使用微信格式但不上传图片）
        $content = $this->generateContentWithWechatImages($messages);
        
        // 构建完整的HTML页面 - 模拟微信公众号环境
        $html = <<<HTML
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>树洞消息预览 - 微信公众号格式</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft Yahei', sans-serif;
            color: #333;
            line-height: 1.6;
        }
        .preview-container {
            max-width: 740px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            padding: 16px;
            box-sizing: border-box;
        }
        .wechat-header {
            background: #07c160;
            color: white;
            padding: 12px 15px;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .wechat-header .back {
            margin-right: 10px;
        }
        .wechat-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(7, 193, 96, 0.9);
            color: white;
            padding: 10px;
            font-size: 12px;
            text-align: center;
            box-shadow: 0 -1px 3px rgba(0,0,0,0.1);
        }
        @media screen and (min-width: 768px) {
            body {
                padding: 20px 0;
                background: #f0f0f0;
            }
            .preview-container {
                border: 1px solid #e0e0e0;
                box-shadow: 0 1px 10px rgba(0,0,0,0.05);
                min-height: auto;
                margin-bottom: 60px;
                border-radius: 8px;
            }
        }
        .msg {
            color: #576b95;
            text-align: center;
            padding: 15px;
            font-size: 13px;
            background: #f0f8ff;
            margin-bottom: 15px;
            border-radius: 6px;
        }
        .actions {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 15px 0;
        }
        .action-btn {
            background: #07c160;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .action-btn.secondary {
            background: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }
        .error-box {
            margin: 20px 0;
            padding: 15px;
            background: #fff0f0;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            color: #721c24;
        }
        .error-code {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .error-message {
            margin-bottom: 15px;
        }
        .error-details {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #e2e6ea;
            white-space: pre-wrap;
            overflow-x: auto;
            font-family: monospace;
            font-size: 12px;
            color: #555;
            max-height: 300px;
            overflow-y: auto;
        }
        .collapsible {
            background: transparent;
            border: none;
            display: block;
            width: 100%;
            text-align: left;
            padding: 5px 0;
            cursor: pointer;
            font-weight: bold;
            color: #007bff;
            margin: 5px 0;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="wechat-header">
        <span class="back">◀</span>
        <span>微信公众号文章预览</span>
    </div>
    <div class="preview-container">
        <div class="msg">
            此预览使用微信公众号富文本格式，实际效果将在微信公众号中展示
        </div>
        
        <div class="actions">
            <button class="action-btn" onclick="executeAction('createDraft')">创建草稿</button>
            <button class="action-btn secondary" onclick="window.history.back()">返回</button>
        </div>
        
        $content
        
        <div class="actions" style="margin-top: 30px;">
            <button class="action-btn" onclick="executeAction('createAndPublish')">一键发布</button>
            <button class="action-btn secondary" onclick="window.location.reload()">刷新预览</button>
        </div>
    </div>
    <div class="wechat-footer">
        预览环境 - 内容将显示为最新10条消息，图片将在实际发布时显示
    </div>
    <script>
        // 处理预览页面中的操作
        function executeAction(action) {
            // 显示加载提示
            const container = document.querySelector('.preview-container');
            container.innerHTML = '<div class="msg" style="margin-top: 30px;">正在执行操作，请稍候...</div>';
            
            // 获取当前URL路径
            const pathParts = window.location.pathname.split('/');
            const baseUrl = '/' + (pathParts[1] || '');
            
            // 发送请求
            fetch(baseUrl + '/wechatArticle/' + action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(async response => {
                // 获取原始响应文本
                const responseText = await response.text();
                console.log('原始响应:', responseText);
                
                // 尝试解析JSON
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    throw {
                        type: 'json_error',
                        message: 'JSON解析失败: ' + e.message,
                        responseText: responseText,
                        status: response.status,
                        statusText: response.statusText
                    };
                }
                
                if (!response.ok) {
                    throw {
                        type: 'http_error',
                        message: '网络请求失败: ' + response.status,
                        status: response.status,
                        statusText: response.statusText,
                        data: data
                    };
                }
                
                return data;
            })
            .then(data => {
                console.log('API响应:', data);
                
                if (data.code === 200) {
                    // 操作成功
                    container.innerHTML = '<div class="msg" style="background:#f0fff0;color:#37874f;margin-top:30px;padding:20px;">' + 
                        '<strong>操作成功！</strong><br/>' + data.msg;
                    
                    // 检查返回的数据是否包含错误信息
                    if (data.data && data.data.errcode && data.data.errcode !== 0) {
                        const errorDetails = data.data.debug_info || {};
                        
                        container.innerHTML += '<div class="error-box" style="margin-top:15px;">' +
                            '<div class="error-code">错误码: ' + data.data.errcode + '</div>' +
                            '<div class="error-message">错误信息: ' + data.data.errmsg + '</div>';
                            
                        // 添加更多错误详情
                        if (Object.keys(errorDetails).length > 0) {
                            container.innerHTML += '<button class="collapsible" onclick="toggleDetails(this)">查看详细信息</button>' + 
                                '<div class="error-details" style="display:none;">' + 
                                formatDebugInfo(errorDetails) + 
                                '</div>';
                        }
                        
                        container.innerHTML += '</div>';
                    } else {
                        container.innerHTML += '<br/><br/>详情：<pre>' + JSON.stringify(data.data, null, 2) + '</pre>';
                    }
                    
                    container.innerHTML += '</div>' +
                        '<div class="actions" style="margin-top:20px;">' +
                        '<button class="action-btn" onclick="window.location.href=\'' + baseUrl + '/wechatArticle/preview\'">返回预览</button>' +
                        '</div>';
                } else {
                    // 操作失败
                    container.innerHTML = '<div class="msg" style="background:#fff0f0;color:#c74751;margin-top:30px;padding:20px;">' + 
                        '<strong>操作失败！</strong><br/>' + data.msg;
                    
                    // 添加错误详情
                    if (data.data) {
                        const errorDetails = data.data.debug_info || data.data;
                        
                        container.innerHTML += '<div class="error-box" style="margin-top:15px;">';
                        
                        if (data.data.errcode) {
                            container.innerHTML += '<div class="error-code">错误码: ' + data.data.errcode + '</div>';
                        }
                        
                        if (data.data.errmsg) {
                            container.innerHTML += '<div class="error-message">错误信息: ' + data.data.errmsg + '</div>';
                        }
                        
                        // 添加更多错误详情
                        if (Object.keys(errorDetails).length > 0) {
                            container.innerHTML += '<button class="collapsible" onclick="toggleDetails(this)">查看详细信息</button>' + 
                                '<div class="error-details" style="display:none;">' + 
                                formatDebugInfo(errorDetails) + 
                                '</div>';
                        }
                        
                        container.innerHTML += '</div>';
                    }
                    
                    container.innerHTML += '</div>' +
                        '<div class="actions" style="margin-top:20px;">' +
                        '<button class="action-btn" onclick="window.location.href=\'' + baseUrl + '/wechatArticle/preview\'">返回预览</button>' +
                        '</div>';
                }
            })
            .catch(error => {
                console.error('操作错误:', error);
                
                // 处理不同类型的错误
                if (error.type === 'json_error') {
                    // JSON解析错误
                    const responseText = error.responseText || '';
                    const excerpt = responseText.length > 1000 ? 
                        responseText.substring(0, 400) + '...\n[内容过长，已截断]\n...' + responseText.substring(responseText.length - 400) :
                        responseText;
                    
                    container.innerHTML = '<div class="msg" style="background:#fff0f0;color:#c74751;margin-top:30px;padding:20px;">' + 
                        '<strong>响应解析错误！</strong><br/>' + error.message +
                        '<div class="error-box" style="margin-top:15px;">' +
                        '<div class="error-code">状态码: ' + error.status + ' ' + error.statusText + '</div>' +
                        '<button class="collapsible" onclick="toggleDetails(this)">查看原始响应</button>' + 
                        '<div class="error-details" style="display:none;">' + 
                        excerpt.replace(/</g, '&lt;').replace(/>/g, '&gt;') + 
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="actions" style="margin-top:20px;">' +
                        '<button class="action-btn" onclick="window.location.href=\'' + baseUrl + '/wechatArticle/preview\'">返回预览</button>' +
                        '</div>';
                } else if (error.type === 'http_error') {
                    // HTTP错误
                    container.innerHTML = '<div class="msg" style="background:#fff0f0;color:#c74751;margin-top:30px;padding:20px;">' + 
                        '<strong>请求错误！</strong><br/>' + error.message;
                    
                    if (error.data) {
                        container.innerHTML += '<div class="error-box" style="margin-top:15px;">' +
                            '<button class="collapsible" onclick="toggleDetails(this)">查看响应详情</button>' + 
                            '<div class="error-details" style="display:none;">' + 
                            JSON.stringify(error.data, null, 2).replace(/</g, '&lt;').replace(/>/g, '&gt;') + 
                            '</div>' +
                            '</div>';
                    }
                    
                    container.innerHTML += '</div>' +
                        '<div class="actions" style="margin-top:20px;">' +
                        '<button class="action-btn" onclick="window.location.href=\'' + baseUrl + '/wechatArticle/preview\'">返回预览</button>' +
                        '</div>';
                } else {
                    // 其他错误
                    container.innerHTML = '<div class="msg" style="background:#fff0f0;color:#c74751;margin-top:30px;padding:20px;">' + 
                        '<strong>请求错误！</strong><br/>' + (error.message || '未知错误') +
                        '</div>' +
                        '<div class="actions" style="margin-top:20px;">' +
                        '<button class="action-btn" onclick="window.location.href=\'' + baseUrl + '/wechatArticle/preview\'">返回预览</button>' +
                        '</div>';
                }
            });
        }
        
        // 格式化调试信息
        function formatDebugInfo(info) {
            if (typeof info !== 'object' || info === null) {
                return String(info).replace(/</g, '&lt;').replace(/>/g, '&gt;');
            }
            
            let result = '';
            for (const key in info) {
                if (Object.hasOwnProperty.call(info, key)) {
                    const value = info[key];
                    result += '<strong>' + key + ':</strong> ';
                    
                    if (typeof value === 'object' && value !== null) {
                        result += '\n' + JSON.stringify(value, null, 2);
                    } else {
                        result += String(value);
                    }
                    
                    result += '\n\n';
                }
            }
            
            return result.replace(/</g, '&lt;').replace(/>/g, '&gt;');
        }
        
        // 切换详情显示
        function toggleDetails(button) {
            const details = button.nextElementSibling;
            details.style.display = details.style.display === 'none' ? 'block' : 'none';
            button.textContent = details.style.display === 'none' ? '查看详细信息' : '隐藏详细信息';
        }
        
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const action = urlParams.get('action');
        
        // 如果有action参数，执行相应操作
        if (action === 'createDraft' || action === 'createAndPublish') {
            executeAction(action);
        }
    </script>
</body>
</html>
HTML;

        // 返回HTML内容
        return response($html)->contentType('text/html');
    }
    
    /**
     * 预览优化后的排版效果 - 纯预览模式，不创建草稿
     */
    public function previewOnly(): \think\Response
    {
        try {
            // 从数据库获取消息
            $messages = $this->getUnpublishedMessages();
            
            if (empty($messages)) {
                return response('没有可显示的消息', 404);
            }
            
            // 生成文章内容（不上传图片，直接使用原始URL）
            $content = $this->generateContentWithWechatImages($messages);
            
            // 构建完整的HTML页面 - 模拟微信公众号环境
            $html = <<<HTML
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>树洞消息预览 - 纯预览模式</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft Yahei', sans-serif;
            color: #333;
            line-height: 1.6;
        }
        .preview-container {
            max-width: 740px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            padding: 16px;
            box-sizing: border-box;
        }
        .wechat-header {
            background: #07c160;
            color: white;
            padding: 12px 15px;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .wechat-header .back {
            margin-right: 10px;
        }
        .wechat-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(7, 193, 96, 0.9);
            color: white;
            padding: 10px;
            font-size: 12px;
            text-align: center;
            box-shadow: 0 -1px 3px rgba(0,0,0,0.1);
        }
        @media screen and (min-width: 768px) {
            body {
                padding: 20px 0;
                background: #f0f0f0;
            }
            .preview-container {
                border: 1px solid #e0e0e0;
                box-shadow: 0 1px 10px rgba(0,0,0,0.05);
                min-height: auto;
                margin-bottom: 60px;
                border-radius: 8px;
            }
        }
        .msg {
            color: #576b95;
            text-align: center;
            padding: 15px;
            font-size: 13px;
            background: #f0f8ff;
            margin-bottom: 15px;
            border-radius: 6px;
        }
        .actions {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 15px 0;
        }
        .action-btn {
            background: #07c160;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .action-btn.secondary {
            background: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="wechat-header">
        <span class="back">◀</span>
        <span>微信公众号文章预览 (纯预览模式)</span>
    </div>
    <div class="preview-container">
        <div class="msg">
            纯预览模式：直接预览内容，不调用微信API，避免超时问题
        </div>
        
        <div class="actions">
            <a href="preview" class="action-btn">标准预览模式</a>
            <a href="diagnose" class="action-btn secondary">系统诊断</a>
        </div>
        
        $content
        
        <div class="actions" style="margin-top: 30px;">
            <a href="preview" class="action-btn">切换到标准预览模式</a>
            <button class="action-btn secondary" onclick="window.history.back()">返回</button>
        </div>
    </div>
    <div class="wechat-footer">
        纯预览模式 - 不调用微信API，图片通过原始URL直接显示
    </div>
</body>
</html>
HTML;

            // 返回HTML内容
            return response($html)->contentType('text/html');
        } catch (\Exception $e) {
            // error_log("[微信文章] 纯预览模式异常: " . $e->getMessage());
            return response('预览失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 诊断接口，用于发送简单请求并返回完整响应内容
     */
    public function diagnose(): Json
    {
        try {
            // error_log("[微信文章] 开始诊断微信API连接");
            
            // 测试系统函数
            $serverInfo = [
                'PHP版本' => PHP_VERSION,
                'cURL版本' => function_exists('curl_version') ? curl_version()['version'] : '未安装',
                'JSON支持' => function_exists('json_encode') ? '支持' : '不支持',
                'SSL支持' => extension_loaded('openssl') ? '支持' : '不支持',
                '最大执行时间' => ini_get('max_execution_time') . '秒',
                '内存限制' => ini_get('memory_limit'),
                '时区设置' => date_default_timezone_get()
            ];
            
            // 测试配置信息
            $configInfo = [
                'AppID' => !empty($this->appId) ? substr($this->appId, 0, 4) . '****' : '未配置',
                'AppSecret' => !empty($this->appSecret) ? '已配置(已隐藏)' : '未配置',
                '封面图片' => !empty($this->coverImagePath) ? (file_exists($this->coverImagePath) ? '存在' : '文件不存在') : '未配置',
                '消息数量限制' => $this->messageLimit,
                '作者' => $this->author,
                '标题模板' => $this->titleTemplate
            ];
            
            // 获取AccessToken (简化版本，避免异常)
            $accessTokenInfo = [];
            
            try {
                $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appId}&secret={$this->appSecret}";
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                $accessTokenInfo = [
                    'HTTP状态码' => $httpCode,
                    'cURL错误' => $error ?: '无'
                ];
                
                if (!$error && $httpCode == 200) {
                    $result = json_decode($response, true);
                    
                    if (isset($result['access_token'])) {
                        $accessTokenInfo['结果'] = '获取成功';
                        $accessTokenInfo['access_token'] = substr($result['access_token'], 0, 10) . '****';
                        $accessTokenInfo['过期时间'] = $result['expires_in'] . '秒';
                    } else {
                        $accessTokenInfo['结果'] = '获取失败';
                        $accessTokenInfo['错误码'] = $result['errcode'] ?? '未知';
                        $accessTokenInfo['错误信息'] = $result['errmsg'] ?? '未知错误';
                    }
                } else {
                    $accessTokenInfo['结果'] = '请求失败';
                    $accessTokenInfo['原始响应'] = $response;
                }
            } catch (\Exception $e) {
                $accessTokenInfo['结果'] = '异常';
                $accessTokenInfo['异常信息'] = $e->getMessage();
            }
            
            // 返回诊断信息
            return json([
                'code' => 200,
                'msg' => '诊断完成',
                'data' => [
                    '系统信息' => $serverInfo,
                    '配置信息' => $configInfo,
                    'AccessToken测试' => $accessTokenInfo
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '诊断过程发生错误: ' . $e->getMessage()]);
        }
    }
} 