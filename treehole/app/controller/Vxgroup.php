<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use think\Request;
use think\facade\Db;
use think\response\Json;
use app\util\JwtUtil;
use app\util\ImageSecurityUtil;

class Vxgroup extends BaseController
{
    /**
     * 更新微信群图片
     * 接收图片，检查管理员权限，替换原图片
     */
    public function updateImage(Request $request): Json
    {
        // 获取请求参数
        $token = $request->post('token', '');
        $fileName = $request->post('fileName', '');
        
        // 检查必要参数
        if (empty($token) || empty($fileName)) {
            return json(['error_code' => 1, 'msg' => '参数不足']);
        }
        
        // 检查文件名有效性
        if (!in_array($fileName, ['微信群1.jpg', '微信群2.jpg', 'trading_group.jpg'])) {
            return json(['error_code' => 1, 'msg' => '文件名不合法']);
        }
        
        // 验证JWT token
        $userData = JwtUtil::validateToken($token);
        if (!$userData) {
            return json(['error_code' => 1, 'msg' => '认证失败，请重新登录']);
        }
        
        // JWT中用户ID存储在user_id字段中
        $userId = $userData['user_id'] ?? 0;
        if (!$userId) {
            return json(['error_code' => 1, 'msg' => '用户信息不完整']);
        }
        
        // 查询用户信息
        try {
            $userInfo = Db::name('user')->where('id', $userId)->find();
            if (!$userInfo) {
                return json(['error_code' => 1, 'msg' => '用户不存在']);
            }
            
            $status = $userInfo['status'] ?? '';
            
            // 检查状态是否为管理员
            if ($status !== '管理员') {
                return json(['error_code' => 1, 'msg' => '权限不足，仅管理员可操作']);
            }
        } catch (\Exception $e) {
            return json(['error_code' => 1, 'msg' => '检查权限失败: ' . $e->getMessage()]);
        }
        
        // 处理上传的图片
        $file = $request->file('image');
        if (!$file) {
            return json(['error_code' => 1, 'msg' => '没有上传文件']);
        }
        
        try {
            // 严格的文件安全验证
            $securityCheck = ImageSecurityUtil::validateThinkPHPFile($file);
            if (!$securityCheck['success']) {
                return json(['error_code' => 1, 'msg' => $securityCheck['message']]);
            }
            
            // 图片保存路径
            $uploadDir = 'tupian/';
            
            // 确保目录存在
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            
            // 定义目标文件的完整路径
            $targetPath = $uploadDir . $fileName;
            
            // 如果文件已存在，先删除
            if (file_exists($targetPath)) {
                unlink($targetPath);
            }
            
            // 移动上传的文件到目标位置
            $info = $file->move($uploadDir, $fileName);
            
            if (!$info) {
                return json(['error_code' => 1, 'msg' => '文件保存失败']);
            }
            
            // 返回成功信息
            return json([
                'error_code' => 0,
                'msg' => '更新图片成功',
                'data' => [
                    'url' => 'https://www.bjgaoxiaoshequ.store/tupian/' . $fileName
                ]
            ]);
            
        } catch (\think\exception\ValidateException $e) {
            return json(['error_code' => 1, 'msg' => '图片验证失败: ' . $e->getMessage()]);
        } catch (\Exception $e) {
            return json(['error_code' => 1, 'msg' => '上传失败: ' . $e->getMessage()]);
        }
    }
} 