<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\Request;
use think\response\Json;

class TemplateMessage extends BaseController
{
    /**
     * 获取微信小程序访问令牌
     * @return array|null 访问令牌信息，包括access_token和expires_in
     */
    private function getAccessToken()
    {
        // 获取微信小程序配置
        $wechatConfig = \app\util\SecretUtil::getWechatMiniprogram('main');
        $appid = $wechatConfig['appid'];
        $appsecret = $wechatConfig['secret'];

        // 获取access_token
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$appsecret}";
        
        try {
            $response = file_get_contents($url);
            $result = json_decode($response, true);
            
            if (isset($result['access_token'])) {
                return $result;
            } else {
                trace('获取access_token失败：' . json_encode($result), 'error');
                return null;
            }
        } catch (\Exception $e) {
            trace('获取access_token异常：' . $e->getMessage(), 'error');
            return null;
        }
    }

    /**
     * 发送意见反馈提醒模板消息
     * @param Request $request 请求对象
     * @return Json 返回JSON响应
     */
    public function sendFeedbackNotification(Request $request): Json
    {
        $params = $request->post();
        
        // 验证必要参数
        if (empty($params['openid']) || empty($params['message_id']) || empty($params['feedback_content'])) {
            return json(['error_code' => 1, 'msg' => '缺少必要参数']);
        }
        
        // 获取access_token
        $tokenResult = $this->getAccessToken();
        if (!$tokenResult) {
            return json(['error_code' => 2, 'msg' => '获取access_token失败']);
        }
        
        $access_token = $tokenResult['access_token'];
        
        // 获取详细的反馈信息
        $message = Db::name('message')
            ->where('id', $params['message_id'])
            ->find();
            
        if (!$message) {
            return json(['error_code' => 3, 'msg' => '意见消息不存在']);
        }
        
        // 获取用户名
        $username = !empty($message['username']) ? $message['username'] : '匿名用户';
        
        // 构建时间显示格式
        $time = date('Y年m月d日 H:i', $message['send_timestamp']);
        
        // 构造模板消息内容 - 根据用户提供的模板结构
        $template_data = [
            'touser' => $params['openid'],
            'template_id' => $params['template_id'],
            'miniprogram' => [
                'appid' => 'wx13d6e0dee303467f',
                'pagepath' => 'pages/foldshare/yijian/yijian'
            ],
            'data' => [
                'thing4' => [
                    'value' => $username
                ],
                'thing9' => [
                    'value' => '新意见反馈'
                ],
                'thing11' => [
                    'value' => '意见建议'
                ],
                'thing8' => [
                    'value' => mb_substr($params['feedback_content'], 0, 20) . (mb_strlen($params['feedback_content']) > 20 ? '...' : '')
                ]
            ]
        ];
        
        // 发送模板消息
        $url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$access_token}";
        
        // 发起POST请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($template_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        
        $response = curl_exec($ch);
        
        if ($response === false) {
            $error = curl_error($ch);
            curl_close($ch);
            trace('发送模板消息失败：' . $error, 'error');
            return json(['error_code' => 4, 'msg' => '发送模板消息失败：' . $error]);
        }
        
        curl_close($ch);
        $result = json_decode($response, true);
        
        // 这里不再记录日志到数据库
        
        if (isset($result['errcode']) && $result['errcode'] == 0) {
            return json(['error_code' => 0, 'msg' => '发送成功', 'data' => $result]);
        } else {
            return json(['error_code' => 5, 'msg' => '发送失败：' . ($result['errmsg'] ?? '未知错误'), 'data' => $result]);
        }
    }
} 