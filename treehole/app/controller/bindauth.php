<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use app\util\JwtUtil;

class bindauth extends BaseController
{
    private $session;
    private const LOGIN_URL = "https://sso.buaa.edu.cn/login?service=";
    private const TARGET_URL = "https://app.buaa.edu.cn/site/center/personal";
    private const INFO_URL = "https://app.buaa.edu.cn/uc/wap/login/get-info";
    private $cookieFile;
    private $secretKey = 'BUAA-SSO-KEY-2024';

    public function __construct()
    {
        $this->cookieFile = runtime_path() . 'cookies_' . uniqid() . '.txt';
        
        $this->session = curl_init();
        curl_setopt($this->session, CURLOPT_COOKIEJAR, $this->cookieFile);
        curl_setopt($this->session, CURLOPT_COOKIEFILE, $this->cookieFile);
        curl_setopt($this->session, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($this->session, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($this->session, CURLOPT_TIMEOUT, 30);
        curl_setopt($this->session, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    }

    private function decrypt($encryptedText)
    {
        try {
            $encryptedBytes = base64_decode($encryptedText);
            $keyBytes = array_map('ord', str_split($this->secretKey));
            $decryptedBytes = '';
            for ($i = 0; $i < strlen($encryptedBytes); $i++) {
                $decryptedBytes .= chr(ord($encryptedBytes[$i]) ^ $keyBytes[$i % count($keyBytes)]);
            }
            return $decryptedBytes;
        } catch (\Exception $e) {
            throw new \Exception('解密失败：' . $e->getMessage());
        }
    }

    public function login()
    {
        try {
            $qqnumber = $this->decrypt(input('post.qqnumber'));
            $username = $this->decrypt(input('post.username'));
            $password = $this->decrypt(input('post.password'));
            $token = input('post.token');

            if (empty($token)) {
                return json(['code' => 0, 'msg' => '未授权，请先登录']);
            }

            $tokenData = JwtUtil::validateToken($token);
            if (!$tokenData || empty($tokenData['user_id'])) {
                return json(['code' => 0, 'msg' => 'token无效或已过期']);
            }

            $user_id = $tokenData['user_id'];
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user) {
                return json(['code' => 0, 'msg' => '用户不存在']);
            }

            $current_time = time();
            $last_request_time = !empty($user['last_request_time']) ? strtotime($user['last_request_time']) : 0;
            $request_count = $user['request_count'] ?? 0;

            if ($current_time - $last_request_time < 60) {
                if ($request_count >= 3) {
                    $wait_time = 60 - ($current_time - $last_request_time);
                    return json(['code' => 0, 'msg' => "请求过于频繁，请在{$wait_time}秒后重试"]);
                }
                Db::name('user')->where('id', $user_id)->update(['request_count' => $request_count + 1]);
            } else {
                Db::name('user')->where('id', $user_id)->update(['last_request_time' => date('Y-m-d H:i:s', $current_time), 'request_count' => 1]);
            }
            
            $executionToken = $this->getToken();
            if (isset($executionToken['code'])) {
                return json($executionToken);
            }

            $loginUrl = self::LOGIN_URL . urlencode(self::TARGET_URL);
            $postData = [
                'username' => $username,
                'password' => $password,
                'execution' => $executionToken,
                '_eventId' => 'submit',
                'type' => 'username_password',
                'submit' => 'LOGIN'
            ];

            curl_setopt($this->session, CURLOPT_URL, $loginUrl);
            curl_setopt($this->session, CURLOPT_POST, true);
            curl_setopt($this->session, CURLOPT_POSTFIELDS, http_build_query($postData));
            
            $response = curl_exec($this->session);
            if ($response === false) {
                throw new \Exception(curl_error($this->session));
            }

            $userInfo = $this->fetchUserInfo();
            return json($userInfo);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '登录失败：' . $e->getMessage()]);
        }
    }

    private function getToken()
    {
        try {
            $loginUrl = self::LOGIN_URL . urlencode(self::TARGET_URL);
            
            curl_setopt($this->session, CURLOPT_URL, $loginUrl);
            curl_setopt($this->session, CURLOPT_POST, false);
            
            $response = curl_exec($this->session);
            if ($response === false) {
                throw new \Exception(curl_error($this->session));
            }
            
            // 提取 execution token
            if (preg_match('/name="execution" value="(.*?)"/', $response, $matches)) {
                return $matches[1];
            }
            
            throw new \Exception('无法获取登录token');
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取token失败：' . $e->getMessage()];
        }
    }

    private function fetchUserInfo()
    {
        try {
            curl_setopt($this->session, CURLOPT_URL, self::INFO_URL);
            curl_setopt($this->session, CURLOPT_POST, false);
            
            $response = curl_exec($this->session);
            if ($response === false) {
                throw new \Exception(curl_error($this->session));
            }
            
            $data = json_decode($response, true);
            if (!$data || !isset($data['d']['info'])) {
                throw new \Exception('获取用户信息失败');
            }
            
            $userInfo = $data['d']['info'];
            
                        $tokenData = JwtUtil::validateToken(input('post.token'));
            if ($tokenData && !empty($tokenData['user_id'])) {
                Db::name('user')->where('id', $tokenData['user_id'])->update([
                    'status' => 'verified',
                    'qq_number' => $this->decrypt(input('post.qqnumber')),
                    'student_id' => $userInfo['xh'] ?? '',
                    'real_name' => $userInfo['xm'] ?? '',
                    'department' => $userInfo['xy'] ?? '',
                    'status_code' => Db::raw('status_code + 1')
                ]);
            }
            
            return [
                'code' => 1,
                'msg' => '认证成功',
                'user_info' => [
                    'student_id' => $userInfo['xh'] ?? '',
                    'real_name' => $userInfo['xm'] ?? '',
                    'department' => $userInfo['xy'] ?? ''
                ]
            ];
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '获取用户信息失败：' . $e->getMessage()];
        }
    }

    public function __destruct()
    {
        curl_close($this->session);
        if (file_exists($this->cookieFile)) {
            unlink($this->cookieFile);
        }
    }
}
