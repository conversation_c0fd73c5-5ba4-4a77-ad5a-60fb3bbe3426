<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信公众号文章发布管理</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .card {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .button {
            display: inline-block;
            background-color: #1890ff;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            margin-right: 10px;
            cursor: pointer;
            border: none;
        }
        .button:hover {
            background-color: #40a9ff;
        }
        .button.danger {
            background-color: #ff4d4f;
        }
        .button.danger:hover {
            background-color: #ff7875;
        }
        .button.success {
            background-color: #52c41a;
        }
        .button.success:hover {
            background-color: #73d13d;
        }
        pre {
            background: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], 
        input[type="number"] {
            padding: 8px;
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .config-item {
            display: flex;
            margin-bottom: 10px;
        }
        .config-name {
            font-weight: bold;
            width: 200px;
        }
        .response-area {
            margin-top: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <h1>微信公众号文章发布管理</h1>
    
    <div class="card">
        <h2>当前配置</h2>
        <div class="config-item">
            <div class="config-name">AppID：</div>
            <div id="app-id">{$appId ?: '未设置'}</div>
        </div>
        <div class="config-item">
            <div class="config-name">Secret：</div>
            <div id="app-secret">{$appSecret ? '******' : '未设置'}</div>
        </div>
        <div class="config-item">
            <div class="config-name">文章标题模板：</div>
            <div id="title-template">{$titleTemplate ?: '未设置'}</div>
        </div>
        <div class="config-item">
            <div class="config-name">作者：</div>
            <div id="author">{$author ?: '未设置'}</div>
        </div>
        <div class="config-item">
            <div class="config-name">每次处理消息数量：</div>
            <div id="message-limit">{$messageLimit ?: '10'}</div>
        </div>
        <div class="config-item">
            <div class="config-name">封面图片：</div>
            <div id="cover-image">{$coverImagePath ?: '未设置'}</div>
        </div>
    </div>
    
    <div class="card">
        <h2>操作</h2>
        <button id="create-draft" class="button">创建草稿</button>
        <button id="one-click-publish" class="button success">一键发布</button>
        <button id="check-messages" class="button">查看待发布消息</button>
    </div>
    
    <div class="card">
        <h2>API调试工具</h2>
        <p>用于分步调试微信公众号接口，确定具体出错位置</p>
        
        <div class="form-group">
            <button id="debug-upload-image" class="button">步骤1：上传封面图片</button>
            <div id="upload-result" style="margin-top: 10px; display: none;">
                <p>媒体ID: <span id="media-id"></span></p>
                <button id="copy-media-id" class="button" style="margin-top: 5px;">复制</button>
            </div>
        </div>
        
        <div class="form-group">
            <label for="draft-media-id">步骤2：创建草稿（需要媒体ID）</label>
            <div style="display: flex;">
                <input type="text" id="draft-media-id" placeholder="请输入上一步获取的媒体ID">
                <button id="debug-create-draft" class="button" style="margin-left: 10px;">创建草稿</button>
            </div>
            <div id="draft-result" style="margin-top: 10px; display: none;">
                <p>草稿ID: <span id="draft-id"></span></p>
                <button id="copy-draft-id" class="button" style="margin-top: 5px;">复制</button>
            </div>
        </div>
        
        <div class="form-group">
            <label for="publish-media-id">步骤3：发布草稿（需要草稿ID）</label>
            <div style="display: flex;">
                <input type="text" id="publish-media-id" placeholder="请输入上一步获取的草稿ID">
                <button id="debug-publish-draft" class="button" style="margin-left: 10px;">发布草稿</button>
            </div>
        </div>
    </div>
    
    <div class="card response-area" id="response-area">
        <h2>操作结果</h2>
        <pre id="response-content"></pre>
    </div>
    
    <div class="card response-area" id="messages-area">
        <h2>待发布消息</h2>
        <div id="messages-content"></div>
    </div>
    
    <script>
        document.getElementById('create-draft').addEventListener('click', function() {
            fetch('/index.php/WechatArticle/createDraft', {
                method: 'POST'
            })
            .then(response => {
                // 即使状态码不是200，也尝试解析JSON
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        // 如果不是JSON，返回原始文本
                        return {code: response.status, msg: "非JSON响应: " + text.substring(0, 500)};
                    }
                });
            })
            .then(data => {
                displayResponse(data);
            })
            .catch(error => {
                displayResponse({
                    code: 500, 
                    msg: '请求出错：' + error.message,
                    stack: error.stack
                });
            });
        });
        
        document.getElementById('one-click-publish').addEventListener('click', function() {
            fetch('/index.php/WechatArticle/createAndPublish', {
                method: 'POST'
            })
            .then(response => {
                // 即使状态码不是200，也尝试解析JSON
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        // 如果不是JSON，返回原始文本
                        return {code: response.status, msg: "非JSON响应: " + text.substring(0, 500)};
                    }
                });
            })
            .then(data => {
                displayResponse(data);
            })
            .catch(error => {
                displayResponse({
                    code: 500, 
                    msg: '请求出错：' + error.message,
                    stack: error.stack
                });
            });
        });
        
        document.getElementById('check-messages').addEventListener('click', function() {
            fetch('/index.php/Message/getMessages', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    page: 1,
                    size: 10,
                    ispull: 1
                })
            })
            .then(response => {
                // 即使状态码不是200，也尝试解析JSON
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        // 如果不是JSON，返回原始文本
                        return {code: response.status, msg: "非JSON响应: " + text.substring(0, 500)};
                    }
                });
            })
            .then(data => {
                displayMessages(data);
            })
            .catch(error => {
                displayResponse({
                    code: 500, 
                    msg: '请求出错：' + error.message,
                    stack: error.stack
                });
            });
        });
        
        function displayResponse(data) {
            const responseArea = document.getElementById('response-area');
            const responseContent = document.getElementById('response-content');
            
            responseArea.style.display = 'block';
            
            // 尝试为不同状态添加不同的样式
            if (data.code === 200) {
                responseContent.style.color = 'green';
            } else {
                responseContent.style.color = 'red';
            }
            
            // 格式化输出所有错误相关信息
            let outputText = JSON.stringify(data, null, 2);
            
            // 如果有原始响应信息，单独显示
            if (data.data && data.data.raw_response) {
                outputText += "\n\n原始响应:\n" + data.data.raw_response;
            }
            
            // 显示堆栈跟踪（如果有）
            if (data.stack) {
                outputText += "\n\n堆栈跟踪:\n" + data.stack;
            }
            
            responseContent.textContent = outputText;
            
            // 滚动到响应区域
            responseArea.scrollIntoView({behavior: 'smooth'});
        }
        
        function displayMessages(data) {
            const messagesArea = document.getElementById('messages-area');
            const messagesContent = document.getElementById('messages-content');
            
            messagesArea.style.display = 'block';
            
            if (data.code !== 200 || !data.data || !data.data.list || data.data.list.length === 0) {
                messagesContent.innerHTML = '<p>没有找到待发布的消息</p>';
                return;
            }
            
            let html = '';
            data.data.list.forEach((message, index) => {
                html += `
                <div class="card" style="background:#fff; margin-bottom:15px;">
                    <h3>消息 #${index + 1}</h3>
                    <div><strong>用户：</strong>${message.username}</div>
                    <div><strong>内容：</strong>${message.content}</div>
                    <div><strong>发布时间：</strong>${new Date(message.send_timestamp * 1000).toLocaleString()}</div>
                    <div><strong>点赞数：</strong>${message.total_likes}</div>
                </div>
                `;
            });
            
            messagesContent.innerHTML = html;
            
            // 滚动到消息区域
            messagesArea.scrollIntoView({behavior: 'smooth'});
        }
        
        // API调试工具
        document.getElementById('debug-upload-image').addEventListener('click', function() {
            fetch('/index.php/WechatArticle/debug', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'upload_image'
                })
            })
            .then(response => {
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        return {code: response.status, msg: "非JSON响应: " + text.substring(0, 500)};
                    }
                });
            })
            .then(data => {
                displayResponse(data);
                
                if (data.code === 200 && data.data && data.data.media_id) {
                    document.getElementById('upload-result').style.display = 'block';
                    document.getElementById('media-id').textContent = data.data.media_id;
                    document.getElementById('draft-media-id').value = data.data.media_id;
                }
            })
            .catch(error => {
                displayResponse({
                    code: 500, 
                    msg: '请求出错：' + error.message,
                    stack: error.stack
                });
            });
        });
        
        document.getElementById('debug-create-draft').addEventListener('click', function() {
            const mediaId = document.getElementById('draft-media-id').value.trim();
            
            if (!mediaId) {
                alert('请先输入媒体ID');
                return;
            }
            
            fetch('/index.php/WechatArticle/debug', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'create_draft',
                    params: mediaId
                })
            })
            .then(response => {
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        return {code: response.status, msg: "非JSON响应: " + text.substring(0, 500)};
                    }
                });
            })
            .then(data => {
                displayResponse(data);
                
                if (data.code === 200 && data.data && data.data.media_id) {
                    document.getElementById('draft-result').style.display = 'block';
                    document.getElementById('draft-id').textContent = data.data.media_id;
                    document.getElementById('publish-media-id').value = data.data.media_id;
                }
            })
            .catch(error => {
                displayResponse({
                    code: 500, 
                    msg: '请求出错：' + error.message,
                    stack: error.stack
                });
            });
        });
        
        document.getElementById('debug-publish-draft').addEventListener('click', function() {
            const mediaId = document.getElementById('publish-media-id').value.trim();
            
            if (!mediaId) {
                alert('请先输入草稿ID');
                return;
            }
            
            fetch('/index.php/WechatArticle/debug', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'publish_draft',
                    params: mediaId
                })
            })
            .then(response => {
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        return {code: response.status, msg: "非JSON响应: " + text.substring(0, 500)};
                    }
                });
            })
            .then(data => {
                displayResponse(data);
            })
            .catch(error => {
                displayResponse({
                    code: 500, 
                    msg: '请求出错：' + error.message,
                    stack: error.stack
                });
            });
        });
        
        // 复制功能
        document.getElementById('copy-media-id').addEventListener('click', function() {
            const mediaId = document.getElementById('media-id').textContent;
            navigator.clipboard.writeText(mediaId).then(() => {
                alert('复制成功');
            });
        });
        
        document.getElementById('copy-draft-id').addEventListener('click', function() {
            const draftId = document.getElementById('draft-id').textContent;
            navigator.clipboard.writeText(draftId).then(() => {
                alert('复制成功');
            });
        });
    </script>
</body>
</html> 