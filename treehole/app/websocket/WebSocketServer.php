<?php
namespace app\websocket;

use Workerman\Worker;
use Workerman\Connection\TcpConnection;
use think\facade\Db;
use think\facade\Cache;

/**
 * @property int $workerId
 */
#[\AllowDynamicProperties]
class WebSocketServer
{
    protected $worker;
    protected $userConnections = [];
    protected $heartbeatInterval = 55;  // 心跳间隔秒数
    protected $maxHeartbeatFailures = 3;  // 最大心跳失败次数
    public $workerId = 0;  // 直接定义workerId属性
    protected $logFile;
    protected $businessLogFile;  // 新增业务日志文件

    public function __construct()
    {
        // 初始化日志文件
        $logDir = dirname(dirname(__DIR__)) . '/runtime/websocket/';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }
        
        $this->logFile = $logDir . 'websocket_' . date('Y-m-d') . '.log';
        $this->businessLogFile = $logDir . 'business_' . date('Y-m-d') . '.log';  // 业务日志文件
        
        // 初始化ThinkPHP应用
        $this->initThinkPHP();
        
        // 创建worker
        $this->worker = new Worker('websocket://0.0.0.0:2345');
$this->worker->transport = 'tcp';  // 添加这行
        
        // 设置进程数
        $this->worker->count = 1;
        
        // 设置名称
        $this->worker->name = 'TreeholeWebSocket';
        
        // 设置回调
        $this->worker->onWorkerStart = [$this, 'onWorkerStart'];
        $this->worker->onConnect = [$this, 'onConnect'];
        $this->worker->onMessage = [$this, 'onMessage'];
        $this->worker->onClose = [$this, 'onClose'];
        $this->worker->onError = [$this, 'onError'];
    }

    protected function initThinkPHP()
    {
        try {
            // 加载ThinkPHP核心文件
            $rootPath = dirname(dirname(__DIR__)) . DIRECTORY_SEPARATOR;
            $this->log("Root路径: " . $rootPath);
            
            require $rootPath . 'vendor/autoload.php';
            $this->log("自动加载文件加载成功");
            
            // 初始化应用
            $app = new \think\App();
            $app->initialize();
            $this->log("ThinkPHP应用初始化成功");

            // 加载数据库配置
            $config = include $rootPath . 'config/database.php';
            
            // 设置数据库配置
            \think\facade\Config::set([
                'default' => 'mysql',
                'connections' => [
                    'mysql' => [
                        'type'     => 'mysql',
                        'hostname' => $config['connections']['mysql']['hostname'],
                        'database' => $config['connections']['mysql']['database'],
                        'username' => $config['connections']['mysql']['username'],
                        'password' => $config['connections']['mysql']['password'],
                        'hostport' => $config['connections']['mysql']['hostport'],
                        'charset'  => 'utf8mb4',
                        'prefix'   => '',
                        'break_reconnect' => true,  // 断线重连
                        'heartbeat' => 60,  // 心跳检测间隔
                    ],
                ],
            ], 'database');

            // 初始化数据库
            Db::connect('mysql');
            $this->log("数据库配置加载成功");
            
        } catch (\Exception $e) {
            $this->log("初始化ThinkPHP失败: " . $e->getMessage(), 'error');
            throw $e;
        }
    }

    public function start()
    {
        Worker::runAll();
    }

    public function onWorkerStart($worker)
    {
        $this->log("WebSocket服务器启动 - 监听端口: " . $worker->getSocketName());
        
        // 初始化数据库连接
        try {
            Db::connect('mysql');
            $this->log("数据库连接成功");
        } catch (\Exception $e) {
            $this->log("数据库连接失败: " . $e->getMessage(), 'error');
        }
        
        // 设置定时器进行心跳检测，每10秒检测一次
        \Workerman\Timer::add(10, function() use ($worker) {
            $this->log("开始心跳检测 - 当前连接数: " . count($worker->connections), 'info', true);
            foreach ($worker->connections as $connection) {
                $currentTime = time();
                $timeSinceLastHeartbeat = $currentTime - $connection->lastHeartbeatTime;
                $this->log("检查连接 - 客户端IP: " . $connection->getRemoteIp() . 
                          ", 上次心跳: " . date('Y-m-d H:i:s', $connection->lastHeartbeatTime) . 
                          ", 间隔: {$timeSinceLastHeartbeat}秒" .
                          ", 失败次数: {$connection->heartbeatFailures}" .
                          (isset($connection->userId) ? ", 用户ID: " . $connection->userId : ""), 'info', true);

                // 发送心跳包
                try {
                    $this->log("发送心跳包 - 客户端IP: " . $connection->getRemoteIp(), 'info', true);
                    $connection->send(json_encode([
                        'type' => 'ping',
                        'time' => $currentTime
                    ]));
                } catch (\Exception $e) {
                    $this->log("发送心跳失败: " . $e->getMessage() . " - 客户端IP: " . $connection->getRemoteIp(), 'error', true);
                }

                // 检查心跳超时，15秒超时
                if ($timeSinceLastHeartbeat > 15) {
                    $connection->heartbeatFailures++;
                    $this->log("心跳检测失败 - 客户端IP: " . $connection->getRemoteIp() . 
                              ", 失败次数: {$connection->heartbeatFailures}, " . 
                              "最后心跳时间: " . date('Y-m-d H:i:s', $connection->lastHeartbeatTime) . 
                              ", 超时: {$timeSinceLastHeartbeat}秒" .
                              (isset($connection->userId) ? ", 用户ID: " . $connection->userId : ""), 'info', true);

                    // 超过最大失败次数，关闭连接
                    if ($connection->heartbeatFailures >= $this->maxHeartbeatFailures) {
                        $this->log(sprintf(
                            "心跳检测失败次数过多，关闭连接 - 客户端IP: %s%s",
                            $connection->getRemoteIp(),
                            isset($connection->userId) ? sprintf(", 用户ID: %d", $connection->userId) : ""
                        ), 'info', true);
                        $connection->close();
                        continue;
                    }
                }
            }
        });
    }

    public function onConnect(TcpConnection $connection)
    {
        $this->log(sprintf("新的连接 - 客户端IP: %s", $connection->getRemoteIp()));
        // 初始化连接属性
        $connection->lastHeartbeatTime = time();
        $connection->heartbeatFailures = 0;
        
        try {
            // 发送连接成功消息
            $connection->send(json_encode([
                'type' => 'connect',
                'status' => 'success',
                'message' => '连接成功',
                'time' => time()
            ]));
            $this->log(sprintf("发送连接成功消息 - 客户端IP: %s", $connection->getRemoteIp()));

            // 立即发送第一个心跳包
            $connection->send(json_encode([
                'type' => 'ping',
                'time' => time()
            ]));
            $this->log(sprintf("发送首次心跳包 - 客户端IP: %s", $connection->getRemoteIp()));
        } catch (\Exception $e) {
            $this->log(sprintf("发送消息失败: %s", $e->getMessage()), 'error');
        }
    }

    public function onMessage(TcpConnection $connection, $data)
    {
        $this->log(sprintf("收到消息 - 客户端IP: %s", $connection->getRemoteIp()));
        $this->log(sprintf("消息内容: %s", $data));
        
        try {
            // 如果是字符串消息，直接回显
            if (is_string($data) && !$this->isJson($data)) {
                $this->log("收到测试消息，直接回显");
                $connection->send($data);
                return;
            }

            $message = json_decode($data, true);
            $this->log("解析后的消息: " . json_encode($message, JSON_UNESCAPED_UNICODE));
            
            // 处理认证请求
            if (isset($message['type']) && $message['type'] === 'auth') {
                $this->log("收到认证请求 - 用户ID: " . $message['userId']);
                $connection->userId = $message['userId'];
                $this->userConnections[$message['userId']] = $connection;
                $connection->lastHeartbeatTime = time(); // 认证时重置心跳时间
                
                // 发送认证成功响应
                $connection->send(json_encode([
                    'type' => 'auth_response',
                    'status' => 'success',
                    'message' => '连接成功'
                ]));
                
                return;
            }
            
            // 处理点赞操作
            if (isset($message['type']) && $message['type'] === 'like') {
                $this->handleLikeMessage($message);
                return;
            }
            
            // 处理心跳响应
            if (isset($message['type']) && $message['type'] === 'pong') {
                $currentTime = time();
                $this->log(sprintf(
                    "收到心跳响应 - 客户端IP: %s, 上次心跳: %s -> 当前: %s",
                    $connection->getRemoteIp(),
                    date('Y-m-d H:i:s', $connection->lastHeartbeatTime),
                    date('Y-m-d H:i:s', $currentTime)
                ), 'info', true);
                $connection->lastHeartbeatTime = $currentTime;
                $connection->heartbeatFailures = 0;
                return;
            }

            // 对于测试页面，不强制要求认证
            if (!isset($connection->userId) && strpos($data, 'test/websocket') === false) {
                $this->log("未认证的消息 - 客户端IP: " . $connection->getRemoteIp());
                $connection->send(json_encode([
                    'type' => 'error',
                    'message' => '请先进行认证'
                ]));
                return;
            }
            
        } catch (\Exception $e) {
            $this->log(sprintf("消息处理错误: %s", $e->getMessage()), 'error');
            $connection->send(json_encode([
                'type' => 'error',
                'code' => 1003,
                'message' => '消息格式错误'
            ]));
        }
    }

    /**
     * 处理点赞消息
     * @param array $message 点赞消息数据
     */
    protected function handleLikeMessage($message)
    {
        try {
            // 验证必要字段
            if (!isset($message['targetId']) || !isset($message['targetType']) || 
                !isset($message['action']) || !isset($message['targetUserId']) || 
                !isset($message['fromUserId'])) {
                throw new \Exception('缺少必要的字段');
            }

            $this->log(sprintf("处理点赞消息: %s", 
                json_encode($message, JSON_UNESCAPED_UNICODE)));

            // 转换ID为整数类型
            $targetId = intval($message['targetId']);
            $targetUserId = intval($message['targetUserId']);
            $fromUserId = intval($message['fromUserId']);

            // 如果是取消点赞，不需要发送通知
            if ($message['action'] === 'unlike' || !$message['shouldNotify']) {
                $this->log("取消点赞或不需要通知，跳过");
                return;
            }

            // 获取点赞者信息
            $fromUser = $this->executeDB(function() use ($fromUserId) {
                return Db::name('user')->where('id', $fromUserId)->find();
            });
            
            if (!$fromUser) {
                throw new \Exception('点赞者信息不存在');
            }
            $this->log(sprintf("获取点赞者信息成功: %s", 
                json_encode($fromUser, JSON_UNESCAPED_UNICODE)));

            // 获取被点赞内容信息
            $targetContent = $this->getTargetContent($message['targetType'], $targetId);
            if (!$targetContent) {
                throw new \Exception('被点赞内容不存在');
            }
            $this->log(sprintf("获取被点赞内容成功: %s", 
                json_encode($targetContent, JSON_UNESCAPED_UNICODE)));

            // 获取未读消息数
            $unreadCount = $this->executeDB(function() use ($targetUserId) {
                return Db::name('notification')
                    ->where('user_id', $targetUserId)
                    ->where('is_read', 0)
                    ->count();
            });

            // 获取总点赞数
            $totalLikes = $this->getLikesCount($message['targetType'], $targetId);

            // 构建通知消息
            $notification = [
                'type' => 'notification',
                'data' => [
                    'type' => 'like',
                    'content' => sprintf('%s点赞了你的%s', 
                        $fromUser['username'], 
                        $this->getTargetTypeText($message['targetType'])),
                    'target_type' => $message['targetType'],
                    'target_id' => $targetId,
                    'is_liked' => true,
                    'total_likes' => $totalLikes,
                    'unread_count' => $unreadCount,
                    'from_user' => [
                        'user_id' => $fromUser['id'],
                        'nickname' => $fromUser['username'],
                        'avatar' => $fromUser['face_url']
                    ],
                    'target_content' => [
                        'id' => $targetContent['id'],
                        'type' => $message['targetType'],
                        'content' => mb_substr($targetContent['content'], 0, 50),
                        'created_at' => $targetContent['create_time']
                    ]
                ]
            ];

            $this->log(sprintf("准备发送通知: %s", 
                json_encode($notification, JSON_UNESCAPED_UNICODE)));

            // 检查目标用户是否在线
            if (!isset($this->userConnections[$targetUserId])) {
                $this->log(sprintf("目标用户不在线，无法发送通知 - 用户ID: %d", $targetUserId));
                return;
            }

            // 发送通知给目标用户
            $this->sendMessageToUser($targetUserId, $notification);
            $this->log("通知发送成功 - 从用户 {$fromUserId} 到用户 {$targetUserId}");
            
        } catch (\Exception $e) {
            $this->log("处理点赞消息错误: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 获取内容的点赞数
     * @param string $type 内容类型
     * @param int $id 内容ID
     * @return int
     */
    protected function getLikesCount($type, $id)
    {
        try {
            return $this->executeDB(function() use ($type, $id) {
                switch ($type) {
                    case 'message':
                        return Db::name('message_like')
                            ->where('message_id', $id)
                            ->count();
                    case 'comment':
                        return Db::name('comment_like')
                            ->where('comment_id', $id)
                            ->count();
                    case 'reply':
                        return Db::name('reply_like')
                            ->where('reply_id', $id)
                            ->count();
                    default:
                        return 0;
                }
            });
        } catch (\Exception $e) {
            $this->log("获取点赞数错误: " . $e->getMessage(), 'error');
            return 0;
        }
    }

    /**
     * 获取目标内容信息
     * @param string $type 内容类型
     * @param string $id 内容ID
     * @return array|null
     */
    protected function getTargetContent($type, $id)
    {
        try {
            return $this->executeDB(function() use ($type, $id) {
                $table = '';
                $fields = [];
                switch ($type) {
                    case 'message':
                        $table = 'message';
                        $fields = ['id', 'content', 'send_timestamp as create_time'];
                        break;
                    case 'comment':
                        $table = 'comment';
                        $fields = ['id', 'content', 'send_timestamp as create_time'];
                        break;
                    case 'reply':
                        $table = 'post';
                        $fields = ['id', 'content', 'send_timestamp as create_time'];
                        break;
                    default:
                        return null;
                }

                $this->log("查询内容 - 表名: {$table}, ID: {$id}, 类型: {$type}");
                
                $content = Db::name($table)
                    ->field($fields)
                    ->where('id', $id)
                    ->find();

                if (!$content) {
                    $this->log("未找到内容 - 表名: {$table}, ID: {$id}");
                    return null;
                }

                return $content;
            });
        } catch (\Exception $e) {
            $this->log("获取内容错误: " . $e->getMessage(), 'error');
            return null;
        }
    }

    /**
     * 获取目标类型的中文描述
     * @param string $type 目标类型
     * @return string
     */
    protected function getTargetTypeText($type)
    {
        switch ($type) {
            case 'message':
                return '树洞';
            case 'comment':
                return '评论';
            case 'reply':
                return '回复';
            default:
                return '内容';
        }
    }

    public function onClose(TcpConnection $connection)
    {
        $this->log("连接关闭 - 客户端IP: " . $connection->getRemoteIp());
        
        // 清理用户连接记录
        if (isset($connection->userId)) {
            unset($this->userConnections[$connection->userId]);
            $this->log("用户连接记录已清理 - 用户ID: " . $connection->userId);
        }
    }

    public function onError(TcpConnection $connection, $code, $msg)
    {
        $this->log("连接错误 - 客户端IP: " . $connection->getRemoteIp() . ", 错误码: {$code}, 错误信息: {$msg}", 'error');
    }

    /**
     * 发送消息给指定用户
     * @param int $userId 目标用户ID
     * @param array $message 消息内容
     */
    protected function sendMessageToUser($userId, $message)
    {
        if (isset($this->userConnections[$userId])) {
            try {
                $this->userConnections[$userId]->send(json_encode($message));
                $this->log("消息发送成功 - 目标用户ID: {$userId}");
            } catch (\Exception $e) {
                $this->log("发送消息失败: " . $e->getMessage(), 'error');
            }
        } else {
            $this->log("目标用户不在线 - 用户ID: {$userId}", 'warning');
        }
    }

    protected function log($message, $level = 'info', $isHeartbeat = false)
    {
        $time = date('Y-m-d H:i:s');
        $logMessage = sprintf("[%s] %s: %s\n", $time, $level, $message);
        
        // 输出到控制台
        echo $logMessage;
        
        // 写入日志文件
        if ($isHeartbeat) {
            // 心跳日志只在调试模式下记录
            if (defined('APP_DEBUG') && APP_DEBUG) {
                file_put_contents($this->logFile, $logMessage, FILE_APPEND);
            }
        } else {
            // 业务日志总是记录
            file_put_contents($this->businessLogFile, $logMessage, FILE_APPEND);
        }
    }

    /**
     * 检查字符串是否为JSON格式
     */
    protected function isJson($string) {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    protected function reconnectDB()
    {
        try {
            Db::disconnect(); // 先断开现有连接
            Db::connect('mysql'); // 重新连接
            $this->log("数据库重连成功");
            return true;
        } catch (\Exception $e) {
            $this->log("数据库重连失败: " . $e->getMessage(), 'error');
            return false;
        }
    }

    protected function executeDB($callback)
    {
        try {
            return $callback();
        } catch (\Exception $e) {
            // 检查是否是连接断开错误
            if (strpos($e->getMessage(), 'MySQL server has gone away') !== false) {
                $this->log("数据库连接断开，尝试重连", 'warning');
                if ($this->reconnectDB()) {
                    // 重连成功，重试操作
                    try {
                        return $callback();
                    } catch (\Exception $retryError) {
                        $this->log("重试操作失败: " . $retryError->getMessage(), 'error');
                        throw $retryError;
                    }
                }
            }
            throw $e;
        }
    }
}