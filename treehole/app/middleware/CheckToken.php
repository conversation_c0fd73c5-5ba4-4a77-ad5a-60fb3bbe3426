<?php
declare (strict_types = 1);

namespace app\middleware;

use app\util\JwtUtil;
use think\facade\Request;
use think\facade\Log;

class CheckToken
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        // 记录请求信息
        Log::info('Token验证 - 请求路径: ' . $request->url());
        
        // 从请求头中获取token (支持多种header名称)
        $token = $request->header('Authorization', '');
        if (empty($token)) {
            $token = $request->header('token', '');
        }
        Log::info('Token验证 - token头: ' . ($token ?: '未设置'));
        
        // 如果没有在header中找到token，则尝试从GET或POST参数中获取
        if (empty($token)) {
            $token = $request->param('token', '');
            $token = $request->param('access_token', $token);
            Log::info('Token验证 - 参数中的token: ' . ($token ?: '未设置'));
        }
        
        // 如果没有提供token
        if (empty($token)) {
            Log::warning('Token验证 - 未提供token');
            return json([
                'code' => 401,
                'msg' => '未授权，请登录'
            ]);
        }
        
        // 处理带有Bearer前缀的token (确保token是字符串)
        if (is_string($token) && strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
            Log::info('Token验证 - 去除Bearer前缀');
        }
        
        // 验证token
        try {
            $userData = JwtUtil::validateToken($token);
            
            if (!$userData) {
                Log::warning('Token验证 - token无效');
                return json([
                    'code' => 401,
                    'msg' => 'token已过期或无效，请重新登录'
                ]);
            }
            
            // 记录验证成功
            Log::info('Token验证 - 成功, 用户数据: ', $userData);
            
            // 确保用户ID是整数类型
            $userId = isset($userData['user_id']) ? intval($userData['user_id']) : 0;
            $openid = isset($userData['openid']) ? (string)$userData['openid'] : '';
            
            Log::info('Token验证 - 处理后的userId: ' . $userId . ', 类型: ' . gettype($userId));
            
            // 将用户ID和openid存储到请求中间件参数中，供后续控制器使用
            $request->middleware('userId', $userId);
            $request->middleware('openid', $openid);
            
            return $next($request);
        } catch (\Exception $e) {
            Log::error('Token验证 - 发生异常: ' . $e->getMessage());
            return json([
                'code' => 401,
                'msg' => 'token验证异常，请重新登录'
            ]);
        }
    }
} 