<?php
declare (strict_types = 1);

namespace app\middleware;

class FilterInjection
{
    public function handle($request, \Closure $next)
    {
        // SQL注入关键词列表 - 只检查明确的SQL注入模式
        $sqlPatterns = [
            '/\bunion\s+select\b/i',
            '/\bselect\s+.*\bfrom\b/i',
            '/\binsert\s+into\b/i',
            '/\bupdate\s+.*\bset\b/i',
            '/\bdelete\s+from\b/i',
            '/\bdrop\s+table\b/i',
            '/\bor\s+1\s*=\s*1\b/i',
            '/\band\s+1\s*=\s*1\b/i',
            '/\'\s*or\s*\'/i',
            '/\'\s*and\s*\'/i',
            '/\binformation_schema\b/i',
            '/\bload_file\s*\(/i',
            '/\boutfile\s*\(/i',
            '/\bexec\s*\(/i',
            '/\bsp_\w+/i',
            '/\bxp_\w+/i',
            '/\bwaitfor\s+delay\b/i',
            '/\bbenchmark\s*\(/i',
            '/\bsleep\s*\(/i'
        ];

        // XSS和危险脚本模式
        $xssPatterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/i',
            '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/i',
            '/javascript\s*:/i',
            '/vbscript\s*:/i',
            '/on\w+\s*=/i', // onclick, onload等事件
            '/data\s*:\s*text\/html/i'
        ];

        // 检查GET参数
        $getParams = $request->get();
        if ($this->checkParams($getParams, $sqlPatterns, $xssPatterns)) {
            \think\facade\Log::warning('安全攻击尝试 - GET参数', [
                'params' => $getParams,
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);
            return json(['code' => 403, 'msg' => '非法请求']);
        }

        // 检查POST参数
        $postParams = $request->post();
        $checkResult = $this->checkParams($postParams, $sqlPatterns, $xssPatterns);
        if ($checkResult) {
            \think\facade\Log::warning('安全攻击尝试 - POST参数', [
                'params' => $postParams,
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'url' => $request->url(),
                'method' => $request->method()
            ]);

            // 在开发环境下提供更详细的错误信息
            if (app()->isDebug()) {
                return json(['code' => 403, 'msg' => '非法请求 - 检测到可疑内容', 'debug' => $postParams]);
            }
            return json(['code' => 403, 'msg' => '非法请求']);
        }

        // 检查路由参数
        $routeParams = $request->route();
        if ($this->checkParams($routeParams, $sqlPatterns, $xssPatterns)) {
            \think\facade\Log::warning('安全攻击尝试 - 路由参数', [
                'params' => $routeParams,
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);
            return json(['code' => 403, 'msg' => '非法请求']);
        }

        return $next($request);
    }

    /**
     * 检查参数是否包含危险模式
     * @param array $params 参数数组
     * @param array $sqlPatterns SQL注入模式数组
     * @param array $xssPatterns XSS攻击模式数组
     * @return bool
     */
    private function checkParams($params, $sqlPatterns, $xssPatterns = [])
    {
        if (!is_array($params)) {
            return false;
        }

        foreach ($params as $key => $value) {
            if (is_string($value)) {
                // 检查SQL注入模式
                foreach ($sqlPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        \think\facade\Log::info('SQL注入模式匹配', [
                            'key' => $key,
                            'value' => $value,
                            'pattern' => $pattern
                        ]);
                        return true;
                    }
                }

                // 检查XSS攻击模式
                foreach ($xssPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        \think\facade\Log::info('XSS模式匹配', [
                            'key' => $key,
                            'value' => $value,
                            'pattern' => $pattern
                        ]);
                        return true;
                    }
                }

                // 检查是否包含多个连续的危险字符（可能是编码攻击）
                if (preg_match('/[\'";]{3,}/', $value)) {
                    \think\facade\Log::info('连续危险字符匹配', [
                        'key' => $key,
                        'value' => $value
                    ]);
                    return true;
                }

                // 检查SQL注释模式（但允许正常的--在文本中）
                if (preg_match('/--\s*[\r\n]/', $value) || preg_match('/\/\*.*?\*\//', $value)) {
                    \think\facade\Log::info('SQL注释模式匹配', [
                        'key' => $key,
                        'value' => $value
                    ]);
                    return true;
                }

                // 检查明显的SQL注入尝试
                if (preg_match('/[\'"][\s]*(\bor\b|\band\b)[\s]*[\'"]?[\s]*[=<>]/', $value)) {
                    \think\facade\Log::info('SQL注入尝试匹配', [
                        'key' => $key,
                        'value' => $value
                    ]);
                    return true;
                }

            } elseif (is_array($value)) {
                // 递归检查数组
                if ($this->checkParams($value, $sqlPatterns, $xssPatterns)) {
                    return true;
                }
            }
        }

        return false;
    }
} 