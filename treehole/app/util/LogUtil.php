<?php
namespace app\util;

use think\facade\Log;

/**
 * 日志工具类 - 结构化日志管理
 */
class LogUtil
{
    /**
     * API调用日志
     */
    public static function api(string $method, string $url, array $params = [], array $response = [], float $duration = 0, string $status = 'success')
    {
        $logData = [
            'type' => 'api_call',
            'method' => $method,
            'url' => $url,
            'params' => $params,
            'response' => $response,
            'duration' => round($duration, 3) . 's',
            'status' => $status,
            'timestamp' => date('Y-m-d H:i:s'),
            'memory' => self::getMemoryUsage()
        ];

        Log::channel('api')->info('API调用', $logData);
    }

    /**
     * 业务操作日志
     */
    public static function business(string $module, string $action, array $data = [], string $userId = '', string $result = 'success')
    {
        $logData = [
            'type' => 'business_operation',
            'module' => $module,
            'action' => $action,
            'user_id' => $userId,
            'data' => $data,
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => request()->ip()
        ];

        Log::channel('business')->info("[$module] $action", $logData);
    }

    /**
     * 数据库操作日志
     */
    public static function database(string $operation, string $table, array $data = [], float $duration = 0, string $sql = '')
    {
        $logData = [
            'type' => 'database_operation',
            'operation' => $operation,
            'table' => $table,
            'data' => $data,
            'duration' => round($duration, 3) . 's',
            'sql' => $sql,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        Log::channel('info')->info("[DB] $operation on $table", $logData);
    }

    /**
     * 文件操作日志
     */
    public static function file(string $operation, string $path, array $details = [], string $result = 'success')
    {
        $logData = [
            'type' => 'file_operation',
            'operation' => $operation,
            'path' => $path,
            'details' => $details,
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        Log::channel('info')->info("[FILE] $operation", $logData);
    }

    /**
     * 图片处理日志
     */
    public static function image(string $action, string $imagePath, array $details = [], string $result = 'success')
    {
        $logData = [
            'type' => 'image_processing',
            'action' => $action,
            'image_path' => $imagePath,
            'details' => $details,
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        Log::channel('info')->info("[IMAGE] $action", $logData);
    }

    /**
     * COS操作日志
     */
    public static function cos(string $action, string $key, array $details = [], string $result = 'success')
    {
        $logData = [
            'type' => 'cos_operation',
            'action' => $action,
            'key' => $key,
            'details' => $details,
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        Log::channel('info')->info("[COS] $action", $logData);
    }

    /**
     * 微信API日志
     */
    public static function wechat(string $api, array $params = [], array $response = [], string $result = 'success')
    {
        $logData = [
            'type' => 'wechat_api',
            'api' => $api,
            'params' => $params,
            'response' => $response,
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        Log::channel('api')->info("[WECHAT] $api", $logData);
    }

    /**
     * 错误日志
     */
    public static function error(string $message, array $context = [], \Throwable $exception = null)
    {
        $logData = [
            'type' => 'error',
            'message' => $message,
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => request()->ip(),
            'url' => request()->url(),
            'method' => request()->method()
        ];

        if ($exception) {
            $logData['exception'] = [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ];
        }

        Log::channel('error')->error($message, $logData);
    }

    /**
     * 警告日志
     */
    public static function warning(string $message, array $context = [])
    {
        $logData = [
            'type' => 'warning',
            'message' => $message,
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => request()->ip(),
            'url' => request()->url()
        ];

        Log::channel('warning')->warning($message, $logData);
    }

    /**
     * 调试日志
     */
    public static function debug(string $message, array $context = [])
    {
        if (!SecretUtil::isLocal()) {
            return; // 生产环境不记录调试日志
        }

        $logData = [
            'type' => 'debug',
            'message' => $message,
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
            'memory' => self::getMemoryUsage()
        ];

        Log::channel('debug')->debug($message, $logData);
    }

    /**
     * 获取内存使用情况
     */
    private static function getMemoryUsage(): string
    {
        $memory = memory_get_usage(true);
        $peak = memory_get_peak_usage(true);
        
        return sprintf(
            'Current: %s, Peak: %s',
            self::formatBytes($memory),
            self::formatBytes($peak)
        );
    }

    /**
     * 格式化字节数
     */
    private static function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 性能监控日志
     */
    public static function performance(string $operation, float $startTime, array $details = [])
    {
        $duration = microtime(true) - $startTime;
        
        $logData = [
            'type' => 'performance',
            'operation' => $operation,
            'duration' => round($duration, 3) . 's',
            'details' => $details,
            'memory' => self::getMemoryUsage(),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        // 超过1秒的操作记录为警告
        if ($duration > 1.0) {
            Log::channel('warning')->warning("[PERFORMANCE] 慢操作: $operation", $logData);
        } else {
            Log::channel('info')->info("[PERFORMANCE] $operation", $logData);
        }
    }
}
