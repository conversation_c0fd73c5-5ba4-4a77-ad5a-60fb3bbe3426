<?php
namespace app\util;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;

class JwtUtil
{
    private static $algo = 'HS256';
    private static $expire = 7200;        // token有效期2小时
    private static $refresh_expire = 604800; // 刷新token有效期7天

    /**
     * 获取JWT密钥
     */
    private static function getKey()
    {
        return SecretUtil::getEncryptionKey('jwt_key');
    }

    /**
     * 生成token
     * @param array $data
     * @return array 返回access_token和refresh_token
     */
    public static function generateToken($data)
    {
        $time = time();
        
        // 生成访问token
        $accessToken = [
            'iat' => $time,
            'exp' => $time + self::$expire,
            'data' => $data,
            'type' => 'access'
        ];

        // 生成刷新token
        $refreshToken = [
            'iat' => $time,
            'exp' => $time + self::$refresh_expire,
            'data' => $data,
            'type' => 'refresh'
        ];

        return [
            'access_token' => JWT::encode($accessToken, self::getKey(), self::$algo),
            'refresh_token' => JWT::encode($refreshToken, self::getKey(), self::$algo),
            'expire_in' => self::$expire
        ];
    }

    /**
     * 验证token
     * @param string $token
     * @return array|false
     */
    public static function validateToken($token)
    {
        try {
            $decoded = JWT::decode($token, new Key(self::getKey(), self::$algo));
            return (array)$decoded->data;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 验证token（新方法名）
     * @param string $token
     * @return array|false
     */
    public static function verifyToken($token)
    {
        try {
            $decoded = JWT::decode($token, new Key(self::getKey(), self::$algo));
            // 递归转换所有对象为数组
            return json_decode(json_encode($decoded), true);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 刷新token
     * @param string $refreshToken
     * @return array|false
     */
    public static function refreshToken($refreshToken)
    {
        try {
            $decoded = JWT::decode($refreshToken, new Key(self::getKey(), self::$algo));
            $decodedArray = (array)$decoded;

            // 验证是否是刷新token
            if (!isset($decodedArray['type']) || $decodedArray['type'] !== 'refresh') {
                return false;
            }

            // 生成新的token对
            return self::generateToken((array)$decoded->data);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 智能验证token，自动尝试刷新
     * @param string $accessToken 访问token
     * @param string $refreshToken 刷新token
     * @return array ['valid' => bool, 'data' => array, 'new_tokens' => array|null]
     */
    public static function smartValidateToken($accessToken, $refreshToken = null)
    {
        try {
            // 首先尝试验证访问token
            $decoded = JWT::decode($accessToken, new Key(self::getKey(), self::$algo));
            return [
                'valid' => true,
                'data' => (array)$decoded->data,
                'new_tokens' => null
            ];
        } catch (ExpiredException $e) {
            // token过期，尝试使用刷新token
            if ($refreshToken) {
                $newTokens = self::refreshToken($refreshToken);
                if ($newTokens) {
                    // 刷新成功，返回新token和用户数据
                    $newDecoded = JWT::decode($newTokens['access_token'], new Key(self::getKey(), self::$algo));
                    return [
                        'valid' => true,
                        'data' => (array)$newDecoded->data,
                        'new_tokens' => $newTokens
                    ];
                }
            }

            return [
                'valid' => false,
                'data' => null,
                'new_tokens' => null,
                'error' => 'token_expired'
            ];
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'data' => null,
                'new_tokens' => null,
                'error' => 'token_invalid'
            ];
        }
    }

    /**
     * 检查token是否即将过期（剩余时间少于30分钟）
     * @param string $token
     * @return bool
     */
    public static function isTokenExpiringSoon($token)
    {
        try {
            $decoded = JWT::decode($token, new Key(self::getKey(), self::$algo));
            $exp = $decoded->exp;
            $now = time();
            $timeLeft = $exp - $now;

            // 如果剩余时间少于30分钟（1800秒），返回true
            return $timeLeft < 1800;
        } catch (\Exception $e) {
            return true; // 如果解析失败，认为需要刷新
        }
    }
}