<?php
namespace app\util;

use think\facade\Config;

/**
 * 密钥统一管理工具类
 */
class SecretUtil
{
    /**
     * 获取微信小程序配置
     * @param string $type main|banxuexing
     * @return array
     */
    public static function getWechatMiniprogram($type = 'main')
    {
        $config = Config::get('secrets.wechat_miniprogram.' . $type);
        return $config ?: [];
    }

    /**
     * 获取微信公众号配置
     * @param string $type main|template
     * @return array
     */
    public static function getWechatOfficial($type = 'main')
    {
        $config = Config::get('secrets.wechat_official.' . $type);
        return $config ?: [];
    }

    /**
     * 获取腾讯云COS配置
     * @return array
     */
    public static function getTencentCos()
    {
        return Config::get('secrets.tencent_cos', []);
    }

    /**
     * 获取加密密钥
     * @param string $type jwt_key|sso_key|crypto_key
     * @return string
     */
    public static function getEncryptionKey($type = 'jwt_key')
    {
        return Config::get('secrets.encryption.' . $type, '');
    }

    /**
     * 获取数据库配置
     * @return array
     * @throws \Exception 当配置不存在时抛出异常
     */
    public static function getDatabase()
    {
        $config = Config::get('secrets.database');

        if (empty($config)) {
            throw new \Exception('数据库配置不存在，请检查 config/secrets.php 文件是否正确配置');
        }

        // 验证必需的配置项
        $required = ['hostname', 'database', 'username', 'password', 'hostport'];
        foreach ($required as $key) {
            if (!isset($config[$key])) {
                throw new \Exception("数据库配置缺少必需项: {$key}");
            }
        }

        return $config;
    }

    /**
     * 获取API配置
     * @return array
     */
    public static function getApi()
    {
        return Config::get('secrets.api', []);
    }

    /**
     * 获取基础URL
     * @return string
     */
    public static function getBaseUrl()
    {
        return Config::get('secrets.api.base_url', '');
    }

    /**
     * 获取Wangz URL
     * @return string
     */
    public static function getWangzUrl()
    {
        return Config::get('secrets.api.wangz', '');
    }



    /**
     * 检查是否为本地环境
     * @return bool
     */
    public static function isLocal()
    {
        $host = $_SERVER['HTTP_HOST'] ?? '';
        return strpos($host, 'localhost') !== false ||
               strpos($host, '127.0.0.1') !== false ||
               strpos($host, '192.168.') !== false;
    }
}
