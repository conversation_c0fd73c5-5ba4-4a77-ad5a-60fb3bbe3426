# 📚 文档索引

## 🔍 快速查找

### 我想了解...

#### 🏗️ 项目整体架构
- [项目结构说明](./PROJECT_STRUCTURE.md) - 了解项目目录结构和文件组织
- [README](./README.md) - 项目概述和快速开始

#### ⚙️ 环境配置
- [环境配置说明](./ENVIRONMENT_CONFIG.md) - 开发和生产环境配置
- [部署指南](./DEPLOYMENT.md) - 生产环境部署流程

#### 💻 开发规范
- [后端开发规范](./BACKEND_DEVELOPMENT.md) - 后端代码规范和架构设计
- [前端开发规范](./FRONTEND_DEVELOPMENT.md) - 前端代码规范和API调用

#### 🔧 工具使用
- [开发工具说明](./DEVELOPMENT_TOOLS.md) - 日志查看、调试工具等
- [日志系统说明](./LOG_SYSTEM.md) - 日志记录、查看和管理

## 📋 按场景分类

### 🚀 新手入门
1. [README](./README.md) - 项目概述
2. [项目结构说明](./PROJECT_STRUCTURE.md) - 了解项目组织
3. [环境配置说明](./ENVIRONMENT_CONFIG.md) - 配置开发环境
4. [前端开发规范](./FRONTEND_DEVELOPMENT.md) - 前端开发入门
5. [后端开发规范](./BACKEND_DEVELOPMENT.md) - 后端开发入门

### 🔧 日常开发
1. [开发工具说明](./DEVELOPMENT_TOOLS.md) - 使用开发工具
2. [日志系统说明](./LOG_SYSTEM.md) - 查看和分析日志
3. [前端开发规范](./FRONTEND_DEVELOPMENT.md) - 前端开发规范
4. [后端开发规范](./BACKEND_DEVELOPMENT.md) - 后端开发规范

### 🚀 部署上线
1. [环境配置说明](./ENVIRONMENT_CONFIG.md) - 环境切换
2. [部署指南](./DEPLOYMENT.md) - 部署流程
3. [日志系统说明](./LOG_SYSTEM.md) - 生产环境日志监控

### 🔍 问题排查
1. [日志系统说明](./LOG_SYSTEM.md) - 查看错误日志
2. [开发工具说明](./DEVELOPMENT_TOOLS.md) - 使用调试工具
3. [部署指南](./DEPLOYMENT.md) - 部署问题排查

## 📖 文档详情

### [README.md](./README.md)
- **用途**：项目总览和快速开始指南
- **适合**：新接触项目的开发者
- **内容**：项目介绍、技术栈、快速开始、重要链接

### [PROJECT_STRUCTURE.md](./PROJECT_STRUCTURE.md)
- **用途**：详细的项目结构说明
- **适合**：需要了解项目组织的开发者
- **内容**：目录结构、文件说明、命名规范

### [ENVIRONMENT_CONFIG.md](./ENVIRONMENT_CONFIG.md)
- **用途**：环境配置和切换说明
- **适合**：需要配置环境的开发者
- **内容**：配置文件、环境切换、密码管理

### [BACKEND_DEVELOPMENT.md](./BACKEND_DEVELOPMENT.md)
- **用途**：后端开发规范和最佳实践
- **适合**：后端开发者
- **内容**：架构设计、代码规范、安全规范、性能优化

### [FRONTEND_DEVELOPMENT.md](./FRONTEND_DEVELOPMENT.md)
- **用途**：前端开发规范和最佳实践
- **适合**：前端开发者
- **内容**：项目架构、网络请求、Token管理、样式规范

### [LOG_SYSTEM.md](./LOG_SYSTEM.md)
- **用途**：日志系统的使用说明
- **适合**：所有开发者
- **内容**：日志分类、查看方法、工具使用

### [DEVELOPMENT_TOOLS.md](./DEVELOPMENT_TOOLS.md)
- **用途**：开发工具和脚本的使用说明
- **适合**：所有开发者
- **内容**：日志查看工具、命令行工具、调试技巧

### [DEPLOYMENT.md](./DEPLOYMENT.md)
- **用途**：生产环境部署指南
- **适合**：运维人员和项目负责人
- **内容**：部署流程、服务器配置、SSL证书、监控维护

## 🔗 外部链接

### 技术文档
- [ThinkPHP 8.0 官方文档](https://www.kancloud.cn/manual/thinkphp8_0)
- [微信小程序官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [腾讯云COS文档](https://cloud.tencent.com/document/product/436)

### 开发工具
- [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- [Postman](https://www.postman.com/)
- [phpMyAdmin](https://www.phpmyadmin.net/)

## 📝 文档维护

### 更新记录
- **2025-01-08**：创建完整的文档体系
- **2025-01-08**：添加日志系统说明
- **2025-01-08**：完善开发规范文档

### 维护原则
1. **及时更新**：代码变更时同步更新文档
2. **简洁明了**：文档内容要简洁易懂
3. **实用性强**：重点关注实际开发中的需求
4. **分类清晰**：按照使用场景合理分类

### 贡献指南
如需更新文档，请：
1. 确保内容准确性
2. 保持格式一致性
3. 添加必要的示例代码
4. 更新相关的索引和链接

---

**提示**：建议将此文档加入书签，方便快速查找所需信息。
