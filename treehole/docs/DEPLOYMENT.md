# 部署指南

## 📋 环境要求

- PHP 8.0+
- MySQL 5.7+
- Apache/Nginx
- Composer
- 腾讯云COS账号

## 🚀 部署步骤

### 1. 克隆代码
```bash
git clone https://github.com/your-username/treehole.git
cd treehole
```

### 2. 安装依赖
```bash
# 生产环境
composer install --no-dev --optimize-autoloader

# 开发环境
composer install
```

### 3. 配置密钥文件
```bash
# 复制配置模板
cp config/secrets.php.example config/secrets.php

# 编辑配置文件，填入真实的密钥信息
nano config/secrets.php
```

### 4. 配置数据库
```sql
-- 创建数据库
CREATE DATABASE treehole CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据库结构
mysql -u root -p treehole < mysql/database.sql
```

### 5. 设置文件权限
```bash
# 设置运行时目录权限
chmod -R 755 runtime/
chmod -R 755 public/

# 设置think命令执行权限
chmod +x think
```

### 6. 配置Web服务器

#### Apache配置
确保 `.htaccess` 文件存在于 `public` 目录，并启用 `mod_rewrite`。

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/treehole/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## 🔒 安全配置

### 1. 密钥管理
- **绝对不要**将 `config/secrets.php` 提交到代码仓库
- 定期更换密钥
- 使用强密码和复杂的加密密钥

### 2. 文件权限
```bash
# 配置文件只读
chmod 600 config/secrets.php

# 运行时目录可写
chmod 755 runtime/
```

### 3. 环境变量
生产环境建议使用环境变量管理敏感信息：
```bash
export DB_PASSWORD="your_secure_password"
export JWT_SECRET="your_jwt_secret"
```

## 📝 配置说明

### secrets.php 配置项

#### 微信小程序
```php
'wechat_miniprogram' => [
    'main' => [
        'appid' => 'wx13d6e0dee303467f',    // 主小程序AppID
        'secret' => 'your_secret_here',     // 主小程序Secret
    ],
]
```

#### 数据库
```php
'database' => [
    'hostname' => $isLocal ? 'localhost' : 'your_tencent_cloud_db_host',
    'database' => $isLocal ? 'treehole' : 'your_tencent_cloud_db_name',
    'username' => $isLocal ? 'root' : 'your_tencent_cloud_db_user',
    'password' => $isLocal ? 'your_local_password' : 'your_tencent_cloud_password',
    'hostport' => $isLocal ? '8889' : '3306',
]
```

#### Redis配置
```php
'redis' => [
    'host' => $isLocal ? '127.0.0.1' : 'your_tencent_cloud_redis_host',
    'port' => 6379,
    'password' => $isLocal ? '' : 'your_tencent_cloud_redis_password',
    'database' => $isLocal ? 1 : 0,
]
```

#### 腾讯云COS
```php
'tencent_cos' => [
    'region' => 'ap-beijing',              // 地域
    'secret_id' => 'your_secret_id',       // SecretId
    'secret_key' => 'your_secret_key',     // SecretKey
]
```

## 🔧 环境检测

系统会自动检测运行环境：
- **开发环境**: 访问域名包含 `localhost`、`127.0.0.1`、`192.168.*`
- **生产环境**: 其他所有域名

## 📊 监控和日志

### 日志位置
- 应用日志: `runtime/log/`
- 错误日志: `runtime/log/single.log`

### 性能监控
- 开发环境: 开启SQL监听和调试模式
- 生产环境: 关闭调试，开启缓存优化

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `secrets.php` 中的数据库配置
   - 确认数据库服务正在运行

2. **文件上传失败**
   - 检查COS配置
   - 确认目录权限

3. **Token验证失败**
   - 检查JWT密钥配置
   - 确认时间同步

### 调试模式
开发环境会自动开启调试模式，生产环境请确保关闭。

## 📞 技术支持

如遇到部署问题，请检查：
1. PHP版本和扩展
2. 数据库连接
3. 文件权限
4. 配置文件格式
