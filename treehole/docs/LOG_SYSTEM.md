# 树洞项目日志系统说明

## 📋 概述

本项目采用分类日志系统，所有日志永久保存，便于问题追踪和系统监控。

## 🗂️ 日志分类

### 1. 错误日志 (error.log)
- **用途**：记录系统错误和异常
- **保存策略**：永久保存
- **重要性**：⭐⭐⭐⭐⭐

### 2. 警告日志 (warning.log)
- **用途**：记录系统警告信息
- **保存策略**：永久保存
- **重要性**：⭐⭐⭐⭐

### 3. 信息日志 (info.log)
- **用途**：记录业务操作、数据库查询、COS操作等
- **保存策略**：永久保存
- **重要性**：⭐⭐⭐

### 4. API日志 (api.log)
- **用途**：记录API调用详情（JSON格式）
- **保存策略**：永久保存
- **重要性**：⭐⭐⭐

### 5. 业务日志 (business.log)
- **用途**：记录重要业务操作
- **保存策略**：永久保存
- **重要性**：⭐⭐⭐⭐

### 6. 调试日志 (debug.log)
- **用途**：开发调试信息（仅开发环境）
- **保存策略**：永久保存
- **重要性**：⭐⭐

## 🔧 配置文件

### 主要配置
- **日志配置**：`config/log.php`
- **查看器配置**：`config/log_viewer.php`
- **密码配置**：`config/secrets.php`

### 密码设置
```php
// config/secrets.php
'log_viewer' => [
    'password' => $isLocal ? 'dev_log_2024' : 'treehole_log_admin_2024',
    'enable_auth' => !$isLocal, // 本地环境不需要密码
]
```

## 🔍 查看方式

### 1. Web界面
- **本地环境**：`http://localhost:80/log_viewer.html`
- **生产环境**：`https://www.bjgaoxiaoshequ.store/log_viewer.html`
- **密码**：`treehole_log_admin_2024`

### 2. 命令行工具（推荐）
```bash
# 交互式日志查看器
./view_logs.sh

# 直接查看
tail -50 runtime/log/info.log
tail -20 runtime/log/error.log

# 搜索日志
grep "SimpleDraftGenerator" runtime/log/info.log
grep "error" runtime/log/error.log

# 实时监控
tail -f runtime/log/*.log
```

## 📊 日志工具类使用

### LogUtil 类方法
```php
// API调用日志
LogUtil::api('POST', '/api/endpoint', $params, $response, $duration);

// 业务操作日志
LogUtil::business('模块名', '操作名', $data, $userId);

// 数据库操作日志
LogUtil::database('SELECT', 'table_name', $conditions, $duration, $sql);

// COS操作日志
LogUtil::cos('upload', $key, $details);

// 错误日志
LogUtil::error('错误信息', $context, $exception);

// 警告日志
LogUtil::warning('警告信息', $context);

// 调试日志（仅开发环境）
LogUtil::debug('调试信息', $context);
```

## 🔐 安全特性

### 访问控制
- **本地环境**：无需密码验证
- **生产环境**：需要密码验证
- **IP白名单**：可配置允许访问的IP地址

### 密码管理
- 密码存储在 `config/secrets.php` 中
- 支持开发和生产环境不同密码
- 后端验证，安全可靠

## 📁 文件位置

```
treehole/
├── runtime/log/           # 日志文件目录
│   ├── error.log         # 错误日志
│   ├── warning.log       # 警告日志
│   ├── info.log          # 信息日志
│   ├── api.log           # API日志
│   ├── business.log      # 业务日志
│   └── debug.log         # 调试日志
├── public/
│   └── log_viewer.html   # Web日志查看器
├── view_logs.sh          # 命令行日志查看脚本
└── config/
    ├── log.php           # 日志配置
    ├── log_viewer.php    # 查看器配置
    └── secrets.php       # 密码配置
```

## 🎯 最佳实践

### 1. 日志记录
- 使用 `LogUtil` 类记录结构化日志
- 重要操作必须记录业务日志
- 错误信息包含足够的上下文
- API调用记录请求和响应详情

### 2. 日志查看
- 优先使用 `./view_logs.sh` 脚本
- 生产环境使用Web界面查看
- 定期检查错误日志
- 使用搜索功能快速定位问题

### 3. 安全注意
- 定期更换日志查看器密码
- 不要在日志中记录敏感信息（密码、密钥等）
- 生产环境限制访问IP（如需要）

## 🚀 快速开始

1. **查看最新日志**：
   ```bash
   ./view_logs.sh
   ```

2. **搜索特定内容**：
   ```bash
   grep "SimpleDraftGenerator" runtime/log/info.log
   ```

3. **Web界面查看**：
   访问 `log_viewer.html` 并输入密码

4. **实时监控**：
   ```bash
   tail -f runtime/log/*.log
   ```

---

**注意**：所有日志文件永久保存，请定期备份重要日志数据。
