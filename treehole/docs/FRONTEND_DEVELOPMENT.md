# 前端开发规范

## 📋 概述

本文档规定了大学城树洞微信小程序前端开发的代码规范、架构设计和最佳实践。

## 🏗️ 项目架构

### 目录结构
```
miniprogram1/
├── app.js              # 小程序入口，环境配置
├── app.json            # 小程序配置
├── app.wxss            # 全局样式
├── pages/              # 页面目录
├── components/         # 自定义组件
├── utils/              # 工具类
└── images/             # 静态资源
```

### 核心工具类
- **request.js** - 统一网络请求
- **tokenManager.js** - Token管理
- **loginManager.js** - 登录状态管理
- **debugUtil.js** - 调试工具

## 🌐 环境配置

### app.js 环境切换
```javascript
// 环境配置 - 注释掉不用的环境
// const baseUrl = 'http://localhost:80/index.php';  // 开发环境
const baseUrl = 'https://www.bjgaoxiaoshequ.store';   // 生产环境

// 自动配置其他参数
const isLocalEnvironment = baseUrl.includes('localhost');
const defaultFaceUrl = isLocalEnvironment ? 
  'http://localhost:80/images/weixiao.png' : 
  'https://www.bjgaoxiaoshequ.store/images/weixiao.png';

App({
  globalData: {
    baseUrl: baseUrl,
    defaultFaceUrl: defaultFaceUrl,
    isLocalEnvironment: isLocalEnvironment,
    userInfo: null,
    token: null
  }
});
```

### 环境切换步骤
1. **开发环境**：取消注释第一行，注释第二行
2. **生产环境**：注释第一行，取消注释第二行
3. **重新编译**：保存后微信开发者工具会自动重新编译

## 📡 网络请求规范

### 使用统一的request工具
```javascript
// utils/request.js
const app = getApp();

function request(url, data = {}, method = 'POST') {
  return new Promise((resolve, reject) => {
    // 显示加载提示
    wx.showLoading({ title: '加载中...' });
    
    // 获取Token
    const token = wx.getStorageSync('token');
    
    wx.request({
      url: `${app.globalData.baseUrl}${url}`,
      data: data,
      method: method,
      header: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'token': token || ''
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200) {
          // 处理业务逻辑错误
          if (res.data.code === 401) {
            // Token过期，跳转登录
            wx.navigateTo({ url: '/pages/login/login' });
            reject(new Error('登录已过期'));
            return;
          }
          
          resolve(res.data);
        } else {
          reject(new Error(`网络错误: ${res.statusCode}`));
        }
      },
      fail: (error) => {
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        reject(error);
      }
    });
  });
}

module.exports = { request };
```

### 页面中使用示例
```javascript
const { request } = require('../../utils/request');

Page({
  data: {
    userList: []
  },
  
  async loadUserList() {
    try {
      const result = await request('/User/getUserList', {
        page: 1,
        limit: 20
      });
      
      if (result.code === 200) {
        this.setData({
          userList: result.data.list
        });
      } else {
        wx.showToast({
          title: result.msg || '获取数据失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
    }
  }
});
```

## 🔐 Token管理

### tokenManager.js
```javascript
const { request } = require('./request');

class TokenManager {
  // 获取Token
  static getToken() {
    return wx.getStorageSync('token') || null;
  }
  
  // 设置Token
  static setToken(token) {
    wx.setStorageSync('token', token);
  }
  
  // 清除Token
  static clearToken() {
    wx.removeStorageSync('token');
  }
  
  // 检查Token是否有效
  static async validateToken() {
    const token = this.getToken();
    if (!token) return false;
    
    try {
      const result = await request('/User/validateToken');
      return result.code === 200;
    } catch (error) {
      return false;
    }
  }
  
  // 刷新Token
  static async refreshToken() {
    try {
      const result = await request('/User/refreshToken');
      if (result.code === 200) {
        this.setToken(result.data.token);
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }
}

module.exports = TokenManager;
```

## 👤 登录管理

### loginManager.js
```javascript
const TokenManager = require('./tokenManager');

class LoginManager {
  // 检查登录状态
  static async checkLoginStatus() {
    const token = TokenManager.getToken();
    if (!token) return false;
    
    return await TokenManager.validateToken();
  }
  
  // 微信登录
  static async wechatLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: async (res) => {
          if (res.code) {
            try {
              const result = await request('/User/wechatLogin', {
                code: res.code
              });
              
              if (result.code === 200) {
                TokenManager.setToken(result.data.token);
                
                // 更新全局用户信息
                const app = getApp();
                app.globalData.userInfo = result.data.userInfo;
                app.globalData.token = result.data.token;
                
                resolve(result.data);
              } else {
                reject(new Error(result.msg));
              }
            } catch (error) {
              reject(error);
            }
          } else {
            reject(new Error('获取微信登录码失败'));
          }
        },
        fail: reject
      });
    });
  }
  
  // 退出登录
  static logout() {
    TokenManager.clearToken();
    
    // 清除全局用户信息
    const app = getApp();
    app.globalData.userInfo = null;
    app.globalData.token = null;
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    });
  }
}

module.exports = LoginManager;
```

## 🎨 样式规范

### 全局样式 (app.wxss)
```css
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 通用类 */
.container {
  padding: 20rpx;
}

.card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background: #007aff;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx;
  text-align: center;
}

.text-center { text-align: center; }
.text-gray { color: #666; }
.text-small { font-size: 24rpx; }
```

### 页面样式规范
```css
/* 页面特定样式 */
.user-list-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eee;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 24rpx;
  color: #999;
}
```

## 📱 页面开发规范

### 页面结构
```javascript
// pages/user/user.js
const { request } = require('../../utils/request');
const LoginManager = require('../../utils/loginManager');

Page({
  data: {
    userInfo: null,
    loading: false
  },
  
  onLoad(options) {
    this.checkLogin();
    this.loadUserInfo();
  },
  
  onShow() {
    // 页面显示时的逻辑
  },
  
  async checkLogin() {
    const isLoggedIn = await LoginManager.checkLoginStatus();
    if (!isLoggedIn) {
      wx.navigateTo({
        url: '/pages/login/login'
      });
    }
  },
  
  async loadUserInfo() {
    this.setData({ loading: true });
    
    try {
      const result = await request('/User/getUserInfo');
      if (result.code === 200) {
        this.setData({
          userInfo: result.data
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },
  
  onUserTap(e) {
    const userId = e.currentTarget.dataset.userId;
    wx.navigateTo({
      url: `/pages/userDetail/userDetail?userId=${userId}`
    });
  }
});
```

## 🔧 调试工具

### debugUtil.js
```javascript
class DebugUtil {
  static log(message, data = null) {
    if (getApp().globalData.isLocalEnvironment) {
      console.log(`[DEBUG] ${message}`, data);
    }
  }
  
  static error(message, error = null) {
    if (getApp().globalData.isLocalEnvironment) {
      console.error(`[ERROR] ${message}`, error);
    }
  }
  
  static showDebugInfo(title, data) {
    if (getApp().globalData.isLocalEnvironment) {
      wx.showModal({
        title: `调试信息: ${title}`,
        content: JSON.stringify(data, null, 2),
        showCancel: false
      });
    }
  }
}

module.exports = DebugUtil;
```

## 📝 代码规范

### 命名规范
```javascript
// ✅ 变量名：小驼峰
const userName = 'test';
const userList = [];
const isLoading = false;

// ✅ 函数名：小驼峰
function getUserInfo() {}
function handleUserTap() {}

// ✅ 常量：大写+下划线
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_COUNT = 3;

// ✅ 页面数据：小驼峰
data: {
  userInfo: null,
  messageList: [],
  currentPage: 1
}
```

### 注释规范
```javascript
/**
 * 获取用户信息
 * @param {number} userId 用户ID
 * @returns {Promise} 返回用户信息
 */
async function getUserInfo(userId) {
  // 实现逻辑
}

// 页面事件处理
Page({
  /**
   * 页面加载完成
   */
  onLoad(options) {
    // 初始化逻辑
  },
  
  /**
   * 用户点击事件
   * @param {Object} e 事件对象
   */
  onUserTap(e) {
    // 处理点击
  }
});
```

## 🚀 性能优化

### 1. 图片优化
```javascript
// 使用适当的图片格式和大小
<image 
  src="{{imageUrl}}" 
  mode="aspectFill"
  lazy-load="{{true}}"
  webp="{{true}}"
/>
```

### 2. 数据加载优化
```javascript
// 分页加载
async loadMoreData() {
  if (this.data.loading || !this.data.hasMore) return;
  
  this.setData({ loading: true });
  
  try {
    const result = await request('/Message/getList', {
      page: this.data.currentPage + 1,
      limit: 20
    });
    
    if (result.code === 200) {
      this.setData({
        messageList: [...this.data.messageList, ...result.data.list],
        currentPage: this.data.currentPage + 1,
        hasMore: result.data.list.length === 20
      });
    }
  } finally {
    this.setData({ loading: false });
  }
}
```

### 3. 缓存策略
```javascript
// 缓存常用数据
const CACHE_KEY = 'user_info_cache';
const CACHE_TIME = 5 * 60 * 1000; // 5分钟

async function getCachedUserInfo() {
  const cached = wx.getStorageSync(CACHE_KEY);
  if (cached && (Date.now() - cached.timestamp < CACHE_TIME)) {
    return cached.data;
  }
  
  // 重新获取数据
  const result = await request('/User/getUserInfo');
  if (result.code === 200) {
    wx.setStorageSync(CACHE_KEY, {
      data: result.data,
      timestamp: Date.now()
    });
    return result.data;
  }
  
  return null;
}
```

## 📚 相关文档

- [后端开发规范](./BACKEND_DEVELOPMENT.md)
- [项目结构说明](./PROJECT_STRUCTURE.md)
- [环境配置说明](./ENVIRONMENT_CONFIG.md)

---

**重要**：前端开发时请严格遵循以上规范，确保代码质量和用户体验。
