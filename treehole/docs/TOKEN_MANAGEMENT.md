# Token自动管理系统

## 📋 概述

本系统实现了完整的JWT token自动管理功能，包括：
- 自动检测token过期
- 自动刷新token
- 全局token管理
- 智能重试机制

## 🔧 后端实现

### 1. JwtUtil 增强功能

#### 智能验证token
```php
$result = JwtUtil::smartValidateToken($accessToken, $refreshToken);
// 返回: ['valid' => bool, 'data' => array, 'new_tokens' => array|null]
```

#### 检查token即将过期
```php
$expiringSoon = JwtUtil::isTokenExpiringSoon($token);
// 返回: bool (剩余时间少于30分钟返回true)
```

### 2. 核心功能

所有token管理功能都集中在 `JwtUtil.php` 中，无需额外的API接口。前端通过 `tokenManager.js` 自动处理所有token相关逻辑。

### 3. 控制器中使用

在控制器中直接使用 `JwtUtil` 进行token验证：
```php
class YourController extends BaseController
{
    public function someAction(Request $request)
    {
        // 获取并验证token
        $token = $request->header('token');
        $userData = JwtUtil::validateToken($token);

        if (!$userData) {
            return json(['code' => 401, 'msg' => 'Token无效或已过期']);
        }

        // 使用用户数据
        $userId = $userData['user_id'];

        return json(['code' => 200, 'data' => 'success']);
    }
}
```

## 📱 前端实现

### 1. TokenManager 工具类

#### 基本使用
```javascript
const tokenManager = require('./utils/tokenManager.js');

// 发送请求（自动处理token）
tokenManager.request({
  url: '/api/someEndpoint',
  data: { key: 'value' }
}).then(res => {
  console.log('请求成功:', res.data);
}).catch(err => {
  console.error('请求失败:', err);
});
```

#### 手动刷新token
```javascript
tokenManager.refreshTokens().then(() => {
  console.log('Token刷新成功');
}).catch(() => {
  console.log('Token刷新失败，需要重新登录');
});
```

### 2. API工具增强

现在的 `api.js` 已经集成了token管理：
```javascript
const api = require('./utils/api.js');

// 自动处理token的请求
api.post('/message/list', { page: 1 })
  .then(data => {
    console.log('消息列表:', data);
  })
  .catch(err => {
    console.error('请求失败:', err);
  });
```

## 🚀 工作流程

### 1. 正常请求流程
```
前端发送请求 → 携带access_token → 后端验证通过 → 返回数据
```

### 2. Token过期处理流程
```
前端发送请求 → access_token过期 → 后端返回401错误 → 
前端自动使用refresh_token刷新 → 获取新token → 重试原请求 → 成功
```

### 3. Refresh Token过期处理
```
前端尝试刷新token → refresh_token也过期 → 清除本地token → 
跳转登录页 → 用户重新登录
```

## ⚙️ 配置说明

### 1. Token有效期配置
```php
// JwtUtil.php
private static $expire = 7200;        // access_token有效期2小时
private static $refresh_expire = 604800; // refresh_token有效期7天
```

### 2. 自动检查间隔
```javascript
// tokenManager.js
// 每5分钟检查一次token过期时间
setInterval(() => {
  tokenManager.checkTokenExpiration();
}, 5 * 60 * 1000);
```

### 3. 预刷新时机
- 当token剩余时间少于30分钟时自动预刷新
- 页面启动时检查token状态
- 请求失败时自动重试刷新

## 🛡️ 安全特性

### 1. 双token机制
- **Access Token**: 短期有效，用于API访问
- **Refresh Token**: 长期有效，仅用于刷新access token

### 2. 自动过期处理
- 检测到token过期自动尝试刷新
- 刷新失败自动跳转登录页
- 避免用户感知到token过期

### 3. 防重复刷新
- 同时多个请求时只刷新一次token
- 使用Promise机制避免并发刷新

## 📝 使用建议

### 1. 控制器开发
- 直接使用 `JwtUtil::validateToken()` 验证token
- 使用 `JwtUtil::smartValidateToken()` 进行智能验证（支持自动刷新）
- 返回标准的JSON响应

### 2. 前端开发
- 统一使用 `api.js` 或 `tokenManager.request()` 发送请求
- 不要直接使用 `wx.request()`
- 让系统自动处理token相关逻辑

### 3. 错误处理
- 监听token错误事件
- 提供用户友好的错误提示
- 自动跳转到合适的页面

## 🔍 调试功能

### 1. 控制台日志
前端会输出详细的token管理日志，便于调试

### 2. 手动测试
可以在控制器中直接调用 `JwtUtil` 的各种方法进行测试

## 📞 故障排除

### 1. Token刷新失败
- 检查refresh_token是否有效
- 确认后端接口是否正常
- 查看网络连接状态

### 2. 自动跳转登录
- 确认是否refresh_token过期
- 检查登录页面路径配置
- 验证用户数据清除逻辑

### 3. 重复登录提示
- 检查token存储是否正常
- 确认token验证逻辑
- 查看并发请求处理
