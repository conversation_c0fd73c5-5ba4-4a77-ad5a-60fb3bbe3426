# 开发工具说明

## 📋 概述

本文档介绍了大学城树洞项目开发过程中使用的各种工具和脚本，帮助开发者提高开发效率。

## 🔍 日志查看工具

### 1. 交互式日志查看器 (view_logs.sh)

#### 功能特点
- 🎨 彩色显示不同级别的日志
- 📋 列出所有日志文件
- 🔍 搜索日志内容
- 📊 显示日志统计信息
- 🔄 实时监控日志

#### 使用方法
```bash
# 进入项目目录
cd /Users/<USER>/Documents/chengxu/jingyuxiaoyuan/treehole

# 运行脚本
./view_logs.sh
```

#### 菜单选项
```
1. 📋 查看日志文件列表
2. 📄 查看信息日志 (info.log)
3. ❌ 查看错误日志 (error.log)
4. ⚠️  查看警告日志 (warning.log)
5. 🔧 查看调试日志 (debug.log)
6. 🌐 查看API日志 (api.log)
7. 💼 查看业务日志 (business.log)
8. 🔍 搜索日志内容
9. 📊 查看日志统计
10. 🔄 实时监控日志
```

### 2. Web日志查看器

#### 访问地址
- **本地环境**：`http://localhost:80/log_viewer.html`
- **生产环境**：`https://www.bjgaoxiaoshequ.store/log_viewer.html`

#### 功能特点
- 🌐 Web界面，无需命令行
- 🔐 生产环境密码保护
- 📁 文件列表浏览
- 🔍 关键词搜索
- 📊 统计信息展示

#### 使用说明
1. **本地环境**：直接访问，无需密码
2. **生产环境**：需要输入密码 `treehole_log_admin_2024`

## 🛠️ 命令行工具

### 1. 常用日志查看命令

#### 查看最新日志
```bash
# 查看最新50行信息日志
tail -50 runtime/log/info.log

# 查看最新20行错误日志
tail -20 runtime/log/error.log

# 查看最新30行警告日志
tail -30 runtime/log/warning.log
```

#### 实时监控日志
```bash
# 监控所有日志文件
tail -f runtime/log/*.log

# 监控特定日志文件
tail -f runtime/log/info.log

# 监控错误日志
tail -f runtime/log/error.log
```

#### 搜索日志内容
```bash
# 搜索SimpleDraftGenerator相关日志
grep "SimpleDraftGenerator" runtime/log/info.log

# 搜索COS相关日志
grep "COS" runtime/log/info.log

# 搜索错误信息
grep -i "error" runtime/log/error.log

# 搜索最近的API调用
grep "API" runtime/log/api.log | tail -10

# 搜索特定时间的日志
grep "2025-01-08" runtime/log/info.log
```

#### 日志统计
```bash
# 查看日志文件大小
ls -lh runtime/log/*.log

# 统计日志行数
wc -l runtime/log/*.log

# 统计错误数量
grep -c "error" runtime/log/error.log

# 统计今天的日志
grep "$(date +%Y-%m-%d)" runtime/log/info.log | wc -l
```

### 2. 系统监控命令

#### 进程监控
```bash
# 查看PHP进程
ps aux | grep php

# 查看Apache进程
ps aux | grep apache

# 查看MySQL进程
ps aux | grep mysql
```

#### 资源使用
```bash
# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看CPU使用
top

# 查看网络连接
netstat -tulpn
```

## 🔧 开发辅助工具

### 1. API测试工具

#### 使用curl测试接口
```bash
# 测试用户登录接口
curl -X POST \
  http://localhost:80/index.php/User/login \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'username=test&password=123456'

# 测试带Token的接口
curl -X POST \
  http://localhost:80/index.php/User/getUserInfo \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -H 'token: your_jwt_token' \
  -d 'user_id=1'
```

#### Postman集合
创建Postman集合，包含常用接口：
- 用户登录
- 获取用户信息
- 消息列表
- 图片上传
- 草稿生成

### 2. 数据库工具

#### MySQL命令行操作
```bash
# 连接数据库
mysql -u root -p treehole

# 查看表结构
DESCRIBE user;
DESCRIBE message;

# 查看数据
SELECT * FROM user LIMIT 10;
SELECT * FROM message ORDER BY id DESC LIMIT 10;

# 统计数据
SELECT COUNT(*) FROM user;
SELECT COUNT(*) FROM message WHERE choose < 100;
```

#### 数据备份和恢复
```bash
# 备份数据库
mysqldump -u root -p treehole > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
mysql -u root -p treehole < backup_20250108_120000.sql

# 备份特定表
mysqldump -u root -p treehole user message > tables_backup.sql
```

### 3. 文件操作工具

#### 查找文件
```bash
# 查找PHP文件
find . -name "*.php" -type f

# 查找包含特定内容的文件
grep -r "SimpleDraftGenerator" app/

# 查找最近修改的文件
find . -name "*.php" -mtime -1

# 查找大文件
find . -size +10M -type f
```

#### 文件权限管理
```bash
# 设置目录权限
chmod -R 755 treehole/

# 设置运行时目录权限
chmod -R 777 treehole/runtime/

# 设置脚本执行权限
chmod +x view_logs.sh
```

## 📊 性能分析工具

### 1. 日志分析

#### 分析API响应时间
```bash
# 从API日志中提取响应时间
grep "duration" runtime/log/api.log | tail -20

# 统计慢查询
grep "duration.*[5-9]\." runtime/log/info.log
```

#### 分析错误频率
```bash
# 统计每小时的错误数量
grep "$(date +%Y-%m-%d)" runtime/log/error.log | cut -d' ' -f2 | cut -d':' -f1 | sort | uniq -c

# 分析最常见的错误
grep "error" runtime/log/error.log | sort | uniq -c | sort -nr | head -10
```

### 2. 系统性能监控

#### 实时监控脚本
```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

echo "=== 系统监控 $(date) ==="
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

echo "内存使用率:"
free | grep Mem | awk '{printf "%.2f%%\n", $3/$2 * 100.0}'

echo "磁盘使用率:"
df -h | grep -vE '^Filesystem|tmpfs|cdrom' | awk '{print $5 " " $1}'

echo "最新错误日志:"
tail -5 runtime/log/error.log

echo "========================"
```

## 🎯 调试技巧

### 1. 前端调试

#### 微信开发者工具
- 使用控制台查看日志
- 网络面板监控API请求
- 存储面板查看本地数据
- 模拟器测试不同设备

#### 真机调试
```javascript
// 在代码中添加调试信息
console.log('API请求:', url, data);
console.log('响应数据:', result);

// 使用wx.showModal显示调试信息
wx.showModal({
  title: '调试信息',
  content: JSON.stringify(data),
  showCancel: false
});
```

### 2. 后端调试

#### 使用LogUtil记录调试信息
```php
// 记录调试信息
LogUtil::debug('用户登录', ['user_id' => $userId, 'ip' => $ip]);

// 记录性能信息
$startTime = microtime(true);
// ... 业务逻辑
LogUtil::performance('getUserList', $startTime, ['count' => count($users)]);
```

#### 使用var_dump调试
```php
// 临时调试（记得删除）
var_dump($data);
exit;

// 写入日志文件
file_put_contents('debug.log', print_r($data, true), FILE_APPEND);
```

## 📚 相关文档

- [日志系统说明](./LOG_SYSTEM.md)
- [后端开发规范](./BACKEND_DEVELOPMENT.md)
- [前端开发规范](./FRONTEND_DEVELOPMENT.md)
- [环境配置说明](./ENVIRONMENT_CONFIG.md)

---

**提示**：善用这些工具可以大大提高开发效率和问题排查能力。
