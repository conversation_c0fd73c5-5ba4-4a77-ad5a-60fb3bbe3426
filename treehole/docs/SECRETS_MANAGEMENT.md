# 密钥统一管理指南

## 📋 概述

本项目已实现密钥统一管理，所有敏感信息都集中在配置文件中，支持开发环境和生产环境自动切换。

## 🔧 配置文件结构

### 后端配置
- **主配置文件**: `treehole/config/secrets.php`
- **工具类**: `treehole/app/util/SecretUtil.php`

### 前端配置
- **小程序配置**: `miniprogram1/config/secrets.js`

## 🔑 管理的密钥类型

### 1. 微信小程序
- 主小程序 AppID: `wx13d6e0dee303467f`
- 主小程序 AppSecret: `a291bdda3c7cdc95679409b10e7b6f05`
- 伴学星小程序 AppID: `wx643c1ff21fe15e4f`

### 2. 微信公众号
- 公众号 AppID: `wx9fc15fa347658035`
- 公众号 AppSecret: `0f367256f65321039ad6e3416978992f`
- 模板消息公众号配置

### 3. 腾讯云COS
- SecretId: `AKIDhGbllaEhx54qRpdIjy0oOo6FF3f3D9rM`
- SecretKey: `KTQCl6UgQzZ1GRSHxRhnhOs8B2g6II9p`

### 4. 加密密钥
- JWT密钥: 环境区分
- SSO加密密钥: 环境区分
- 通用加密密钥: 环境区分

### 5. 数据库
- 开发环境: localhost:8889, root/root
- 生产环境: localhost:3306, root/8808243

## 🚀 使用方法

### 后端使用
```php
use app\util\SecretUtil;

// 获取微信小程序配置
$miniConfig = SecretUtil::getWechatMiniprogram('main');
$appid = $miniConfig['appid'];
$secret = $miniConfig['secret'];

// 获取腾讯云COS配置
$cosConfig = SecretUtil::getTencentCos();
$secretId = $cosConfig['secret_id'];

// 获取加密密钥
$jwtKey = SecretUtil::getEncryptionKey('jwt_key');

// 获取API配置
$baseUrl = SecretUtil::getBaseUrl();
```

### 前端使用
```javascript
const secretsConfig = require('./config/secrets.js');

// 获取API地址
const apiUrl = secretsConfig.api.wangz;

// 获取加密密钥
const secretKey = secretsConfig.encryption.secretKey;

// 检查环境
const isLocal = secretsConfig.environment.isLocal;
```

## 🔒 安全建议

### 1. 生产环境部署
- 确保 `config/secrets.php` 文件权限设置为 600
- 不要将包含真实密钥的配置文件提交到代码仓库
- 考虑使用环境变量或密钥管理服务

### 2. 开发环境
- 开发环境可以使用测试密钥
- 定期更新密钥
- 不要在日志中输出密钥信息

### 3. 代码审查
- 确保新代码使用 SecretUtil 获取密钥
- 禁止硬编码密钥
- 定期检查是否有密钥泄露

## 📝 迁移清单

### 已完成
- ✅ 创建统一配置文件
- ✅ 创建密钥管理工具类
- ✅ 修改 COS 配置
- ✅ 修改微信配置
- ✅ 修改 JWT 工具类
- ✅ 修改小程序配置
- ✅ 修改 User.php 控制器
- ✅ 修改 Messag5e.php 控制器
- ✅ 修改 WechatTemplate.php 控制器
- ✅ 修改 TemplateMessage.php 控制器
- ✅ 修改 Sen.php 控制器
- ✅ 修改 Use2r.php 控制器
- ✅ 修改 Message.php 控制器
- ✅ 修改 Comment.php 控制器
- ✅ 修改 WechatArticle.php 控制器
- ✅ 修改 app.php 配置文件
- ✅ 清理多余代码和文件

### 待完成
- ⏳ 添加密钥轮换机制
- ⏳ 添加密钥加密存储
- ⏳ 生产环境密钥更新



## 🛠️ 部署步骤

### 生产环境部署
1. 修改 `config/secrets.php` 中的生产环境密钥
2. 确保文件权限正确
3. 测试所有功能是否正常
4. 监控日志确保无密钥泄露

### 开发环境设置
1. 使用开发环境密钥
2. 确保本地数据库配置正确
3. 测试环境自动切换功能

## 📞 联系方式

如有密钥管理相关问题，请联系项目维护者。
