# 后端开发规范

## 📋 概述

本文档规定了大学城树洞项目后端开发的代码规范、架构设计和最佳实践。

## 🏗️ 架构设计

### 三层架构
```
Controller (控制器层) → Service (服务层) → Model (模型层)
```

#### Controller (控制器层)
- **职责**：接收请求、验证参数、调用Service、返回响应
- **原则**：保持轻薄，不包含业务逻辑
- **示例**：
```php
public function createUser(Request $request): Json
{
    // 1. 参数验证
    $validate = new UserValidate();
    if (!$validate->check($request->param())) {
        return json(['code' => 400, 'msg' => $validate->getError()]);
    }
    
    // 2. 调用Service
    $userService = new UserService();
    $result = $userService->createUser($request->param());
    
    // 3. 返回响应
    return json($result);
}
```

#### Service (服务层)
- **职责**：处理核心业务逻辑
- **原则**：封装复杂业务流程，可复用
- **示例**：
```php
public function createUser(array $data): array
{
    try {
        // 业务逻辑处理
        $userData = $this->processUserData($data);
        
        // 调用Model
        $userId = UserModel::create($userData);
        
        // 记录日志
        LogUtil::business('User', 'create', ['user_id' => $userId]);
        
        return ['code' => 200, 'data' => ['user_id' => $userId]];
    } catch (\Exception $e) {
        LogUtil::error('创建用户失败', ['data' => $data], $e);
        return ['code' => 500, 'msg' => '创建用户失败'];
    }
}
```

#### Model (模型层)
- **职责**：与数据库直接交互
- **原则**：只负责数据的增删改查
- **示例**：
```php
class UserModel extends Model
{
    protected $table = 'user';
    
    public static function createUser(array $data): int
    {
        return self::insertGetId($data);
    }
    
    public static function getUserById(int $userId): ?array
    {
        return self::where('id', $userId)->find();
    }
}
```

## 🔧 开发规范

### 1. 请求处理规范

#### 统一使用POST请求
```php
// ✅ 正确：所有接口使用POST
public function getUserInfo(Request $request): Json
{
    $userId = $request->param('user_id');
    // ...
}

// ❌ 错误：不使用GET请求
public function getUserInfo(int $userId): Json
{
    // ...
}
```

#### Token验证
```php
// 从请求头获取Token
$token = $request->header('Authorization') ?: $request->header('token');
if (!$token) {
    return json(['code' => 401, 'msg' => '缺少认证Token']);
}

// 验证Token
$payload = JwtUtil::decode($token);
if (!$payload) {
    return json(['code' => 401, 'msg' => 'Token无效']);
}
```

### 2. 数据库操作规范

#### 使用查询构造器
```php
// ✅ 推荐：使用查询构造器
$users = Db::table('user')
    ->where('status', 1)
    ->where('create_time', '>', time() - 86400)
    ->select();

// ✅ 推荐：使用ORM模型
$users = UserModel::where('status', 1)
    ->where('create_time', '>', time() - 86400)
    ->select();

// ❌ 避免：直接写SQL（除非必要）
$sql = "SELECT * FROM user WHERE status = 1";
$users = Db::query($sql);
```

#### 事务处理
```php
Db::transaction(function () use ($data) {
    // 创建用户
    $userId = UserModel::create($data['user']);
    
    // 创建用户资料
    ProfileModel::create([
        'user_id' => $userId,
        'nickname' => $data['nickname']
    ]);
    
    return $userId;
});
```

### 3. 错误处理规范

#### 统一异常处理
```php
try {
    // 业务逻辑
    $result = $this->processData($data);
    return json(['code' => 200, 'data' => $result]);
} catch (ValidationException $e) {
    // 验证异常
    LogUtil::warning('参数验证失败', ['error' => $e->getMessage()]);
    return json(['code' => 400, 'msg' => $e->getMessage()]);
} catch (BusinessException $e) {
    // 业务异常
    LogUtil::warning('业务处理失败', ['error' => $e->getMessage()]);
    return json(['code' => 500, 'msg' => $e->getMessage()]);
} catch (\Exception $e) {
    // 系统异常
    LogUtil::error('系统异常', ['error' => $e->getMessage()], $e);
    return json(['code' => 500, 'msg' => '系统异常，请稍后重试']);
}
```

### 4. 日志记录规范

#### 使用LogUtil记录日志
```php
// 业务操作日志
LogUtil::business('User', 'login', ['user_id' => $userId]);

// API调用日志
LogUtil::api('POST', '/user/create', $params, $response, $duration);

// 数据库操作日志
LogUtil::database('INSERT', 'user', $data, $duration, $sql);

// 错误日志
LogUtil::error('用户创建失败', ['data' => $data], $exception);

// 性能监控
$startTime = microtime(true);
// ... 业务逻辑
LogUtil::performance('createUser', $startTime, ['user_id' => $userId]);
```

### 5. 缓存使用规范

#### Redis缓存
```php
// 设置缓存
cache('user_info_' . $userId, $userInfo, 3600);

// 获取缓存
$userInfo = cache('user_info_' . $userId);
if (!$userInfo) {
    $userInfo = UserModel::getUserById($userId);
    cache('user_info_' . $userId, $userInfo, 3600);
}

// 删除缓存
cache('user_info_' . $userId, null);
```

## 📝 代码规范

### 1. 命名规范

#### 类名
```php
// ✅ 大驼峰命名
class UserController extends BaseController {}
class UserService {}
class UserModel extends Model {}
```

#### 方法名
```php
// ✅ 小驼峰命名
public function getUserInfo() {}
public function createUser() {}
public function updateUserStatus() {}
```

#### 变量名
```php
// ✅ 小驼峰命名
$userId = 123;
$userInfo = [];
$isActive = true;
```

### 2. 注释规范

#### 类注释
```php
/**
 * 用户控制器
 * 处理用户相关的HTTP请求
 */
class UserController extends BaseController
{
    // ...
}
```

#### 方法注释
```php
/**
 * 创建用户
 * @param Request $request HTTP请求对象
 * @return Json JSON响应
 */
public function createUser(Request $request): Json
{
    // ...
}
```

### 3. 返回格式规范

#### 统一响应格式
```php
// 成功响应
return json([
    'code' => 200,
    'msg' => '操作成功',
    'data' => $result
]);

// 错误响应
return json([
    'code' => 400,
    'msg' => '参数错误',
    'data' => null
]);

// 分页响应
return json([
    'code' => 200,
    'msg' => '获取成功',
    'data' => [
        'list' => $list,
        'total' => $total,
        'page' => $page,
        'limit' => $limit
    ]
]);
```

## 🔐 安全规范

### 1. 参数验证
```php
// 使用验证器
$validate = new UserValidate();
if (!$validate->scene('create')->check($request->param())) {
    return json(['code' => 400, 'msg' => $validate->getError()]);
}

// 手动验证
$userId = $request->param('user_id', 0, 'intval');
if ($userId <= 0) {
    return json(['code' => 400, 'msg' => '用户ID无效']);
}
```

### 2. SQL注入防护
```php
// ✅ 使用参数绑定
Db::table('user')->where('id', $userId)->find();

// ❌ 避免字符串拼接
$sql = "SELECT * FROM user WHERE id = " . $userId;
```

### 3. XSS防护
```php
// 输出时转义
$content = htmlspecialchars($userInput, ENT_QUOTES, 'UTF-8');
```

## 🚀 性能优化

### 1. 数据库优化
- 合理使用索引
- 避免N+1查询
- 使用批量操作
- 适当使用缓存

### 2. 缓存策略
- 热点数据缓存
- 查询结果缓存
- 页面片段缓存
- 合理设置过期时间

### 3. 代码优化
- 减少不必要的数据库查询
- 使用合适的数据结构
- 避免在循环中进行数据库操作

## 🧪 测试规范

### 1. 单元测试
```php
public function testCreateUser()
{
    $data = [
        'username' => 'test_user',
        'email' => '<EMAIL>'
    ];
    
    $service = new UserService();
    $result = $service->createUser($data);
    
    $this->assertEquals(200, $result['code']);
    $this->assertArrayHasKey('user_id', $result['data']);
}
```

### 2. 接口测试
- 使用Postman或类似工具
- 测试正常流程和异常情况
- 验证返回数据格式

## 📚 相关文档

- [前端开发规范](./FRONTEND_DEVELOPMENT.md)
- [数据库设计](./DATABASE_DESIGN.md)
- [日志系统说明](./LOG_SYSTEM.md)
- [环境配置说明](./ENVIRONMENT_CONFIG.md)

---

**重要**：所有开发人员都应遵循以上规范，确保代码质量和项目的可维护性。
