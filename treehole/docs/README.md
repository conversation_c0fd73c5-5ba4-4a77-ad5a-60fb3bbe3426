# 🌳 大学城树洞微信小程序

一个基于 ThinkPHP 8 + 微信小程序的大学生社交平台，提供树洞、校园交易、跑腿服务等功能。

## ✨ 主要功能

### 🎯 核心功能
- **树洞系统**: 匿名发布心情、想法，支持图片上传
- **校园交易**: 二手物品买卖，瀑布流展示
- **跑腿服务**: 校园内代购、代取等服务
- **用户系统**: 微信授权登录，学校认证

### 🔧 技术特性
- **前端**: 微信小程序原生开发
- **后端**: ThinkPHP 8 框架
- **数据库**: MySQL 8.0
- **存储**: 腾讯云COS对象存储
- **缓存**: Redis缓存系统
- **架构**: MVC三层架构，Service层业务逻辑

## 🚀 快速开始

### 环境要求
- PHP 8.0+
- MySQL 5.7+
- Redis 6.0+
- Composer
- 微信开发者工具

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/treehole.git
cd treehole
```

2. **安装依赖**
```bash
composer install
```

3. **配置环境**
```bash
# 复制配置模板
cp config/secrets.php.example config/secrets.php

# 编辑配置文件
nano config/secrets.php
```

4. **数据库设置**
```sql
CREATE DATABASE treehole CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
mysql -u root -p treehole < mysql/database.sql
```

5. **设置权限**
```bash
chmod +x think
chmod -R 755 runtime/
```

详细部署说明请参考 [DEPLOYMENT.md](DEPLOYMENT.md)

## 🔒 安全特性

### 配置文件安全
- `config/secrets.php` - 包含所有敏感信息，**绝对不要提交到代码仓库**
- 使用 `secrets.php.example` 作为配置模板
- 支持开发/生产环境自动切换

### 数据安全
- JWT Token认证
- 参数验证和过滤
- SQL注入防护
- 图片安全验证

## 📞 联系方式

- 问题反馈: [Issues](https://github.com/your-username/treehole/issues)
- 部署文档: [DEPLOYMENT.md](DEPLOYMENT.md)

## 📄 开源协议

本项目采用 MIT 协议 - 查看 [LICENSE.txt](LICENSE.txt) 文件了解详情
