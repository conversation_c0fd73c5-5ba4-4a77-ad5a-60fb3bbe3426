# 环境配置说明

## 📋 概述

本项目支持本地开发环境和生产环境的自动切换，通过统一的配置文件管理不同环境的参数。

## 🔧 配置文件

### 主配置文件：`config/secrets.php`
这是项目的核心配置文件，包含所有环境相关的配置。

```php
<?php
use app\util\SecretUtil;

// 自动检测环境
$isLocal = SecretUtil::isLocal();

return [
    // 图片域名配置
    'images' => [
        'defaultFaceUrl' => $isLocal ? 
            'http://localhost:80/images/weixiao.png' : 
            'https://www.bjgaoxiaoshequ.store/images/weixiao.png'
    ],

    // COS配置
    'cos' => [
        'region' => 'ap-beijing',
        'secret_id' => 'your_secret_id',
        'secret_key' => 'your_secret_key',
    ],

    // 数据库配置
    'database' => [
        'hostname' => $isLocal ? 'localhost' : 'localhost',
        'database' => 'treehole',
        'username' => $isLocal ? 'root' : 'root',
        'password' => $isLocal ? 'root' : 'your_prod_password',
        'hostport' => $isLocal ? '8889' : '3306',
    ],

    // API域名配置
    'api' => [
        'base_url' => $isLocal ? 'http://localhost:80/index.php' : 'https://www.bjgaoxiaoshequ.store',
    ],

    // 日志查看器配置
    'log_viewer' => [
        'password' => $isLocal ? 'dev_log_2024' : 'treehole_log_admin_2024',
        'enable_auth' => !$isLocal,
    ],
];
```

## 🌍 环境类型

### 本地开发环境
- **识别方式**：域名包含 `localhost`、`127.0.0.1` 或 `192.168.`
- **特点**：
  - 使用本地数据库
  - 图片使用本地路径
  - 日志查看器无需密码
  - 调试模式开启

### 生产环境
- **识别方式**：非本地域名
- **特点**：
  - 使用生产数据库
  - 图片使用COS存储
  - 日志查看器需要密码
  - 优化模式运行

## 🎯 前端环境切换

### app.js 中的环境配置
```javascript
// 环境配置 - 注释掉不用的环境
// const baseUrl = 'http://localhost:80/index.php';  // 开发环境
const baseUrl = 'https://www.bjgaoxiaoshequ.store';   // 生产环境

// 根据域名自动配置其他参数
const isLocalEnvironment = baseUrl.includes('localhost');
const defaultFaceUrl = isLocalEnvironment ? 
  'http://localhost:80/images/weixiao.png' : 
  'https://www.bjgaoxiaoshequ.store/images/weixiao.png';
```

### 切换方法
1. **切换到开发环境**：
   ```javascript
   const baseUrl = 'http://localhost:80/index.php';  // 开发环境
   // const baseUrl = 'https://www.bjgaoxiaoshequ.store';   // 生产环境
   ```

2. **切换到生产环境**：
   ```javascript
   // const baseUrl = 'http://localhost:80/index.php';  // 开发环境
   const baseUrl = 'https://www.bjgaoxiaoshequ.store';   // 生产环境
   ```

## 🔐 密码管理

### 日志查看器密码
- **本地环境**：`dev_log_2024`（实际不需要输入）
- **生产环境**：`treehole_log_admin_2024`

### 修改密码
编辑 `config/secrets.php`：
```php
'log_viewer' => [
    'password' => $isLocal ? 'new_dev_password' : 'new_prod_password',
    'enable_auth' => !$isLocal,
],
```

## 🗄️ 数据库配置

### 本地环境
```php
'database' => [
    'hostname' => 'localhost',
    'database' => 'treehole',
    'username' => 'root',
    'password' => 'root',
    'hostport' => '8889', // MAMP端口
],
```

### 生产环境
```php
'database' => [
    'hostname' => 'localhost',
    'database' => 'treehole',
    'username' => 'root',
    'password' => 'your_production_password',
    'hostport' => '3306',
],
```

## 📁 COS存储配置

### 配置说明
```php
'cos' => [
    'region' => 'ap-beijing',
    'secret_id' => 'your_secret_id',
    'secret_key' => 'your_secret_key',
    'public_bucket' => $isLocal ? 'treeholepublic-1320255796' : 'treeholepublic-1320255796',
    'private_bucket' => $isLocal ? 'treeholeprivate-1320255796' : 'treeholeprivate-1320255796',
    'public_domain' => $isLocal ? 
        'https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com' : 
        'https://public.bjgaoxiaoshequ.store',
    'private_domain' => $isLocal ? 
        'https://treeholeprivate-1320255796.cos.ap-beijing.myqcloud.com' : 
        'https://private.bjgaoxiaoshequ.store',
],
```

## 🚀 部署流程

### 1. 本地开发
```bash
# 确保使用开发环境配置
# 前端：修改 app.js 使用本地API
# 后端：secrets.php 自动检测本地环境
```

### 2. 生产部署
```bash
# 前端：修改 app.js 使用生产API
# 后端：secrets.php 自动检测生产环境
# 上传代码到服务器
```

## ⚠️ 注意事项

### 安全提醒
1. **不要提交敏感信息**：`secrets.php` 包含敏感信息，不应提交到版本控制
2. **使用示例文件**：提供 `secrets.php.example` 作为模板
3. **定期更换密码**：定期更换数据库密码和API密钥

### 开发建议
1. **环境一致性**：确保开发和生产环境的PHP版本一致
2. **配置检查**：部署前检查所有配置是否正确
3. **日志监控**：部署后检查日志确保系统正常运行

## 🔍 故障排查

### 常见问题
1. **API调用失败**：检查前端 `app.js` 中的 `baseUrl` 配置
2. **图片显示异常**：检查COS配置和域名设置
3. **数据库连接失败**：检查数据库配置和网络连接
4. **日志查看器无法访问**：检查密码配置和权限设置

### 调试方法
```bash
# 查看环境检测结果
tail -f runtime/log/info.log | grep "Environment"

# 检查配置加载
tail -f runtime/log/debug.log | grep "Config"
```

---

**重要**：修改配置后需要重启服务才能生效。
