# 项目结构说明

## 📋 概述

本文档详细说明了大学城树洞微信小程序项目的目录结构和文件组织方式。

## 🗂️ 项目根目录结构

```
jingyuxiaoyuan/
├── miniprogram1/          # 前端微信小程序
├── treehole/             # 后端ThinkPHP项目
└── docs/                 # 项目文档（本文档所在目录）
```

## 📱 前端结构 (miniprogram1/)

```
miniprogram1/
├── app.js                # 小程序入口文件
├── app.json             # 小程序配置文件
├── app.wxss             # 全局样式文件
├── project.config.json  # 项目配置文件
├── sitemap.json         # 站点地图配置
├── config/              # 配置文件目录
│   └── secrets.js       # 前端配置（已废弃，现在使用app.js中的配置）
├── pages/               # 页面目录
│   ├── index/           # 首页
│   ├── login/           # 登录页
│   ├── profile/         # 个人中心
│   └── ...              # 其他页面
├── components/          # 自定义组件
├── utils/               # 工具类
│   ├── request.js       # 网络请求封装
│   ├── tokenManager.js  # Token管理
│   ├── loginManager.js  # 登录管理
│   ├── debugUtil.js     # 调试工具
│   └── ...              # 其他工具
└── images/              # 静态图片资源
```

### 前端重要文件说明

#### app.js
- 小程序入口文件
- 包含环境配置（开发/生产环境切换）
- 全局数据管理

#### utils/request.js
- 统一的网络请求封装
- 自动处理Token携带
- 统一错误处理

#### utils/tokenManager.js
- JWT Token管理
- 自动刷新Token
- 本地存储管理

## 🖥️ 后端结构 (treehole/)

```
treehole/
├── app/                 # 应用目录
│   ├── controller/      # 控制器
│   ├── model/          # 模型
│   ├── service/        # 服务层
│   ├── util/           # 工具类
│   ├── middleware/     # 中间件
│   ├── command/        # 命令行工具
│   └── validate/       # 验证器
├── config/             # 配置文件
├── public/             # 公共文件（Web入口）
├── runtime/            # 运行时文件
│   └── log/           # 日志文件
├── vendor/             # Composer依赖
├── mysql/              # 数据库建表脚本
├── docs/               # 项目文档
├── view_logs.sh        # 日志查看脚本
└── composer.json       # Composer配置
```

### 后端目录详细说明

#### app/controller/ - 控制器层
```
controller/
├── BaseController.php      # 基础控制器
├── User.php               # 用户相关接口
├── Message.php            # 消息相关接口
├── SimpleDraftGenerator.php # 草稿生成器
├── LogViewer.php          # 日志查看器
└── ...                    # 其他控制器
```

#### app/service/ - 服务层
```
service/
├── UserService.php        # 用户业务逻辑
├── MessageService.php     # 消息业务逻辑
├── ImageService.php       # 图片处理服务
└── ...                    # 其他服务
```

#### app/util/ - 工具类
```
util/
├── SecretUtil.php         # 配置工具类
├── JwtUtil.php           # JWT工具类
├── CosUtil.php           # 腾讯云COS工具类
├── LogUtil.php           # 日志工具类
└── ...                   # 其他工具类
```

#### config/ - 配置文件
```
config/
├── app.php               # 应用配置
├── database.php          # 数据库配置
├── log.php              # 日志配置
├── log_viewer.php       # 日志查看器配置
├── secrets.php          # 敏感信息配置
├── secrets.php.example  # 配置模板
├── cos.php              # COS配置
└── ...                  # 其他配置
```

#### public/ - 公共文件
```
public/
├── index.php            # 入口文件
├── log_viewer.html      # 日志查看器Web界面
├── images/              # 静态图片
└── uploads/             # 上传文件目录
```

#### runtime/ - 运行时文件
```
runtime/
├── log/                 # 日志文件目录
│   ├── error.log       # 错误日志
│   ├── warning.log     # 警告日志
│   ├── info.log        # 信息日志
│   ├── api.log         # API日志
│   ├── business.log    # 业务日志
│   └── debug.log       # 调试日志
├── cache/              # 缓存文件
└── temp/               # 临时文件
```

#### mysql/ - 数据库脚本
```
mysql/
├── create_tables.sql    # 建表脚本
├── init_data.sql       # 初始数据
└── update_*.sql        # 数据库更新脚本
```

## 📄 重要文件说明

### 配置文件

#### treehole/config/secrets.php
- **用途**：存储所有敏感配置信息
- **内容**：数据库密码、API密钥、COS配置等
- **注意**：不应提交到版本控制系统

#### miniprogram1/app.js
- **用途**：前端环境配置和全局数据
- **内容**：API地址、环境切换配置
- **注意**：需要手动切换开发/生产环境

### 工具脚本

#### treehole/view_logs.sh
- **用途**：交互式日志查看工具
- **功能**：彩色显示、搜索、统计等
- **使用**：`./view_logs.sh`

#### treehole/public/log_viewer.html
- **用途**：Web版日志查看器
- **功能**：文件浏览、内容查看、搜索等
- **访问**：浏览器访问对应URL

## 🔄 数据流向

### 请求流程
```
微信小程序 → utils/request.js → 后端Controller → Service → Model → 数据库
                                      ↓
                                   LogUtil → 日志文件
```

### 文件上传流程
```
微信小程序 → 选择图片 → 后端接口 → CosUtil → 腾讯云COS
                                    ↓
                                 返回URL → 存储到数据库
```

## 📝 命名规范

### 文件命名
- **控制器**：大驼峰命名，如 `UserController.php`
- **模型**：大驼峰命名，如 `UserModel.php`
- **工具类**：大驼峰命名 + Util后缀，如 `JwtUtil.php`
- **配置文件**：小写+下划线，如 `log_viewer.php`

### 目录命名
- **小写+下划线**：如 `app/controller/`
- **小驼峰**：如 `miniprogram1/utils/`

### 变量命名
- **PHP**：小驼峰命名，如 `$userName`
- **JavaScript**：小驼峰命名，如 `userName`
- **数据库字段**：小写+下划线，如 `user_name`

## 🔧 开发工具

### 推荐的IDE配置
- **后端**：PhpStorm 或 VS Code
- **前端**：微信开发者工具
- **数据库**：phpMyAdmin 或 Navicat

### 调试工具
- **日志查看**：`./view_logs.sh` 或 Web界面
- **API测试**：Postman 或 Insomnia
- **数据库管理**：phpMyAdmin

---

**注意**：项目结构可能会随着开发进度而调整，请及时更新文档。
